import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Validation script to verify the new MessageList and AttackList schema
 * This script tests the new database structure and ensures everything works correctly
 */
async function validateMigration() {
  console.log('🔍 Starting migration validation...');
  
  try {
    // Test 1: Verify MessageList table structure
    console.log('\n📋 Testing MessageList table...');
    const messageListCount = await prisma.messageList.count();
    console.log(`✅ MessageList table accessible - Current count: ${messageListCount}`);
    
    // Test 2: Verify AttackList table structure  
    console.log('\n⚔️ Testing AttackList table...');
    const attackListCount = await prisma.attackList.count();
    console.log(`✅ AttackList table accessible - Current count: ${attackListCount}`);
    
    // Test 3: Verify enum values work correctly
    console.log('\n🔢 Testing enum values...');
    
    // Find a test contact to use for validation
    const testContact = await prisma.instagramContact.findFirst({
      select: {
        id: true,
        organizationId: true,
        instagramNickname: true,
        priority: true
      }
    });
    
    if (testContact) {
      console.log(`📞 Using test contact: ${testContact.instagramNickname}`);
      
      // Test MessageList creation with all enum values
      const testMessageList = await prisma.messageList.create({
        data: {
          organizationId: testContact.organizationId,
          contactId: testContact.id,
          nickname: testContact.instagramNickname,
          priority: testContact.priority || 3,
          messageType: 'SYSTEM',
          scheduledTime: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
          messageContent: 'Test system message for validation',
          status: 'PENDING',
          handlerType: 'SYSTEM',
          sequenceNumber: 1
        }
      });
      console.log(`✅ MessageList entry created: ${testMessageList.id}`);
      
      // Test AttackList creation with all enum values
      const testAttackList = await prisma.attackList.create({
        data: {
          organizationId: testContact.organizationId,
          contactId: testContact.id,
          messageListId: testMessageList.id,
          nickname: testContact.instagramNickname,
          priority: testContact.priority || 3,
          messageContent: 'Test first message for validation',
          messageType: 'FIRST_MESSAGE',
          sequenceNumber: 1,
          status: 'READY'
        }
      });
      console.log(`✅ AttackList entry created: ${testAttackList.id}`);
      
      // Test 4: Verify relationships work correctly
      console.log('\n🔗 Testing relationships...');
      const messageListWithRelations = await prisma.messageList.findUnique({
        where: { id: testMessageList.id },
        include: {
          Organization: { select: { name: true } },
          InstagramContact: { select: { instagramNickname: true } },
          AttackList: true
        }
      });
      
      if (messageListWithRelations) {
        console.log(`✅ MessageList relationships work:`);
        console.log(`   - Organization: ${messageListWithRelations.Organization.name}`);
        console.log(`   - Contact: ${messageListWithRelations.InstagramContact.instagramNickname}`);
        console.log(`   - Related AttackList entries: ${messageListWithRelations.AttackList.length}`);
      }
      
      // Test 5: Verify indexes work (query performance)
      console.log('\n📊 Testing index performance...');
      const start = Date.now();
      
      await prisma.messageList.findMany({
        where: {
          organizationId: testContact.organizationId,
          status: 'PENDING'
        },
        orderBy: {
          scheduledTime: 'asc'
        },
        take: 10
      });
      
      const queryTime = Date.now() - start;
      console.log(`✅ MessageList query completed in ${queryTime}ms`);
      
      await prisma.attackList.findMany({
        where: {
          organizationId: testContact.organizationId,
          status: 'READY'
        },
        orderBy: [
          { priority: 'desc' },
          { addedAt: 'asc' }
        ],
        take: 10
      });
      
      console.log(`✅ AttackList query completed successfully`);
      
      // Cleanup test data
      console.log('\n🧹 Cleaning up test data...');
      await prisma.attackList.delete({ where: { id: testAttackList.id } });
      await prisma.messageList.delete({ where: { id: testMessageList.id } });
      console.log('✅ Test data cleaned up');
      
    } else {
      console.log('⚠️ No test contact found - skipping relationship tests');
    }
    
    // Test 6: Verify legacy fields/tables are gone
    console.log('\n🗑️ Verifying legacy cleanup...');
    
    try {
      // This should fail since InstagramFollowUp table no longer exists
      await prisma.$queryRaw`SELECT COUNT(*) FROM "InstagramFollowUp"`;
      console.log('❌ ERROR: InstagramFollowUp table still exists!');
    } catch (error) {
      console.log('✅ InstagramFollowUp table successfully removed');
    }
    
    // Check that legacy fields are gone from InstagramContact
    const contactFields = await prisma.$queryRaw`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'InstagramContact' 
      AND column_name LIKE 'followUp%'
    `;
    
    if (Array.isArray(contactFields) && contactFields.length === 0) {
      console.log('✅ Legacy followUp fields successfully removed from InstagramContact');
    } else {
      console.log('❌ ERROR: Legacy followUp fields still exist:', contactFields);
    }
    
    console.log('\n🎉 Migration validation completed successfully!');
    console.log('\n📋 New Schema Summary:');
    console.log('   - MessageList: For system messages within 24h window');
    console.log('   - AttackList: For chrome extension messages (first + follow-ups > 24h)');
    console.log('   - All legacy follow-up fields and tables removed');
    console.log('   - Proper indexes and relationships established');
    console.log('   - Clean separation of concerns achieved');
    
  } catch (error) {
    console.error('❌ Migration validation failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Export for programmatic use
export { validateMigration };

// Run validation if called directly
const isMainModule = import.meta.url === `file://${process.argv[1]}`;
if (isMainModule) {
  validateMigration().catch((error) => {
    console.error('Validation failed:', error);
    process.exit(1);
  });
}