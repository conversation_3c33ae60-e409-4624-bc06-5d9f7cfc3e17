import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();

async function checkMessages() {
  try {
    const messages = await prisma.instagramMessage.findMany({
      where: {
        OR: [
          { content: { contains: 'sł<PERSON><PERSON>, widzę że wcześniej rozmawialiśmy o chęci poznawania większej liczby dziewczyn' } },
          { content: { contains: 'co u Ciebie z tym tematem' } }
        ]
      },
      select: {
        messageId: true,
        content: true,
        messageType: true,
        isEcho: true,
        isFromUser: true,
        timestamp: true,
        createdAt: true
      },
      orderBy: {
        timestamp: 'desc'
      }
    });

    console.log('=== MESSAGE ANALYSIS ===');
    console.log(`Found ${messages.length} messages matching the criteria:`);
    
    messages.forEach((msg, index) => {
      console.log(`\n--- Message ${index + 1} ---`);
      console.log(`Message ID: ${msg.messageId}`);
      console.log(`Content: ${msg.content.substring(0, 60)}...`);
      console.log(`Type: ${msg.messageType}`);
      console.log(`Is Echo: ${msg.isEcho}`);
      console.log(`Is From User: ${msg.isFromUser}`);
      console.log(`Timestamp: ${msg.timestamp}`);
      console.log(`Created At: ${msg.createdAt}`);
    });

    // Check for duplicates by messageId
    const messageIds = messages.map(m => m.messageId);
    const duplicateIds = messageIds.filter((id, index) => messageIds.indexOf(id) !== index);
    
    if (duplicateIds.length > 0) {
      console.log(`\n🚨 DUPLICATES FOUND: ${duplicateIds.length} duplicate message IDs`);
      duplicateIds.forEach(id => console.log(`Duplicate ID: ${id}`));
    } else {
      console.log('\n✅ NO DUPLICATES: All message IDs are unique');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkMessages();