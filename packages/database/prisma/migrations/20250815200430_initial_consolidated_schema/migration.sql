-- CreateEnum
CREATE TYPE "ActionType" AS ENUM ('create', 'update', 'delete');

-- CreateEnum
CREATE TYPE "ActorType" AS ENUM ('system', 'member', 'api');

-- CreateEnum
CREATE TYPE "FeedbackCategory" AS ENUM ('suggestion', 'problem', 'question');

-- CreateEnum
CREATE TYPE "InvitationStatus" AS ENUM ('pending', 'accepted', 'revoked');

-- CreateEnum
CREATE TYPE "Role" AS ENUM ('member', 'admin');

-- CreateEnum
CREATE TYPE "WebhookTrigger" AS ENUM ('instagramMessageReceived');

-- CreateEnum
CREATE TYPE "FollowUpStatus" AS ENUM ('pending', 'sent', 'failed', 'external');

-- CreateEnum
CREATE TYPE "InstagramBotStatus" AS ENUM ('active', 'paused', 'disabled');

-- CreateEnum
CREATE TYPE "InstagramContactStage" AS ENUM ('new', 'initial', 'engaged', 'qualified', 'formsent', 'disqualified', 'converted', 'blocked', 'suspicious');

-- CreateEnum
CREATE TYPE "MessageType" AS ENUM ('incoming', 'outgoing');

-- CreateEnum
CREATE TYPE "QueueStatus" AS ENUM ('pending', 'processing', 'completed', 'failed', 'retrying');

-- CreateEnum
CREATE TYPE "InstagramFollowerPriority" AS ENUM ('low', 'normal', 'high', 'urgent');

-- CreateEnum
CREATE TYPE "InstagramFollowerStatus" AS ENUM ('pending', 'contacted', 'responded', 'engaged', 'converted', 'ignored', 'blocked');

-- CreateEnum
CREATE TYPE "ConversationSource" AS ENUM ('extension', 'api');

-- CreateEnum
CREATE TYPE "FollowerProcessingStatus" AS ENUM ('pending', 'processing', 'completed', 'failed', 'retrying');

-- CreateEnum
CREATE TYPE "FollowerProcessingType" AS ENUM ('batch', 'conversation');

-- CreateEnum
CREATE TYPE "InstagramMessageType" AS ENUM ('RECEIVED', 'AI_SENT', 'MANUAL_SENT');

-- CreateTable
CREATE TABLE "Account" (
    "id" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "type" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "providerAccountId" TEXT NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" TEXT,
    "scope" TEXT,
    "id_token" TEXT,
    "session_state" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_Account" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ApiKey" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "description" VARCHAR(70) NOT NULL,
    "hashedKey" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3),
    "lastUsedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_ApiKey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AuthenticatorApp" (
    "id" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "accountName" VARCHAR(255) NOT NULL,
    "issuer" VARCHAR(255) NOT NULL,
    "secret" VARCHAR(255) NOT NULL,
    "recoveryCodes" VARCHAR(1024) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_AuthenticatorApp" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ChangeEmailRequest" (
    "id" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "email" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL,
    "valid" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_ChangeEmailRequest" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Feedback" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "userId" UUID,
    "category" "FeedbackCategory" NOT NULL DEFAULT 'suggestion',
    "message" VARCHAR(4000) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_Feedback" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Invitation" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "token" UUID NOT NULL,
    "email" VARCHAR(255) NOT NULL,
    "role" "Role" NOT NULL DEFAULT 'member',
    "status" "InvitationStatus" NOT NULL DEFAULT 'pending',
    "lastSentAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_Invitation" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Membership" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "role" "Role" NOT NULL DEFAULT 'member',
    "isOwner" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PK_Membership" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Notification" (
    "id" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "subject" VARCHAR(128),
    "content" VARCHAR(8000) NOT NULL,
    "link" VARCHAR(2000),
    "seenAt" TIMESTAMP(3),
    "dismissed" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "organizationId" UUID,

    CONSTRAINT "PK_Notification" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Organization" (
    "id" UUID NOT NULL,
    "stripeCustomerId" VARCHAR(255),
    "name" VARCHAR(255) NOT NULL,
    "tier" VARCHAR(255) NOT NULL DEFAULT 'free',
    "logo" VARCHAR(2048),
    "slug" VARCHAR(255) NOT NULL,

    CONSTRAINT "PK_Organization" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OrganizationLogo" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "data" BYTEA,
    "contentType" VARCHAR(255),
    "hash" VARCHAR(64),

    CONSTRAINT "PK_OrganizationLogo" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ResetPasswordRequest" (
    "id" UUID NOT NULL,
    "email" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_ResetPasswordRequest" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Session" (
    "id" UUID NOT NULL,
    "sessionToken" TEXT NOT NULL,
    "userId" UUID NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_Session" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "User" (
    "id" UUID NOT NULL,
    "image" VARCHAR(2048),
    "name" VARCHAR(64) NOT NULL,
    "email" TEXT,
    "emailVerified" TIMESTAMP(3),
    "password" VARCHAR(60),
    "lastLogin" TIMESTAMP(3),
    "phone" VARCHAR(32),
    "locale" VARCHAR(8) NOT NULL DEFAULT 'en-US',
    "completedOnboarding" BOOLEAN NOT NULL DEFAULT false,
    "enabledInboxNotifications" BOOLEAN NOT NULL DEFAULT false,
    "enabledWeeklySummary" BOOLEAN NOT NULL DEFAULT false,
    "enabledNewsletter" BOOLEAN NOT NULL DEFAULT false,
    "enabledProductUpdates" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_User" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserImage" (
    "id" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "data" BYTEA,
    "contentType" VARCHAR(255),
    "hash" VARCHAR(64),

    CONSTRAINT "PK_UserImage" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VerificationToken" (
    "identifier" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL
);

-- CreateTable
CREATE TABLE "Webhook" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "url" VARCHAR(2000) NOT NULL,
    "triggers" "WebhookTrigger"[],
    "secret" VARCHAR(1024),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_Webhook" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InstagramContact" (
    "id" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "instagramNickname" TEXT NOT NULL,
    "instagramId" TEXT,
    "firstName" TEXT,
    "lastName" TEXT,
    "email" TEXT,
    "phone" TEXT,
    "status" TEXT NOT NULL DEFAULT 'new',
    "lastInteractionAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "avatar" VARCHAR(2048),
    "followUpMessage1" TEXT,
    "followUpMessage2" TEXT,
    "followUpMessage3" TEXT,
    "followUpMessage4" TEXT,
    "followUpMessage5" TEXT,
    "followUpMessage6" TEXT,
    "followUpStatus1" "FollowUpStatus",
    "followUpStatus2" "FollowUpStatus",
    "followUpStatus3" "FollowUpStatus",
    "followUpStatus4" "FollowUpStatus",
    "followUpStatus5" "FollowUpStatus",
    "followUpStatus6" "FollowUpStatus",
    "followUpTime1" TIMESTAMP(3),
    "followUpTime2" TIMESTAMP(3),
    "followUpTime3" TIMESTAMP(3),
    "followUpTime4" TIMESTAMP(3),
    "followUpTime5" TIMESTAMP(3),
    "followUpTime6" TIMESTAMP(3),
    "isConversionLinkSent" BOOLEAN NOT NULL DEFAULT false,
    "isIgnored" BOOLEAN NOT NULL DEFAULT false,
    "isTakeControl" BOOLEAN NOT NULL DEFAULT false,
    "messageCount" INTEGER NOT NULL DEFAULT 0,
    "aiSuspiciousFlags" JSONB,
    "aiSuspiciousNotes" TEXT,
    "lastAiAlert" TIMESTAMP(3),
    "lastSlackAlert" TIMESTAMP(3),
    "slackChannelId" VARCHAR(255),
    "slackThreadTs" VARCHAR(255),
    "stage" "InstagramContactStage" NOT NULL DEFAULT 'new',
    "followerCount" INTEGER,
    "isUserFollowBusiness" BOOLEAN,
    "isBusinessFollowUser" BOOLEAN,
    "isVerifiedUser" BOOLEAN,
    "priority" INTEGER NOT NULL DEFAULT 3,
    "conversationSource" "ConversationSource" NOT NULL DEFAULT 'extension',
    "nextMessageAt" TIMESTAMP(3),
    "attackListStatus" TEXT DEFAULT 'pending',
    "batchMessageStatus" TEXT DEFAULT 'pending',
    "currentMessageSequence" INTEGER DEFAULT 1,
    "lastMessageSentAt" TIMESTAMP(3),
    "batchId" TEXT,
    "firstMessageHash" VARCHAR(64),
    "messagesSentCount" INTEGER NOT NULL DEFAULT 0,
    "lastMessageSentHash" VARCHAR(64),

    CONSTRAINT "InstagramContact_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InstagramFollower" (
    "id" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "instagramNickname" TEXT NOT NULL,
    "instagramId" TEXT,
    "avatar" VARCHAR(2048),
    "followerCount" INTEGER,
    "isVerified" BOOLEAN NOT NULL DEFAULT false,
    "batchNumber" INTEGER NOT NULL DEFAULT 1,
    "priority" "InstagramFollowerPriority" NOT NULL DEFAULT 'normal',
    "status" "InstagramFollowerStatus" NOT NULL DEFAULT 'pending',
    "isTargeted" BOOLEAN NOT NULL DEFAULT false,
    "lastResponseAt" TIMESTAMP(3),
    "conversationStartedAt" TIMESTAMP(3),
    "followUpCount" INTEGER NOT NULL DEFAULT 0,
    "lastFollowUpAt" TIMESTAMP(3),
    "nextFollowUpAt" TIMESTAMP(3),
    "isConversationActive" BOOLEAN NOT NULL DEFAULT false,
    "automationEnabled" BOOLEAN NOT NULL DEFAULT true,
    "conversationId" TEXT,
    "isContacted" BOOLEAN NOT NULL DEFAULT false,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InstagramFollower_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AboutMeConfig" (
    "id" UUID NOT NULL,
    "botSettingsId" UUID NOT NULL,
    "aboutMePrompt" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AboutMeConfig_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BotClosingLevel" (
    "id" UUID NOT NULL,
    "levelNumber" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "promptContent" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BotClosingLevel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BotMode" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "promptTemplate" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BotMode_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BotRule" (
    "id" UUID NOT NULL,
    "botSettingsId" UUID NOT NULL,
    "ruleType" TEXT NOT NULL,
    "ruleContent" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BotRule_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "GlobalPrompt" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "promptContent" TEXT NOT NULL,
    "promptType" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "GlobalPrompt_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AdminPrompt" (
    "id" UUID NOT NULL,
    "generalPrompt" TEXT NOT NULL,
    "technicalPrompt" TEXT NOT NULL,
    "conversationGatheringPrompt" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AdminPrompt_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AdminSettings" (
    "id" UUID NOT NULL,
    "cacheForAllUsers" BOOLEAN NOT NULL DEFAULT false,
    "cacheType" TEXT NOT NULL DEFAULT '5m',
    "enableConversationCache" BOOLEAN NOT NULL DEFAULT true,
    "maxConversationMessages" INTEGER NOT NULL DEFAULT 200,
    "aiProvider" TEXT NOT NULL DEFAULT 'claude',
    "openrouterApiKey" TEXT,
    "openrouterModel" TEXT,
    "useReasoning" BOOLEAN NOT NULL DEFAULT false,
    "reasoningBudget" INTEGER NOT NULL DEFAULT 4000,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AdminSettings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BotStyle" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "promptText" TEXT NOT NULL,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BotStyle_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PromptConfig" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "aboutUs" TEXT,
    "qualificationQuestions" TEXT,
    "additionalInfo" TEXT,
    "botStyleId" UUID,
    "youtubeLink" TEXT,
    "websiteLink" TEXT,
    "leadMagnetLink" TEXT,
    "conversionLink" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PromptConfig_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InstagramBotSettings" (
    "id" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "instagramToken" TEXT NOT NULL,
    "instagramAccountId" TEXT NOT NULL,
    "status" "InstagramBotStatus" NOT NULL DEFAULT 'disabled',
    "responseTimeMin" INTEGER NOT NULL DEFAULT 10,
    "responseTimeMax" INTEGER NOT NULL DEFAULT 20,
    "botModeId" UUID NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InstagramBotSettings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InstagramConversation" (
    "id" UUID NOT NULL,
    "contactId" UUID NOT NULL,
    "messageType" "MessageType" NOT NULL,
    "messageContent" TEXT NOT NULL,
    "instagramMessageId" TEXT,
    "sentAt" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "InstagramConversation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InstagramMessage" (
    "id" UUID NOT NULL,
    "contactId" UUID NOT NULL,
    "messageId" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "isFromUser" BOOLEAN NOT NULL DEFAULT true,
    "isFromExtension" BOOLEAN NOT NULL DEFAULT false,
    "messageType" "InstagramMessageType" NOT NULL DEFAULT 'RECEIVED',
    "isEcho" BOOLEAN NOT NULL DEFAULT false,
    "mediaUrl" TEXT,
    "mediaType" TEXT,
    "mediaDescription" TEXT,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "InstagramMessage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InstagramFollowUp" (
    "id" UUID NOT NULL,
    "contactId" UUID NOT NULL,
    "sequenceNumber" INTEGER NOT NULL,
    "message" TEXT NOT NULL,
    "scheduledTime" TIMESTAMP(3) NOT NULL,
    "status" "FollowUpStatus" NOT NULL DEFAULT 'pending',
    "sentAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InstagramFollowUp_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InstagramSettings" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "isBotEnabled" BOOLEAN NOT NULL DEFAULT false,
    "minResponseTime" INTEGER NOT NULL DEFAULT 30,
    "maxResponseTime" INTEGER NOT NULL DEFAULT 50,
    "messageDelayMin" INTEGER NOT NULL DEFAULT 3,
    "messageDelayMax" INTEGER NOT NULL DEFAULT 5,
    "instagramToken" TEXT,
    "instagramAccountId" TEXT,
    "instagramWebhookId" TEXT,
    "instagramUsername" TEXT,
    "instagramName" TEXT,
    "instagramProfilePicture" TEXT,
    "isConnected" BOOLEAN NOT NULL DEFAULT false,
    "tokenExpiresAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "followUpCleanupDays" INTEGER DEFAULT 30,
    "autoCleanupEnabled" BOOLEAN DEFAULT true,

    CONSTRAINT "InstagramSettings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InstagramConversationsNotGathered" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "instagramConversationId" TEXT NOT NULL,
    "participantUsername" TEXT NOT NULL,
    "participantId" TEXT NOT NULL,
    "updatedTime" TIMESTAMP(3) NOT NULL,
    "isGathered" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InstagramConversationsNotGathered_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InstagramFollowerProcessingState" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "lastProcessedPosition" INTEGER NOT NULL DEFAULT 0,
    "totalFollowersCount" INTEGER,
    "isCompleted" BOOLEAN NOT NULL DEFAULT false,
    "lastProcessedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InstagramFollowerProcessingState_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LeadForm" (
    "id" UUID NOT NULL,
    "botSettingsId" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "formFields" JSONB NOT NULL,
    "successMessage" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LeadForm_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LeadMagnet" (
    "id" UUID NOT NULL,
    "botSettingsId" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "contentUrl" TEXT,
    "triggerConditions" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LeadMagnet_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "QualificationQuestion" (
    "id" UUID NOT NULL,
    "botSettingsId" UUID NOT NULL,
    "question" TEXT NOT NULL,
    "orderNumber" INTEGER NOT NULL,
    "isRequired" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "QualificationQuestion_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MessageQueue" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "senderId" TEXT NOT NULL,
    "messageId" TEXT NOT NULL,
    "messageText" TEXT NOT NULL,
    "mediaUrl" TEXT,
    "mediaType" TEXT,
    "priority" INTEGER NOT NULL DEFAULT 1,
    "scheduledAt" TIMESTAMP(3) NOT NULL,
    "attempts" INTEGER NOT NULL DEFAULT 0,
    "maxAttempts" INTEGER NOT NULL DEFAULT 3,
    "status" "QueueStatus" NOT NULL DEFAULT 'pending',
    "errorMessage" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MessageQueue_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FollowerProcessingQueue" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "followerId" UUID NOT NULL,
    "priority" INTEGER NOT NULL DEFAULT 1,
    "scheduledAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "attempts" INTEGER NOT NULL DEFAULT 0,
    "maxAttempts" INTEGER NOT NULL DEFAULT 3,
    "status" "FollowerProcessingStatus" NOT NULL DEFAULT 'pending',
    "errorMessage" TEXT,
    "hasConversation" BOOLEAN NOT NULL DEFAULT false,
    "processingType" "FollowerProcessingType" NOT NULL DEFAULT 'batch',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "FollowerProcessingQueue_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InstagramError" (
    "id" UUID NOT NULL,
    "organizationId" UUID,
    "contactId" UUID,
    "messageId" TEXT,
    "operation" TEXT NOT NULL,
    "errorMessage" TEXT NOT NULL,
    "errorStack" TEXT,
    "retryCount" INTEGER NOT NULL DEFAULT 0,
    "resolved" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "InstagramError_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WebhookLog" (
    "id" UUID NOT NULL,
    "requestId" TEXT NOT NULL,
    "method" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "headers" JSONB,
    "body" JSONB,
    "signature" TEXT,
    "isValid" BOOLEAN NOT NULL,
    "responseCode" INTEGER NOT NULL,
    "processingTime" INTEGER NOT NULL,
    "errorMessage" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "WebhookLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StageChangeLog" (
    "id" UUID NOT NULL,
    "contactId" UUID NOT NULL,
    "fromStage" TEXT,
    "toStage" TEXT NOT NULL,
    "reason" TEXT,
    "changedBy" TEXT NOT NULL,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "StageChangeLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TestSettings" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "aboutUs" TEXT,
    "qualificationQuestions" TEXT,
    "additionalInfo" TEXT,
    "botStyleId" UUID,
    "youtubeLink" TEXT,
    "websiteLink" TEXT,
    "leadMagnetLink" TEXT,
    "conversionLink" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TestSettings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ChromeExtensionSettings" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "timeBetweenDMsMin" INTEGER NOT NULL DEFAULT 3,
    "timeBetweenDMsMax" INTEGER NOT NULL DEFAULT 8,
    "messagesBeforeBreakMin" INTEGER NOT NULL DEFAULT 8,
    "messagesBeforeBreakMax" INTEGER NOT NULL DEFAULT 15,
    "breakDurationMin" INTEGER NOT NULL DEFAULT 10,
    "breakDurationMax" INTEGER NOT NULL DEFAULT 20,
    "pauseStart" TEXT NOT NULL DEFAULT '00:30',
    "pauseStop" TEXT NOT NULL DEFAULT '07:00',
    "smartFocus" BOOLEAN NOT NULL DEFAULT true,
    "isConnected" BOOLEAN NOT NULL DEFAULT false,
    "lastConnectionAt" TIMESTAMP(3),
    "extensionStatus" TEXT NOT NULL DEFAULT 'FRESH_START',
    "currentActivity" TEXT,
    "lastActivityAt" TIMESTAMP(3),
    "totalFollowersScraped" INTEGER NOT NULL DEFAULT 0,
    "lastScrapedPosition" INTEGER NOT NULL DEFAULT 0,
    "lastScrapedUsernames" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "scrapingTargetReached" BOOLEAN NOT NULL DEFAULT false,
    "allFollowersScraped" BOOLEAN NOT NULL DEFAULT false,
    "lastScrapingSession" TIMESTAMP(3),
    "scrapingEnabled" BOOLEAN NOT NULL DEFAULT true,
    "scrapingIntervalMin" INTEGER NOT NULL DEFAULT 15,
    "scrapingIntervalMax" INTEGER NOT NULL DEFAULT 30,
    "scrapingIntervalDays" INTEGER NOT NULL DEFAULT 5,
    "nextScrapingAllowedAt" TIMESTAMP(3),
    "dailyMessageLimit" INTEGER,
    "messagesSentToday" INTEGER NOT NULL DEFAULT 0,
    "lastMessageResetDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "attackPastFollowers" BOOLEAN NOT NULL DEFAULT true,
    "continuousScraping" BOOLEAN NOT NULL DEFAULT true,
    "lastContinuousScrapingAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ChromeExtensionSettings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TestAdminPrompt" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "generalPrompt" TEXT NOT NULL,
    "technicalPrompt" TEXT NOT NULL,
    "conversationGatheringPrompt" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TestAdminPrompt_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MessageBatch" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MessageBatch_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MessageBatchItem" (
    "id" UUID NOT NULL,
    "messageBatchId" UUID NOT NULL,
    "messageText" TEXT NOT NULL,
    "sequenceNumber" INTEGER NOT NULL,
    "delayMinutes" INTEGER NOT NULL DEFAULT 5,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "MessageBatchItem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FollowUpTemplate" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "messageText" TEXT NOT NULL,
    "sequenceNumber" INTEGER NOT NULL,
    "variationNumber" INTEGER NOT NULL DEFAULT 1,
    "delayHours" INTEGER NOT NULL DEFAULT 24,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "FollowUpTemplate_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "IX_Account_userId" ON "Account"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "Account_provider_providerAccountId_key" ON "Account"("provider", "providerAccountId");

-- CreateIndex
CREATE UNIQUE INDEX "ApiKey_hashedKey_key" ON "ApiKey"("hashedKey");

-- CreateIndex
CREATE INDEX "IX_ApiKey_organizationId" ON "ApiKey"("organizationId");

-- CreateIndex
CREATE UNIQUE INDEX "AuthenticatorApp_userId_key" ON "AuthenticatorApp"("userId");

-- CreateIndex
CREATE INDEX "IX_AuthenticatorApp_userId" ON "AuthenticatorApp"("userId");

-- CreateIndex
CREATE INDEX "IX_ChangeEmailRequest_userId" ON "ChangeEmailRequest"("userId");

-- CreateIndex
CREATE INDEX "IX_Feedback_organizationId" ON "Feedback"("organizationId");

-- CreateIndex
CREATE INDEX "IX_Feedback_userId" ON "Feedback"("userId");

-- CreateIndex
CREATE INDEX "IX_Invitation_organizationId" ON "Invitation"("organizationId");

-- CreateIndex
CREATE INDEX "IX_Invitation_token" ON "Invitation"("token");

-- CreateIndex
CREATE UNIQUE INDEX "Membership_organizationId_userId_key" ON "Membership"("organizationId", "userId");

-- CreateIndex
CREATE INDEX "IX_Notification_userId" ON "Notification"("userId");

-- CreateIndex
CREATE INDEX "IX_Notification_organizationId" ON "Notification"("organizationId");

-- CreateIndex
CREATE UNIQUE INDEX "Organization_slug_key" ON "Organization"("slug");

-- CreateIndex
CREATE INDEX "IX_Organization_stripeCustomerId" ON "Organization"("stripeCustomerId");

-- CreateIndex
CREATE INDEX "IX_OrganizationLogo_organizationId" ON "OrganizationLogo"("organizationId");

-- CreateIndex
CREATE INDEX "IX_ResetPasswordRequest_email" ON "ResetPasswordRequest"("email");

-- CreateIndex
CREATE UNIQUE INDEX "Session_sessionToken_key" ON "Session"("sessionToken");

-- CreateIndex
CREATE INDEX "IX_Session_userId" ON "Session"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE INDEX "IX_UserImage_userId" ON "UserImage"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "VerificationToken_token_key" ON "VerificationToken"("token");

-- CreateIndex
CREATE UNIQUE INDEX "VerificationToken_identifier_token_key" ON "VerificationToken"("identifier", "token");

-- CreateIndex
CREATE INDEX "IX_Webhook_organizationId" ON "Webhook"("organizationId");

-- CreateIndex
CREATE INDEX "InstagramContact_instagramNickname_idx" ON "InstagramContact"("instagramNickname");

-- CreateIndex
CREATE INDEX "InstagramContact_stage_idx" ON "InstagramContact"("stage");

-- CreateIndex
CREATE INDEX "InstagramContact_userId_idx" ON "InstagramContact"("userId");

-- CreateIndex
CREATE INDEX "InstagramContact_organizationId_idx" ON "InstagramContact"("organizationId");

-- CreateIndex
CREATE INDEX "InstagramContact_firstMessageHash_idx" ON "InstagramContact"("firstMessageHash");

-- CreateIndex
CREATE INDEX "InstagramFollower_instagramNickname_idx" ON "InstagramFollower"("instagramNickname");

-- CreateIndex
CREATE INDEX "InstagramFollower_status_idx" ON "InstagramFollower"("status");

-- CreateIndex
CREATE INDEX "InstagramFollower_priority_idx" ON "InstagramFollower"("priority");

-- CreateIndex
CREATE INDEX "InstagramFollower_batchNumber_idx" ON "InstagramFollower"("batchNumber");

-- CreateIndex
CREATE INDEX "InstagramFollower_userId_idx" ON "InstagramFollower"("userId");

-- CreateIndex
CREATE INDEX "InstagramFollower_organizationId_idx" ON "InstagramFollower"("organizationId");

-- CreateIndex
CREATE INDEX "InstagramFollower_automationEnabled_idx" ON "InstagramFollower"("automationEnabled");

-- CreateIndex
CREATE UNIQUE INDEX "AboutMeConfig_botSettingsId_key" ON "AboutMeConfig"("botSettingsId");

-- CreateIndex
CREATE INDEX "BotRule_botSettingsId_idx" ON "BotRule"("botSettingsId");

-- CreateIndex
CREATE INDEX "AdminSettings_aiProvider_idx" ON "AdminSettings"("aiProvider");

-- CreateIndex
CREATE INDEX "PromptConfig_organizationId_idx" ON "PromptConfig"("organizationId");

-- CreateIndex
CREATE INDEX "PromptConfig_botStyleId_idx" ON "PromptConfig"("botStyleId");

-- CreateIndex
CREATE INDEX "InstagramBotSettings_organizationId_idx" ON "InstagramBotSettings"("organizationId");

-- CreateIndex
CREATE INDEX "InstagramBotSettings_userId_idx" ON "InstagramBotSettings"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "InstagramBotSettings_userId_organizationId_key" ON "InstagramBotSettings"("userId", "organizationId");

-- CreateIndex
CREATE INDEX "InstagramConversation_contactId_idx" ON "InstagramConversation"("contactId");

-- CreateIndex
CREATE INDEX "InstagramMessage_contactId_idx" ON "InstagramMessage"("contactId");

-- CreateIndex
CREATE INDEX "InstagramMessage_messageId_idx" ON "InstagramMessage"("messageId");

-- CreateIndex
CREATE INDEX "InstagramMessage_messageType_idx" ON "InstagramMessage"("messageType");

-- CreateIndex
CREATE INDEX "InstagramFollowUp_contactId_idx" ON "InstagramFollowUp"("contactId");

-- CreateIndex
CREATE UNIQUE INDEX "InstagramSettings_organizationId_key" ON "InstagramSettings"("organizationId");

-- CreateIndex
CREATE INDEX "InstagramSettings_organizationId_idx" ON "InstagramSettings"("organizationId");

-- CreateIndex
CREATE INDEX "InstagramSettings_instagramWebhookId_idx" ON "InstagramSettings"("instagramWebhookId");

-- CreateIndex
CREATE UNIQUE INDEX "InstagramConversationsNotGathered_instagramConversationId_key" ON "InstagramConversationsNotGathered"("instagramConversationId");

-- CreateIndex
CREATE INDEX "InstagramConversationsNotGathered_organizationId_idx" ON "InstagramConversationsNotGathered"("organizationId");

-- CreateIndex
CREATE INDEX "InstagramConversationsNotGathered_participantUsername_idx" ON "InstagramConversationsNotGathered"("participantUsername");

-- CreateIndex
CREATE INDEX "InstagramConversationsNotGathered_participantId_idx" ON "InstagramConversationsNotGathered"("participantId");

-- CreateIndex
CREATE INDEX "InstagramConversationsNotGathered_isGathered_idx" ON "InstagramConversationsNotGathered"("isGathered");

-- CreateIndex
CREATE INDEX "InstagramConversationsNotGathered_updatedTime_idx" ON "InstagramConversationsNotGathered"("updatedTime");

-- CreateIndex
CREATE UNIQUE INDEX "InstagramFollowerProcessingState_organizationId_key" ON "InstagramFollowerProcessingState"("organizationId");

-- CreateIndex
CREATE INDEX "InstagramFollowerProcessingState_organizationId_idx" ON "InstagramFollowerProcessingState"("organizationId");

-- CreateIndex
CREATE INDEX "LeadForm_botSettingsId_idx" ON "LeadForm"("botSettingsId");

-- CreateIndex
CREATE INDEX "LeadMagnet_botSettingsId_idx" ON "LeadMagnet"("botSettingsId");

-- CreateIndex
CREATE INDEX "QualificationQuestion_botSettingsId_idx" ON "QualificationQuestion"("botSettingsId");

-- CreateIndex
CREATE INDEX "MessageQueue_status_scheduledAt_idx" ON "MessageQueue"("status", "scheduledAt");

-- CreateIndex
CREATE INDEX "MessageQueue_organizationId_idx" ON "MessageQueue"("organizationId");

-- CreateIndex
CREATE INDEX "FollowerProcessingQueue_status_scheduledAt_idx" ON "FollowerProcessingQueue"("status", "scheduledAt");

-- CreateIndex
CREATE INDEX "FollowerProcessingQueue_organizationId_idx" ON "FollowerProcessingQueue"("organizationId");

-- CreateIndex
CREATE INDEX "FollowerProcessingQueue_followerId_idx" ON "FollowerProcessingQueue"("followerId");

-- CreateIndex
CREATE INDEX "InstagramError_organizationId_idx" ON "InstagramError"("organizationId");

-- CreateIndex
CREATE INDEX "InstagramError_createdAt_idx" ON "InstagramError"("createdAt");

-- CreateIndex
CREATE INDEX "InstagramError_resolved_idx" ON "InstagramError"("resolved");

-- CreateIndex
CREATE UNIQUE INDEX "WebhookLog_requestId_key" ON "WebhookLog"("requestId");

-- CreateIndex
CREATE INDEX "WebhookLog_createdAt_idx" ON "WebhookLog"("createdAt");

-- CreateIndex
CREATE INDEX "WebhookLog_isValid_idx" ON "WebhookLog"("isValid");

-- CreateIndex
CREATE INDEX "StageChangeLog_contactId_idx" ON "StageChangeLog"("contactId");

-- CreateIndex
CREATE INDEX "StageChangeLog_createdAt_idx" ON "StageChangeLog"("createdAt");

-- CreateIndex
CREATE INDEX "StageChangeLog_changedBy_idx" ON "StageChangeLog"("changedBy");

-- CreateIndex
CREATE INDEX "TestSettings_organizationId_idx" ON "TestSettings"("organizationId");

-- CreateIndex
CREATE INDEX "TestSettings_userId_idx" ON "TestSettings"("userId");

-- CreateIndex
CREATE INDEX "TestSettings_botStyleId_idx" ON "TestSettings"("botStyleId");

-- CreateIndex
CREATE UNIQUE INDEX "TestSettings_organizationId_userId_key" ON "TestSettings"("organizationId", "userId");

-- CreateIndex
CREATE UNIQUE INDEX "ChromeExtensionSettings_organizationId_key" ON "ChromeExtensionSettings"("organizationId");

-- CreateIndex
CREATE INDEX "ChromeExtensionSettings_organizationId_idx" ON "ChromeExtensionSettings"("organizationId");

-- CreateIndex
CREATE INDEX "ChromeExtensionSettings_lastMessageResetDate_idx" ON "ChromeExtensionSettings"("lastMessageResetDate");

-- CreateIndex
CREATE INDEX "TestAdminPrompt_organizationId_idx" ON "TestAdminPrompt"("organizationId");

-- CreateIndex
CREATE INDEX "TestAdminPrompt_userId_idx" ON "TestAdminPrompt"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "TestAdminPrompt_organizationId_userId_key" ON "TestAdminPrompt"("organizationId", "userId");

-- CreateIndex
CREATE INDEX "MessageBatch_organizationId_idx" ON "MessageBatch"("organizationId");

-- CreateIndex
CREATE INDEX "MessageBatch_userId_idx" ON "MessageBatch"("userId");

-- CreateIndex
CREATE INDEX "MessageBatchItem_messageBatchId_idx" ON "MessageBatchItem"("messageBatchId");

-- CreateIndex
CREATE INDEX "FollowUpTemplate_organizationId_idx" ON "FollowUpTemplate"("organizationId");

-- CreateIndex
CREATE INDEX "FollowUpTemplate_userId_idx" ON "FollowUpTemplate"("userId");

-- CreateIndex
CREATE INDEX "FollowUpTemplate_sequenceNumber_idx" ON "FollowUpTemplate"("sequenceNumber");

-- CreateIndex
CREATE INDEX "FollowUpTemplate_variationNumber_idx" ON "FollowUpTemplate"("variationNumber");

-- CreateIndex
CREATE UNIQUE INDEX "FollowUpTemplate_organizationId_sequenceNumber_variationNum_key" ON "FollowUpTemplate"("organizationId", "sequenceNumber", "variationNumber");

-- AddForeignKey
ALTER TABLE "Account" ADD CONSTRAINT "Account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ApiKey" ADD CONSTRAINT "ApiKey_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AuthenticatorApp" ADD CONSTRAINT "AuthenticatorApp_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChangeEmailRequest" ADD CONSTRAINT "ChangeEmailRequest_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Feedback" ADD CONSTRAINT "Feedback_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Feedback" ADD CONSTRAINT "Feedback_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Invitation" ADD CONSTRAINT "Invitation_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Membership" ADD CONSTRAINT "Membership_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Membership" ADD CONSTRAINT "Membership_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Session" ADD CONSTRAINT "Session_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Webhook" ADD CONSTRAINT "Webhook_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InstagramContact" ADD CONSTRAINT "InstagramContact_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InstagramContact" ADD CONSTRAINT "InstagramContact_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InstagramFollower" ADD CONSTRAINT "InstagramFollower_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InstagramFollower" ADD CONSTRAINT "InstagramFollower_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AboutMeConfig" ADD CONSTRAINT "AboutMeConfig_botSettingsId_fkey" FOREIGN KEY ("botSettingsId") REFERENCES "InstagramBotSettings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BotRule" ADD CONSTRAINT "BotRule_botSettingsId_fkey" FOREIGN KEY ("botSettingsId") REFERENCES "InstagramBotSettings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromptConfig" ADD CONSTRAINT "PromptConfig_botStyleId_fkey" FOREIGN KEY ("botStyleId") REFERENCES "BotStyle"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromptConfig" ADD CONSTRAINT "PromptConfig_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InstagramBotSettings" ADD CONSTRAINT "InstagramBotSettings_botModeId_fkey" FOREIGN KEY ("botModeId") REFERENCES "BotMode"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InstagramBotSettings" ADD CONSTRAINT "InstagramBotSettings_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InstagramBotSettings" ADD CONSTRAINT "InstagramBotSettings_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InstagramConversation" ADD CONSTRAINT "InstagramConversation_contactId_fkey" FOREIGN KEY ("contactId") REFERENCES "InstagramContact"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InstagramMessage" ADD CONSTRAINT "InstagramMessage_contactId_fkey" FOREIGN KEY ("contactId") REFERENCES "InstagramContact"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InstagramFollowUp" ADD CONSTRAINT "InstagramFollowUp_contactId_fkey" FOREIGN KEY ("contactId") REFERENCES "InstagramContact"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InstagramSettings" ADD CONSTRAINT "InstagramSettings_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InstagramConversationsNotGathered" ADD CONSTRAINT "InstagramConversationsNotGathered_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InstagramFollowerProcessingState" ADD CONSTRAINT "InstagramFollowerProcessingState_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LeadForm" ADD CONSTRAINT "LeadForm_botSettingsId_fkey" FOREIGN KEY ("botSettingsId") REFERENCES "InstagramBotSettings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LeadMagnet" ADD CONSTRAINT "LeadMagnet_botSettingsId_fkey" FOREIGN KEY ("botSettingsId") REFERENCES "InstagramBotSettings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "QualificationQuestion" ADD CONSTRAINT "QualificationQuestion_botSettingsId_fkey" FOREIGN KEY ("botSettingsId") REFERENCES "InstagramBotSettings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MessageQueue" ADD CONSTRAINT "MessageQueue_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FollowerProcessingQueue" ADD CONSTRAINT "FollowerProcessingQueue_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FollowerProcessingQueue" ADD CONSTRAINT "FollowerProcessingQueue_followerId_fkey" FOREIGN KEY ("followerId") REFERENCES "InstagramFollower"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InstagramError" ADD CONSTRAINT "InstagramError_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StageChangeLog" ADD CONSTRAINT "StageChangeLog_contactId_fkey" FOREIGN KEY ("contactId") REFERENCES "InstagramContact"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TestSettings" ADD CONSTRAINT "TestSettings_botStyleId_fkey" FOREIGN KEY ("botStyleId") REFERENCES "BotStyle"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TestSettings" ADD CONSTRAINT "TestSettings_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TestSettings" ADD CONSTRAINT "TestSettings_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChromeExtensionSettings" ADD CONSTRAINT "ChromeExtensionSettings_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TestAdminPrompt" ADD CONSTRAINT "TestAdminPrompt_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TestAdminPrompt" ADD CONSTRAINT "TestAdminPrompt_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MessageBatch" ADD CONSTRAINT "MessageBatch_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MessageBatch" ADD CONSTRAINT "MessageBatch_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MessageBatchItem" ADD CONSTRAINT "MessageBatchItem_messageBatchId_fkey" FOREIGN KEY ("messageBatchId") REFERENCES "MessageBatch"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FollowUpTemplate" ADD CONSTRAINT "FollowUpTemplate_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FollowUpTemplate" ADD CONSTRAINT "FollowUpTemplate_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
