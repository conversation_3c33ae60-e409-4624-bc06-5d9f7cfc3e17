/*
  Warnings:

  - You are about to drop the column `followUpMessage1` on the `InstagramContact` table. All the data in the column will be lost.
  - You are about to drop the column `followUpMessage2` on the `InstagramContact` table. All the data in the column will be lost.
  - You are about to drop the column `followUpMessage3` on the `InstagramContact` table. All the data in the column will be lost.
  - You are about to drop the column `followUpMessage4` on the `InstagramContact` table. All the data in the column will be lost.
  - You are about to drop the column `followUpMessage5` on the `InstagramContact` table. All the data in the column will be lost.
  - You are about to drop the column `followUpMessage6` on the `InstagramContact` table. All the data in the column will be lost.
  - You are about to drop the column `followUpStatus1` on the `InstagramContact` table. All the data in the column will be lost.
  - You are about to drop the column `followUpStatus2` on the `InstagramContact` table. All the data in the column will be lost.
  - You are about to drop the column `followUpStatus3` on the `InstagramContact` table. All the data in the column will be lost.
  - You are about to drop the column `followUpStatus4` on the `InstagramContact` table. All the data in the column will be lost.
  - You are about to drop the column `followUpStatus5` on the `InstagramContact` table. All the data in the column will be lost.
  - You are about to drop the column `followUpStatus6` on the `InstagramContact` table. All the data in the column will be lost.
  - You are about to drop the column `followUpTime1` on the `InstagramContact` table. All the data in the column will be lost.
  - You are about to drop the column `followUpTime2` on the `InstagramContact` table. All the data in the column will be lost.
  - You are about to drop the column `followUpTime3` on the `InstagramContact` table. All the data in the column will be lost.
  - You are about to drop the column `followUpTime4` on the `InstagramContact` table. All the data in the column will be lost.
  - You are about to drop the column `followUpTime5` on the `InstagramContact` table. All the data in the column will be lost.
  - You are about to drop the column `followUpTime6` on the `InstagramContact` table. All the data in the column will be lost.
  - You are about to drop the `InstagramFollowUp` table. If the table is not empty, all the data it contains will be lost.

*/
-- CreateEnum
CREATE TYPE "MessageListType" AS ENUM ('SYSTEM', 'EXTENSION');

-- CreateEnum
CREATE TYPE "MessageListStatus" AS ENUM ('PENDING', 'PROCESSING', 'SENT', 'FAILED');

-- CreateEnum
CREATE TYPE "MessageListHandler" AS ENUM ('SYSTEM', 'EXTENSION');

-- CreateEnum
CREATE TYPE "AttackListType" AS ENUM ('FIRST_MESSAGE', 'FOLLOW_UP');

-- CreateEnum
CREATE TYPE "AttackListStatus" AS ENUM ('READY', 'SENT', 'FAILED');

-- DropForeignKey
ALTER TABLE "InstagramFollowUp" DROP CONSTRAINT "InstagramFollowUp_contactId_fkey";

-- AlterTable
ALTER TABLE "InstagramContact" DROP COLUMN "followUpMessage1",
DROP COLUMN "followUpMessage2",
DROP COLUMN "followUpMessage3",
DROP COLUMN "followUpMessage4",
DROP COLUMN "followUpMessage5",
DROP COLUMN "followUpMessage6",
DROP COLUMN "followUpStatus1",
DROP COLUMN "followUpStatus2",
DROP COLUMN "followUpStatus3",
DROP COLUMN "followUpStatus4",
DROP COLUMN "followUpStatus5",
DROP COLUMN "followUpStatus6",
DROP COLUMN "followUpTime1",
DROP COLUMN "followUpTime2",
DROP COLUMN "followUpTime3",
DROP COLUMN "followUpTime4",
DROP COLUMN "followUpTime5",
DROP COLUMN "followUpTime6";

-- DropTable
DROP TABLE "InstagramFollowUp";

-- DropEnum
DROP TYPE "FollowUpStatus";

-- CreateTable
CREATE TABLE "MessageList" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "contactId" UUID NOT NULL,
    "nickname" VARCHAR(255) NOT NULL,
    "priority" INTEGER NOT NULL DEFAULT 3,
    "messageType" "MessageListType" NOT NULL DEFAULT 'SYSTEM',
    "scheduledTime" TIMESTAMP(3) NOT NULL,
    "messageContent" TEXT NOT NULL,
    "status" "MessageListStatus" NOT NULL DEFAULT 'PENDING',
    "handlerType" "MessageListHandler" NOT NULL DEFAULT 'SYSTEM',
    "sequenceNumber" INTEGER NOT NULL DEFAULT 1,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MessageList_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AttackList" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "contactId" UUID NOT NULL,
    "messageListId" UUID,
    "nickname" VARCHAR(255) NOT NULL,
    "priority" INTEGER NOT NULL DEFAULT 3,
    "messageContent" TEXT NOT NULL,
    "messageType" "AttackListType" NOT NULL DEFAULT 'FIRST_MESSAGE',
    "sequenceNumber" INTEGER NOT NULL DEFAULT 1,
    "addedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" "AttackListStatus" NOT NULL DEFAULT 'READY',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AttackList_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "MessageList_organizationId_idx" ON "MessageList"("organizationId");

-- CreateIndex
CREATE INDEX "MessageList_scheduledTime_idx" ON "MessageList"("scheduledTime");

-- CreateIndex
CREATE INDEX "MessageList_contactId_idx" ON "MessageList"("contactId");

-- CreateIndex
CREATE INDEX "MessageList_priority_idx" ON "MessageList"("priority");

-- CreateIndex
CREATE INDEX "AttackList_organizationId_idx" ON "AttackList"("organizationId");

-- CreateIndex
CREATE INDEX "AttackList_priority_idx" ON "AttackList"("priority");

-- CreateIndex
CREATE INDEX "AttackList_addedAt_idx" ON "AttackList"("addedAt");

-- CreateIndex
CREATE INDEX "AttackList_contactId_idx" ON "AttackList"("contactId");

-- CreateIndex
CREATE INDEX "AttackList_messageListId_idx" ON "AttackList"("messageListId");

-- AddForeignKey
ALTER TABLE "MessageList" ADD CONSTRAINT "MessageList_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MessageList" ADD CONSTRAINT "MessageList_contactId_fkey" FOREIGN KEY ("contactId") REFERENCES "InstagramContact"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AttackList" ADD CONSTRAINT "AttackList_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AttackList" ADD CONSTRAINT "AttackList_contactId_fkey" FOREIGN KEY ("contactId") REFERENCES "InstagramContact"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AttackList" ADD CONSTRAINT "AttackList_messageListId_fkey" FOREIGN KEY ("messageListId") REFERENCES "MessageList"("id") ON DELETE SET NULL ON UPDATE CASCADE;
