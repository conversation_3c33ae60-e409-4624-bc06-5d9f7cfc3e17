generator client {
  provider        = "prisma-client-js"
  previewFeatures = []
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String   @id(map: "PK_Account") @default(uuid()) @db.Uuid
  userId            String   @db.Uuid
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId], map: "IX_Account_userId")
}

model ApiKey {
  id             String       @id(map: "PK_ApiKey") @default(uuid()) @db.Uuid
  organizationId String       @db.Uuid
  description    String       @db.VarChar(70)
  hashedKey      String       @unique
  expiresAt      DateTime?
  lastUsedAt     DateTime?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId], map: "IX_ApiKey_organizationId")
}

model AuthenticatorApp {
  id            String   @id(map: "PK_AuthenticatorApp") @default(uuid()) @db.Uuid
  userId        String   @unique @db.Uuid
  accountName   String   @db.VarChar(255)
  issuer        String   @db.VarChar(255)
  secret        String   @db.VarChar(255)
  recoveryCodes String   @db.VarChar(1024)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "IX_AuthenticatorApp_userId")
}

model ChangeEmailRequest {
  id        String   @id(map: "PK_ChangeEmailRequest") @default(uuid()) @db.Uuid
  userId    String   @db.Uuid
  email     String
  expires   DateTime
  valid     Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "IX_ChangeEmailRequest_userId")
}

model Feedback {
  id             String           @id(map: "PK_Feedback") @default(uuid()) @db.Uuid
  organizationId String           @db.Uuid
  userId         String?          @db.Uuid
  category       FeedbackCategory @default(SUGGESTION)
  message        String           @db.VarChar(4000)
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  organization   Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user           User?            @relation(fields: [userId], references: [id])

  @@index([organizationId], map: "IX_Feedback_organizationId")
  @@index([userId], map: "IX_Feedback_userId")
}

model Invitation {
  id             String           @id(map: "PK_Invitation") @default(uuid()) @db.Uuid
  organizationId String           @db.Uuid
  token          String           @default(uuid()) @db.Uuid
  email          String           @db.VarChar(255)
  role           Role             @default(MEMBER)
  status         InvitationStatus @default(PENDING)
  lastSentAt     DateTime?
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  organization   Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId], map: "IX_Invitation_organizationId")
  @@index([token], map: "IX_Invitation_token")
}

model Membership {
  id             String       @id(map: "PK_Membership") @default(uuid()) @db.Uuid
  organizationId String       @db.Uuid
  userId         String       @db.Uuid
  role           Role         @default(MEMBER)
  isOwner        Boolean      @default(false)
  createdAt      DateTime     @default(now())
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([organizationId, userId])
}

model Notification {
  id             String        @id(map: "PK_Notification") @default(uuid()) @db.Uuid
  userId         String        @db.Uuid
  subject        String?       @db.VarChar(128)
  content        String        @db.VarChar(8000)
  link           String?       @db.VarChar(2000)
  seenAt         DateTime?
  dismissed      Boolean       @default(false)
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  organizationId String?       @db.Uuid
  Organization   Organization? @relation(fields: [organizationId], references: [id])
  user           User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "IX_Notification_userId")
  @@index([organizationId], map: "IX_Notification_organizationId")
}

model Organization {
  id               String   @id(map: "PK_Organization") @default(uuid()) @db.Uuid
  stripeCustomerId String?  @db.VarChar(255)
  name             String   @db.VarChar(255)
  tier             String   @default("free") @db.VarChar(255)
  logo             String?  @db.VarChar(2048)
  slug             String   @unique @db.VarChar(255)
  apiKeys          ApiKey[]

  feedback                          Feedback[]
  InstagramBotSettings              InstagramBotSettings[]
  InstagramContact                  InstagramContact[]
  InstagramConversationsNotGathered InstagramConversationsNotGathered[]
  InstagramError                    InstagramError[]
  InstagramFollower                 InstagramFollower[]
  InstagramSettings                 InstagramSettings?
  invitations                       Invitation[]
  memberships                       Membership[]
  MessageQueue                      MessageQueue[]
  Notification                      Notification[]
  PromptConfig                      PromptConfig[]
  TestAdminPrompt                   TestAdminPrompt[]
  TestSettings                      TestSettings[]
  webhooks                          Webhook[]
  MessageBatch                      MessageBatch[]
  FollowUpTemplate                  FollowUpTemplate[]
  InstagramFollowerProcessingState  InstagramFollowerProcessingState?
  ChromeExtensionSettings           ChromeExtensionSettings?
  FollowerProcessingQueue           FollowerProcessingQueue[]
  MessageList                       MessageList[]
  AttackList                        AttackList[]

  @@index([stripeCustomerId], map: "IX_Organization_stripeCustomerId")
}

model OrganizationLogo {
  id             String  @id(map: "PK_OrganizationLogo") @default(uuid()) @db.Uuid
  organizationId String  @db.Uuid
  data           Bytes?
  contentType    String? @db.VarChar(255)
  hash           String? @db.VarChar(64)

  @@index([organizationId], map: "IX_OrganizationLogo_organizationId")
}

model ResetPasswordRequest {
  id        String   @id(map: "PK_ResetPasswordRequest") @default(uuid()) @db.Uuid
  email     String
  expires   DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([email], map: "IX_ResetPasswordRequest_email")
}

model Session {
  id           String   @id(map: "PK_Session") @default(uuid()) @db.Uuid
  sessionToken String   @unique
  userId       String   @db.Uuid
  expires      DateTime
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "IX_Session_userId")
}

model User {
  id                        String               @id(map: "PK_User") @default(uuid()) @db.Uuid
  image                     String?              @db.VarChar(2048)
  name                      String               @db.VarChar(64)
  email                     String?              @unique
  emailVerified             DateTime?
  password                  String?              @db.VarChar(60)
  lastLogin                 DateTime?
  phone                     String?              @db.VarChar(32)
  locale                    String               @default("en-US") @db.VarChar(8)
  completedOnboarding       Boolean              @default(false)
  enabledInboxNotifications Boolean              @default(false)
  enabledWeeklySummary      Boolean              @default(false)
  enabledNewsletter         Boolean              @default(false)
  enabledProductUpdates     Boolean              @default(false)
  createdAt                 DateTime             @default(now())
  updatedAt                 DateTime             @updatedAt
  accounts                  Account[]
  authenticatorApp          AuthenticatorApp?
  changeEmailRequests       ChangeEmailRequest[]

  feedback             Feedback[]
  InstagramBotSettings InstagramBotSettings[]
  InstagramContact     InstagramContact[]
  InstagramFollower    InstagramFollower[]
  memberships          Membership[]
  notifications        Notification[]
  sessions             Session[]
  TestAdminPrompt      TestAdminPrompt[]
  TestSettings         TestSettings[]
  MessageBatch         MessageBatch[]
  FollowUpTemplate     FollowUpTemplate[]
}

model UserImage {
  id          String  @id(map: "PK_UserImage") @default(uuid()) @db.Uuid
  userId      String  @db.Uuid
  data        Bytes?
  contentType String? @db.VarChar(255)
  hash        String? @db.VarChar(64)

  @@index([userId], map: "IX_UserImage_userId")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Webhook {
  id             String           @id(map: "PK_Webhook") @default(uuid()) @db.Uuid
  organizationId String           @db.Uuid
  url            String           @db.VarChar(2000)
  triggers       WebhookTrigger[]
  secret         String?          @db.VarChar(1024)
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  organization   Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId], map: "IX_Webhook_organizationId")
}

model InstagramContact {
  id                     String                  @id @default(uuid()) @db.Uuid
  userId                 String                  @db.Uuid
  organizationId         String                  @db.Uuid
  instagramNickname      String
  instagramId            String?
  firstName              String?
  lastName               String?
  email                  String?
  phone                  String?
  status                 String                  @default("new")
  lastInteractionAt      DateTime?
  createdAt              DateTime                @default(now())
  updatedAt              DateTime                @updatedAt
  avatar                 String?                 @db.VarChar(2048)
  isConversionLinkSent   Boolean                 @default(false)
  isIgnored              Boolean                 @default(false)
  isTakeControl          Boolean                 @default(false)
  messageCount           Int                     @default(0)
  aiSuspiciousFlags      Json?
  aiSuspiciousNotes      String?
  lastAiAlert            DateTime?
  lastSlackAlert         DateTime?
  slackChannelId         String?                 @db.VarChar(255)
  slackThreadTs          String?                 @db.VarChar(255)
  stage                  InstagramContactStage   @default(new)
  followerCount          Int?
  isUserFollowBusiness   Boolean?
  isBusinessFollowUser   Boolean?
  isVerifiedUser         Boolean?
  priority               Int                     @default(3)
  conversationSource     ConversationSource      @default(extension)
  nextMessageAt          DateTime?
  attackListStatus       String?                 @default("pending")
  // New fields for Chrome extension workflow
  batchMessageStatus     String?                 @default("pending") // pending, to_send, sent, failed
  currentMessageSequence Int?                    @default(1) // Which message in sequence (1, 2, 3)
  lastMessageSentAt      DateTime? // When last message was sent
  batchId                String? // Which batch this contact is using
  // New fields for duplicate prevention
  firstMessageHash       String?                 @db.VarChar(64) // SHA256 hash of first message sent
  messagesSentCount      Int                     @default(0) // Total messages sent (batches count as 1)
  lastMessageSentHash    String?                 @db.VarChar(64) // SHA256 hash of last message sent
  Organization           Organization            @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  User                   User                    @relation(fields: [userId], references: [id], onDelete: Cascade)
  InstagramConversation  InstagramConversation[]
  InstagramMessage       InstagramMessage[]
  StageChangeLog         StageChangeLog[]
  MessageList            MessageList[]
  AttackList             AttackList[]

  @@index([instagramNickname])
  @@index([stage])
  @@index([userId])
  @@index([organizationId])
  @@index([firstMessageHash])
}

model InstagramFollower {
  id                      String                    @id @default(uuid()) @db.Uuid
  userId                  String                    @db.Uuid
  organizationId          String                    @db.Uuid
  instagramNickname       String
  instagramId             String?
  avatar                  String?                   @db.VarChar(2048)
  followerCount           Int?
  isVerified              Boolean                   @default(false)
  batchNumber             Int                       @default(1)
  priority                InstagramFollowerPriority @default(normal)
  status                  InstagramFollowerStatus   @default(pending)
  isTargeted              Boolean                   @default(false)
  lastResponseAt          DateTime?
  conversationStartedAt   DateTime?
  followUpCount           Int                       @default(0)
  lastFollowUpAt          DateTime?
  nextFollowUpAt          DateTime?
  isConversationActive    Boolean                   @default(false)
  automationEnabled       Boolean                   @default(true)
  conversationId          String?
  isContacted             Boolean                   @default(false)
  notes                   String?
  createdAt               DateTime                  @default(now())
  updatedAt               DateTime                  @updatedAt
  Organization            Organization              @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  User                    User                      @relation(fields: [userId], references: [id], onDelete: Cascade)
  FollowerProcessingQueue FollowerProcessingQueue[]

  @@index([instagramNickname])
  @@index([status])
  @@index([priority])
  @@index([batchNumber])
  @@index([userId])
  @@index([organizationId])
  @@index([automationEnabled])
}

model AboutMeConfig {
  id                   String               @id @db.Uuid
  botSettingsId        String               @unique @db.Uuid
  aboutMePrompt        String
  createdAt            DateTime             @default(now())
  updatedAt            DateTime
  InstagramBotSettings InstagramBotSettings @relation(fields: [botSettingsId], references: [id], onDelete: Cascade)
}

model BotClosingLevel {
  id            String   @id @db.Uuid
  levelNumber   Int
  name          String
  description   String?
  promptContent String
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime
}

model BotMode {
  id                   String                 @id @db.Uuid
  name                 String
  description          String?
  promptTemplate       String
  isActive             Boolean                @default(true)
  createdAt            DateTime               @default(now())
  updatedAt            DateTime
  InstagramBotSettings InstagramBotSettings[]
}

model BotRule {
  id                   String               @id @db.Uuid
  botSettingsId        String               @db.Uuid
  ruleType             String
  ruleContent          String
  isActive             Boolean              @default(true)
  createdAt            DateTime             @default(now())
  updatedAt            DateTime
  InstagramBotSettings InstagramBotSettings @relation(fields: [botSettingsId], references: [id], onDelete: Cascade)

  @@index([botSettingsId])
}

model GlobalPrompt {
  id            String   @id @db.Uuid
  name          String
  promptContent String
  promptType    String
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime
}

model AdminPrompt {
  id                          String   @id @default(uuid()) @db.Uuid
  generalPrompt               String
  technicalPrompt             String
  conversationGatheringPrompt String?
  createdAt                   DateTime @default(now())
  updatedAt                   DateTime @updatedAt
}

model AdminSettings {
  id                      String   @id @default(uuid()) @db.Uuid
  cacheForAllUsers        Boolean  @default(false)
  cacheType               String   @default("5m")
  enableConversationCache Boolean  @default(true)
  maxConversationMessages Int      @default(200)
  aiProvider              String   @default("claude")
  openrouterApiKey        String?
  openrouterModel         String?
  useReasoning            Boolean  @default(false)
  reasoningBudget         Int      @default(4000)
  createdAt               DateTime @default(now())
  updatedAt               DateTime @updatedAt

  @@index([aiProvider])
}

model BotStyle {
  id           String         @id @default(uuid()) @db.Uuid
  name         String
  description  String?
  promptText   String
  isDefault    Boolean        @default(false)
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt
  PromptConfig PromptConfig[]
  TestSettings TestSettings[]
}

model PromptConfig {
  id                     String       @id @default(uuid()) @db.Uuid
  organizationId         String       @db.Uuid
  aboutUs                String?
  qualificationQuestions String?
  additionalInfo         String?
  botStyleId             String?      @db.Uuid
  youtubeLink            String?
  websiteLink            String?
  leadMagnetLink         String?
  conversionLink         String
  createdAt              DateTime     @default(now())
  updatedAt              DateTime     @updatedAt
  botStyle               BotStyle?    @relation(fields: [botStyleId], references: [id])
  organization           Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId])
  @@index([botStyleId])
}

model InstagramBotSettings {
  id                    String                  @id @db.Uuid
  userId                String                  @db.Uuid
  organizationId        String                  @db.Uuid
  instagramToken        String
  instagramAccountId    String
  status                InstagramBotStatus      @default(disabled)
  responseTimeMin       Int                     @default(10)
  responseTimeMax       Int                     @default(20)
  botModeId             String                  @db.Uuid
  createdAt             DateTime                @default(now())
  updatedAt             DateTime
  AboutMeConfig         AboutMeConfig?
  BotRule               BotRule[]
  BotMode               BotMode                 @relation(fields: [botModeId], references: [id])
  Organization          Organization            @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  User                  User                    @relation(fields: [userId], references: [id], onDelete: Cascade)
  LeadForm              LeadForm[]
  LeadMagnet            LeadMagnet[]
  QualificationQuestion QualificationQuestion[]

  @@unique([userId, organizationId])
  @@index([organizationId])
  @@index([userId])
}

model InstagramConversation {
  id                 String           @id @db.Uuid
  contactId          String           @db.Uuid
  messageType        MessageType
  messageContent     String
  instagramMessageId String?
  sentAt             DateTime
  createdAt          DateTime         @default(now())
  InstagramContact   InstagramContact @relation(fields: [contactId], references: [id], onDelete: Cascade)

  @@index([contactId])
}

model InstagramMessage {
  id               String               @id @default(uuid()) @db.Uuid
  contactId        String               @db.Uuid
  messageId        String
  content          String
  isFromUser       Boolean              @default(true)
  isFromExtension  Boolean              @default(false)
  messageType      InstagramMessageType @default(RECEIVED) // RECEIVED, AI_SENT, MANUAL_SENT
  isEcho           Boolean              @default(false) // True for webhook echo messages
  mediaUrl         String?
  mediaType        String?
  mediaDescription String?
  timestamp        DateTime             @default(now())
  createdAt        DateTime             @default(now())
  InstagramContact InstagramContact     @relation(fields: [contactId], references: [id], onDelete: Cascade)

  @@index([contactId])
  @@index([messageId])
  @@index([messageType])
}

model InstagramSettings {
  id                      String       @id @default(uuid()) @db.Uuid
  organizationId          String       @unique @db.Uuid
  isBotEnabled            Boolean      @default(false)
  minResponseTime         Int          @default(30)
  maxResponseTime         Int          @default(50)
  messageDelayMin         Int          @default(3)
  messageDelayMax         Int          @default(5)
  instagramToken          String?
  instagramAccountId      String?
  instagramWebhookId      String? // Instagram Business Account ID that webhooks actually use
  instagramUsername       String?
  instagramName           String?
  instagramProfilePicture String?
  isConnected             Boolean      @default(false)
  tokenExpiresAt          DateTime?
  createdAt               DateTime     @default(now())
  updatedAt               DateTime     @updatedAt
  followUpCleanupDays     Int?         @default(30)
  autoCleanupEnabled      Boolean?     @default(true)
  Organization            Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId])
  @@index([instagramWebhookId])
}

model InstagramConversationsNotGathered {
  id                      String       @id @default(uuid()) @db.Uuid
  organizationId          String       @db.Uuid
  instagramConversationId String       @unique
  participantUsername     String
  participantId           String
  updatedTime             DateTime
  isGathered              Boolean      @default(false)
  createdAt               DateTime     @default(now())
  updatedAt               DateTime     @updatedAt
  Organization            Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId])
  @@index([participantUsername])
  @@index([participantId])
  @@index([isGathered])
  @@index([updatedTime])
}

model InstagramFollowerProcessingState {
  id                    String       @id @default(uuid()) @db.Uuid
  organizationId        String       @unique @db.Uuid
  lastProcessedPosition Int          @default(0) // Position in follower list where we stopped
  totalFollowersCount   Int? // Total number of followers
  isCompleted           Boolean      @default(false) // True when reached end of followers
  lastProcessedAt       DateTime? // When last batch was processed
  createdAt             DateTime     @default(now())
  updatedAt             DateTime     @updatedAt
  Organization          Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId])
}

model LeadForm {
  id                   String               @id @db.Uuid
  botSettingsId        String               @db.Uuid
  name                 String
  formFields           Json
  successMessage       String?
  createdAt            DateTime             @default(now())
  updatedAt            DateTime
  InstagramBotSettings InstagramBotSettings @relation(fields: [botSettingsId], references: [id], onDelete: Cascade)

  @@index([botSettingsId])
}

model LeadMagnet {
  id                   String               @id @db.Uuid
  botSettingsId        String               @db.Uuid
  name                 String
  description          String?
  contentUrl           String?
  triggerConditions    Json?
  createdAt            DateTime             @default(now())
  updatedAt            DateTime
  InstagramBotSettings InstagramBotSettings @relation(fields: [botSettingsId], references: [id], onDelete: Cascade)

  @@index([botSettingsId])
}

model QualificationQuestion {
  id                   String               @id @db.Uuid
  botSettingsId        String               @db.Uuid
  question             String
  orderNumber          Int
  isRequired           Boolean              @default(true)
  createdAt            DateTime             @default(now())
  updatedAt            DateTime
  InstagramBotSettings InstagramBotSettings @relation(fields: [botSettingsId], references: [id], onDelete: Cascade)

  @@index([botSettingsId])
}

model MessageQueue {
  id             String       @id @default(uuid()) @db.Uuid
  organizationId String       @db.Uuid
  senderId       String
  messageId      String
  messageText    String
  mediaUrl       String?
  mediaType      String?
  priority       Int          @default(1)
  scheduledAt    DateTime
  attempts       Int          @default(0)
  maxAttempts    Int          @default(3)
  status         QueueStatus  @default(pending)
  errorMessage   String?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  Organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([status, scheduledAt])
  @@index([organizationId])
}

model FollowerProcessingQueue {
  id                String                   @id @default(uuid()) @db.Uuid
  organizationId    String                   @db.Uuid
  followerId        String                   @db.Uuid
  priority          Int                      @default(1)
  scheduledAt       DateTime                 @default(now())
  attempts          Int                      @default(0)
  maxAttempts       Int                      @default(3)
  status            FollowerProcessingStatus @default(pending)
  errorMessage      String?
  hasConversation   Boolean                  @default(false)
  processingType    FollowerProcessingType   @default(batch)
  createdAt         DateTime                 @default(now())
  updatedAt         DateTime                 @updatedAt
  Organization      Organization             @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  InstagramFollower InstagramFollower        @relation(fields: [followerId], references: [id], onDelete: Cascade)

  @@index([status, scheduledAt])
  @@index([organizationId])
  @@index([followerId])
}

model InstagramError {
  id             String        @id @default(uuid()) @db.Uuid
  organizationId String?       @db.Uuid
  contactId      String?       @db.Uuid
  messageId      String?
  operation      String
  errorMessage   String
  errorStack     String?
  retryCount     Int           @default(0)
  resolved       Boolean       @default(false)
  createdAt      DateTime      @default(now())
  Organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId])
  @@index([createdAt])
  @@index([resolved])
}

model WebhookLog {
  id             String   @id @default(uuid()) @db.Uuid
  requestId      String   @unique
  method         String
  url            String
  headers        Json?
  body           Json?
  signature      String?
  isValid        Boolean
  responseCode   Int
  processingTime Int
  errorMessage   String?
  createdAt      DateTime @default(now())

  @@index([createdAt])
  @@index([isValid])
}

model StageChangeLog {
  id               String           @id @default(uuid()) @db.Uuid
  contactId        String           @db.Uuid
  fromStage        String?
  toStage          String
  reason           String?
  changedBy        String
  metadata         Json?
  createdAt        DateTime         @default(now())
  InstagramContact InstagramContact @relation(fields: [contactId], references: [id], onDelete: Cascade)

  @@index([contactId])
  @@index([createdAt])
  @@index([changedBy])
}

model TestSettings {
  id                     String       @id @default(uuid()) @db.Uuid
  organizationId         String       @db.Uuid
  userId                 String       @db.Uuid
  aboutUs                String?
  qualificationQuestions String?
  additionalInfo         String?
  botStyleId             String?      @db.Uuid
  youtubeLink            String?
  websiteLink            String?
  leadMagnetLink         String?
  conversionLink         String?
  createdAt              DateTime     @default(now())
  updatedAt              DateTime     @updatedAt
  botStyle               BotStyle?    @relation(fields: [botStyleId], references: [id])
  organization           Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user                   User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([organizationId, userId])
  @@index([organizationId])
  @@index([userId])
  @@index([botStyleId])
}

model ChromeExtensionSettings {
  id                       String       @id @default(uuid()) @db.Uuid
  organizationId           String       @unique @db.Uuid
  timeBetweenDMsMin        Int          @default(3) // minutes, 1-99
  timeBetweenDMsMax        Int          @default(8) // minutes, 1-99
  messagesBeforeBreakMin   Int          @default(8) // 1-99
  messagesBeforeBreakMax   Int          @default(15) // 1-99
  breakDurationMin         Int          @default(10) // minutes, 1-99
  breakDurationMax         Int          @default(20) // minutes, 1-99
  pauseStart               String       @default("00:30") // HH:MM format
  pauseStop                String       @default("07:00") // HH:MM format
  smartFocus               Boolean      @default(true)
  isConnected              Boolean      @default(false)
  lastConnectionAt         DateTime?
  extensionStatus          String       @default("FRESH_START") // Dynamic status: FRESH_START, SCRAPED_250, SCRAPED_500, etc., ALL_SCRAPED, ACTIVE, IDLE, STOPPED
  currentActivity          String? // What the extension is currently doing
  lastActivityAt           DateTime? // When the last activity occurred
  // Enhanced scraping tracking
  totalFollowersScraped    Int          @default(0) // Total followers scraped across all sessions
  lastScrapedPosition      Int          @default(0) // Position in follower list where we stopped
  lastScrapedUsernames     String[]     @default([]) // Last 10 scraped usernames for resume detection
  scrapingTargetReached    Boolean      @default(false) // Whether current batch target was reached
  allFollowersScraped      Boolean      @default(false) // Whether we've reached the end of all followers
  lastScrapingSession      DateTime? // When the last scraping session occurred
  // Scraping controls
  scrapingEnabled          Boolean      @default(true) // Whether automatic scraping is enabled
  scrapingIntervalMin      Int          @default(15) // Minutes between scraping sessions (minimum)
  scrapingIntervalMax      Int          @default(30) // Minutes between scraping sessions (maximum)
  scrapingIntervalDays     Int          @default(5) // Days to wait between scraping sessions (legacy)
  nextScrapingAllowedAt    DateTime? // When the next scraping session is allowed
  // New fields for message limits and continuous scraping
  dailyMessageLimit        Int? // NULL = unlimited, otherwise minimum 10
  messagesSentToday        Int          @default(0) // Counter for messages sent today
  lastMessageResetDate     DateTime     @default(now()) // Last date when counter was reset
  attackPastFollowers      Boolean      @default(true) // Toggle for follower scraping
  continuousScraping       Boolean      @default(true) // Enable continuous scraping when idle
  lastContinuousScrapingAt DateTime? // Track last continuous scraping time for 1-hour break
  createdAt                DateTime     @default(now())
  updatedAt                DateTime     @updatedAt
  Organization             Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId])
  @@index([lastMessageResetDate])
}

model TestAdminPrompt {
  id                          String       @id @default(uuid()) @db.Uuid
  organizationId              String       @db.Uuid
  userId                      String       @db.Uuid
  generalPrompt               String
  technicalPrompt             String
  conversationGatheringPrompt String?
  createdAt                   DateTime     @default(now())
  updatedAt                   DateTime     @updatedAt
  organization                Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user                        User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([organizationId, userId])
  @@index([organizationId])
  @@index([userId])
}

model MessageBatch {
  id               String             @id @default(uuid()) @db.Uuid
  organizationId   String             @db.Uuid
  userId           String             @db.Uuid
  name             String             @db.VarChar(255)
  isActive         Boolean            @default(true)
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt
  Organization     Organization       @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  User             User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  MessageBatchItem MessageBatchItem[]

  @@index([organizationId])
  @@index([userId])
}

model MessageBatchItem {
  id             String       @id @default(uuid()) @db.Uuid
  messageBatchId String       @db.Uuid
  messageText    String
  sequenceNumber Int
  delayMinutes   Int          @default(5)
  createdAt      DateTime     @default(now())
  MessageBatch   MessageBatch @relation(fields: [messageBatchId], references: [id], onDelete: Cascade)

  @@index([messageBatchId])
}

model FollowUpTemplate {
  id              String       @id @default(uuid()) @db.Uuid
  organizationId  String       @db.Uuid
  userId          String       @db.Uuid
  messageText     String
  sequenceNumber  Int
  variationNumber Int          @default(1)
  delayHours      Int          @default(24)
  isActive        Boolean      @default(true)
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt
  Organization    Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  User            User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([organizationId, sequenceNumber, variationNumber])
  @@index([organizationId])
  @@index([userId])
  @@index([sequenceNumber])
  @@index([variationNumber])
}

model MessageList {
  id               String             @id @default(uuid()) @db.Uuid
  organizationId   String             @db.Uuid
  contactId        String             @db.Uuid
  nickname         String             @db.VarChar(255)
  priority         Int                @default(3) // 1-5, contact priority
  messageType      MessageListType    @default(SYSTEM)
  scheduledTime    DateTime // when message should be sent
  messageContent   String             @db.Text
  status           MessageListStatus  @default(PENDING)
  handlerType      MessageListHandler @default(SYSTEM)
  sequenceNumber   Int                @default(1) // order in follow-up sequence
  sentAt           DateTime? // when message was sent
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt
  Organization     Organization       @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  InstagramContact InstagramContact   @relation(fields: [contactId], references: [id], onDelete: Cascade)
  AttackList       AttackList[]

  @@index([organizationId])
  @@index([scheduledTime])
  @@index([contactId])
  @@index([priority])
}

model AttackList {
  id               String           @id @default(uuid()) @db.Uuid
  organizationId   String           @db.Uuid
  contactId        String           @db.Uuid
  messageListId    String?          @db.Uuid // FK to MessageList, nullable
  nickname         String           @db.VarChar(255)
  priority         Int              @default(3) // 1-5, contact priority
  messageContent   String           @db.Text
  messageType      AttackListType   @default(FIRST_MESSAGE)
  sequenceNumber   Int              @default(1)
  addedAt          DateTime         @default(now()) // when added to attack list
  status           AttackListStatus @default(READY)
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt
  Organization     Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  InstagramContact InstagramContact @relation(fields: [contactId], references: [id], onDelete: Cascade)
  MessageList      MessageList?     @relation(fields: [messageListId], references: [id], onDelete: SetNull)

  @@index([organizationId])
  @@index([priority])
  @@index([addedAt])
  @@index([contactId])
  @@index([messageListId])
}

enum ActionType {
  CREATE @map("create")
  UPDATE @map("update")
  DELETE @map("delete")
}

enum ActorType {
  SYSTEM @map("system")
  MEMBER @map("member")
  API    @map("api")
}

enum FeedbackCategory {
  SUGGESTION @map("suggestion")
  PROBLEM    @map("problem")
  QUESTION   @map("question")
}

enum InvitationStatus {
  PENDING  @map("pending")
  ACCEPTED @map("accepted")
  REVOKED  @map("revoked")
}

enum Role {
  MEMBER @map("member")
  ADMIN  @map("admin")
}

enum WebhookTrigger {
  INSTAGRAM_MESSAGE_RECEIVED @map("instagramMessageReceived")
}

enum InstagramBotStatus {
  active
  paused
  disabled
}

enum InstagramContactStage {
  new
  initial
  engaged
  qualified
  formsent
  disqualified
  converted
  blocked
  suspicious
}

enum MessageType {
  incoming
  outgoing
}

enum QueueStatus {
  pending
  processing
  completed
  failed
  retrying
}

enum InstagramFollowerPriority {
  low
  normal
  high
  urgent
}

enum InstagramFollowerStatus {
  pending
  contacted
  responded
  engaged
  converted
  ignored
  blocked
}

enum ConversationSource {
  extension
  api
}

enum FollowerProcessingStatus {
  pending
  processing
  completed
  failed
  retrying
}

enum FollowerProcessingType {
  batch
  conversation
}

enum InstagramMessageType {
  RECEIVED // User sent message to business
  AI_SENT // AI/Bot sent message to user
  MANUAL_SENT // Manual message sent from Instagram
}

enum MessageListType {
  SYSTEM
  EXTENSION
}

enum MessageListStatus {
  PENDING
  PROCESSING
  SENT
  FAILED
  EXTERNAL
}

enum MessageListHandler {
  SYSTEM
  EXTENSION
}

enum AttackListType {
  FIRST_MESSAGE
  FOLLOW_UP
}

enum AttackListStatus {
  READY
  SENT
  FAILED
}
