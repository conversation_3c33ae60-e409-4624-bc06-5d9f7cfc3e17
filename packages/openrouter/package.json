{"name": "@workspace/openrouter", "version": "0.0.0", "private": true, "main": "./src/index.js", "types": "./src/index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint . --fix", "type-check": "tsc --noEmit"}, "dependencies": {"axios": "^1.6.7", "zod": "3.24.2"}, "devDependencies": {"@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "eslint": "9.21.0", "typescript": "5.7.2"}}