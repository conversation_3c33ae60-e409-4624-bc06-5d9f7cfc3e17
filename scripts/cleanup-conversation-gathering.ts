#!/usr/bin/env ts-node

/**
 * Complete Conversation Gathering Cleanup Script
 * This script removes all conversation gathering functionality from the system
 * 
 * Usage: npx ts-node scripts/cleanup-conversation-gathering.ts
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const prisma = new PrismaClient();
import { promises as fs } from 'fs';
import path from 'path';

const DRY_RUN = false; // Set to true to see what would be done without actually doing it

async function main() {
  console.log('🧹 Starting Complete Conversation Gathering Cleanup');
  console.log(`Mode: ${DRY_RUN ? 'DRY RUN (no changes will be made)' : 'LIVE CLEANUP'}`);
  console.log('='.repeat(60));

  try {
    await step1DatabaseCleanup();
    await step2FileRemoval();
    await step3CodeCleanup();
    console.log('\n🎉 Cleanup completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Run: npx prisma generate');
    console.log('2. Test the application');
    console.log('3. Create database migration to drop InstagramConversationsNotGathered table');
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
    process.exit(1);
  }
}

async function step1DatabaseCleanup() {
  console.log('\n📋 Step 1: Database Cleanup');
  console.log('-'.repeat(30));

  try {
    // Count records before cleanup
    const conversationCount = await prisma.instagramConversationsNotGathered.count();
    console.log(`Found ${conversationCount} conversation records to remove`);

    const queueItemsWithConversations = await prisma.followerProcessingQueue.count({
      where: { hasConversation: true }
    });
    console.log(`Found ${queueItemsWithConversations} queue items with conversation flags`);

    if (!DRY_RUN) {
      // Clear all conversation gathering data
      await prisma.instagramConversationsNotGathered.deleteMany({});
      console.log('✅ Cleared all conversation records');

      // Reset queue items to remove conversation flags
      const queueUpdate = await prisma.followerProcessingQueue.updateMany({
        data: {
          hasConversation: false,
          processingType: 'batch'
        }
      });
      console.log(`✅ Updated ${queueUpdate.count} queue items (removed conversation flags)`);

      // Reset extension status if needed
      const extensionUpdate = await prisma.chromeExtensionSettings.updateMany({
        where: { 
          extensionStatus: { in: ['CONVERSATIONS_GATHERING', 'CONVERSATIONS_GATHERED_READY'] }
        },
        data: {
          extensionStatus: 'READY',
          currentActivity: 'Ready for follower processing (conversation gathering removed)',
          lastActivityAt: new Date()
        }
      });
      console.log(`✅ Updated ${extensionUpdate.count} extension settings`);
    } else {
      console.log(`[DRY RUN] Would clear ${conversationCount} conversation records`);
      console.log(`[DRY RUN] Would update ${queueItemsWithConversations} queue items`);
    }
  } catch (error) {
    console.error('❌ Database cleanup failed:', error);
    throw error;
  }
}

async function step2FileRemoval() {
  console.log('\n🗑️ Step 2: File Removal');
  console.log('-'.repeat(25));

  const filesToRemove = [
    // Core conversation gathering files
    'apps/dashboard/lib/instagram-conversation-gathering.ts',
    'apps/dashboard/lib/conversation-processor.ts',
    
    // API endpoints
    'apps/dashboard/app/api/conversation-gathering',
    'apps/dashboard/app/api/fix-stuck-conversations/route.ts',
    
    // Test files
    'apps/dashboard/app/api/test/conversation-gathering-comprehensive/route.ts',
    'apps/dashboard/app/api/test/conversation-gathering-flow/route.ts', 
    'apps/dashboard/app/api/test/process-stuck-conversations/route.ts',
    
    // Documentation
    'docs/instagram-conversation-gathering-optimization.md',
    'docs/instagram-conversation-gathering-fix.md',
    
    // Components (if they exist)
    'apps/dashboard/components/organizations/slug/instagram/conversation-gathering-status.tsx'
  ];

  for (const filePath of filesToRemove) {
    try {
      const fullPath = path.join(process.cwd(), filePath);
      const exists = await fs.access(fullPath).then(() => true).catch(() => false);
      
      if (exists) {
        if (!DRY_RUN) {
          const stats = await fs.stat(fullPath);
          if (stats.isDirectory()) {
            await fs.rm(fullPath, { recursive: true, force: true });
            console.log(`✅ Removed directory: ${filePath}`);
          } else {
            await fs.unlink(fullPath);
            console.log(`✅ Removed file: ${filePath}`);
          }
        } else {
          console.log(`[DRY RUN] Would remove: ${filePath}`);
        }
      } else {
        console.log(`⚠️ Not found (skipping): ${filePath}`);
      }
    } catch (error) {
      console.error(`❌ Failed to remove ${filePath}:`, error.message);
    }
  }
}

async function step3CodeCleanup() {
  console.log('\n✂️ Step 3: Code Cleanup');
  console.log('-'.repeat(22));

  const codesToClean = [
    {
      file: 'apps/dashboard/lib/follower-processing-queue.ts',
      description: 'Remove conversation processor imports and cleanup logic'
    },
    {
      file: 'apps/dashboard/app/api/instagram/oauth/callback/route.ts',
      description: 'Remove conversation gathering trigger'
    },
    {
      file: 'apps/dashboard/app/api/chrome-extension/process-followers/route.ts',
      description: 'Remove conversation gathering and matching logic'
    },
    {
      file: 'apps/dashboard/lib/instagram-follower-trigger.ts',
      description: 'Remove conversation-related processing'
    }
  ];

  console.log('⚠️ Manual code cleanup required for these files:');
  for (const item of codesToClean) {
    console.log(`📝 ${item.file}`);
    console.log(`   → ${item.description}`);
  }

  console.log('\nRefer to the detailed removal plan in:');
  console.log('📄 docs/conversation-gathering-complete-removal-plan.md');
}

// Execute if run directly
if (require.main === module) {
  main().catch(console.error);
}

export { main as cleanupConversationGathering };