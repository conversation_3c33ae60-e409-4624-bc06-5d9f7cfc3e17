#!/usr/bin/env tsx

/**
 * Fix Instagram Account ID Mismatch
 * 
 * This script fixes the common issue where OAuth stores the Instagram User ID
 * but webhooks use the Instagram Business Account ID.
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const prisma = new PrismaClient();

async function fixInstagramAccountId() {
  console.log('🔧 Fixing Instagram Account ID mismatch...\n');

  try {
    // Find all Instagram settings
    const instagramSettings = await prisma.instagramSettings.findMany({
      include: {
        Organization: {
          select: {
            slug: true,
            name: true
          }
        }
      }
    });

    console.log(`Found ${instagramSettings.length} Instagram account(s) to check:`);

    for (const settings of instagramSettings) {
      console.log(`\n📋 Checking: ${settings.Organization.name} (@${settings.instagramUsername})`);
      console.log(`   Current stored ID: ${settings.instagramAccountId}`);

      if (!settings.instagramToken) {
        console.log('   ⚠️ No access token - skipping');
        continue;
      }

      try {
        // Get current account info from Instagram API
        const response = await fetch(
          `https://graph.instagram.com/v22.0/me?fields=id,username&access_token=${settings.instagramToken}`,
          { method: 'GET' }
        );

        if (!response.ok) {
          console.log(`   ❌ Failed to fetch account info: ${response.status}`);
          continue;
        }

        const accountInfo = await response.json();
        const currentApiId = accountInfo.id;

        console.log(`   API returned ID: ${currentApiId}`);

        // Check if we need to update
        if (settings.instagramAccountId !== currentApiId) {
          console.log(`   🔄 Updating Instagram Account ID...`);
          
          await prisma.instagramSettings.update({
            where: {
              id: settings.id
            },
            data: {
              instagramAccountId: currentApiId,
              updatedAt: new Date()
            }
          });

          console.log(`   ✅ Updated: ${settings.instagramAccountId} → ${currentApiId}`);
        } else {
          console.log(`   ✅ ID is already correct`);
        }

      } catch (error) {
        console.log(`   ❌ Error fetching account info:`, error);
      }
    }

    console.log('\n🎉 Fix completed!');
    console.log('\n📋 Summary:');
    console.log('   - This script updated Instagram Account IDs to match what webhooks expect');
    console.log('   - Your webhook routing should now work correctly');
    console.log('   - Test by sending a message to your Instagram account');

  } catch (error) {
    console.error('❌ Error during fix:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the fix
if (require.main === module) {
  fixInstagramAccountId()
    .then(() => {
      console.log('\n✅ Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script failed:', error);
      process.exit(1);
    });
}

export { fixInstagramAccountId };