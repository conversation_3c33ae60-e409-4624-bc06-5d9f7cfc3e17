#!/usr/bin/env tsx

/**
 * Apply Instagram Webhook ID Migration
 * 
 * This script applies the migration that adds the instagramWebhookId field
 * and regenerates the Prisma client to fix TypeScript errors.
 */

import { execSync } from 'child_process';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function applyMigration() {
  console.log('🔧 Applying Instagram Webhook ID migration...\n');

  try {
    console.log('📋 Step 1: Applying database migration...');
    
    // Navigate to database package and apply migration
    process.chdir('packages/database');
    
    // Apply the migration
    execSync('pnpm prisma migrate deploy', { stdio: 'inherit' });
    
    console.log('✅ Migration applied successfully!\n');
    
    console.log('📋 Step 2: Regenerating Prisma client...');
    
    // Generate the new Prisma client
    execSync('pnpm prisma generate', { stdio: 'inherit' });
    
    console.log('✅ Prisma client regenerated successfully!\n');
    
    console.log('🎉 All done! TypeScript errors should now be resolved.');
    console.log('💡 Your webhook handler now supports both OAuth and webhook IDs.');
    
  } catch (error) {
    console.error('❌ Error during migration:', error);
    process.exit(1);
  }
}

// Run the migration
applyMigration()
  .then(() => {
    console.log('\n✅ Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });