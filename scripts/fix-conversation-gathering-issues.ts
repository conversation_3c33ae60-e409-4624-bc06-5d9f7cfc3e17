#!/usr/bin/env tsx

/**
 * ROBUST FIX: Disable Automatic Conversation Gathering
 * 
 * This script fixes all issues preventing the 50-follower system from working:
 * 1. Disables automatic conversation gathering triggers
 * 2. Fixes stuck conversation gathering status
 * 3. Forces extension to ACTIVE state for immediate messaging
 * 4. Provides fallback for simple follower processing
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const prisma = new PrismaClient();

async function fixConversationGatheringIssues() {
  console.log('🔧 ROBUST FIX: Disabling automatic conversation gathering triggers...\n');

  try {
    // 1. Fix all stuck extension statuses
    console.log('1️⃣ Fixing stuck extension statuses...');
    const stuckExtensions = await prisma.chromeExtensionSettings.findMany({
      where: {
        extensionStatus: 'CONVERSATIONS_GATHERING'
      },
      include: {
        Organization: {
          select: { name: true, slug: true }
        }
      }
    });

    for (const ext of stuckExtensions) {
      console.log(`   🔄 Fixing ${ext.Organization.name} (${ext.Organization.slug})`);
      
      await prisma.chromeExtensionSettings.update({
        where: { id: ext.id },
        data: {
          extensionStatus: 'ACTIVE',
          currentActivity: 'Ready for messaging - conversation gathering disabled',
          lastActivityAt: new Date()
        }
      });
      
      console.log(`   ✅ Status updated to ACTIVE`);
    }

    // 2. Stop any running conversation gathering processes
    console.log('\n2️⃣ Stopping running conversation gathering processes...');
    const gatheringProcesses = await prisma.instagramConversationsNotGathered.groupBy({
      by: ['organizationId'],
      where: {
        isGathered: false
      },
      _count: {
        id: true
      }
    });

    for (const process of gatheringProcesses) {
      console.log(`   🛑 Found ${process._count.id} pending conversations for org: ${process.organizationId}`);
      
      // Mark all as gathered to stop processing
      await prisma.instagramConversationsNotGathered.updateMany({
        where: {
          organizationId: process.organizationId,
          isGathered: false
        },
        data: {
          isGathered: true
        }
      });
      
      console.log(`   ✅ Marked ${process._count.id} conversations as completed`);
    }

    // 3. Process any pending followers without conversation requirements
    console.log('\n3️⃣ Processing pending followers with simple system...');
    const pendingFollowers = await prisma.instagramFollower.findMany({
      where: {
        status: 'pending',
        automationEnabled: true
      },
      include: {
        Organization: {
          select: { name: true, slug: true }
        }
      },
      take: 100 // Limit to avoid overwhelming
    });

    let processedCount = 0;
    const organizationStats = new Map<string, number>();

    for (const follower of pendingFollowers) {
      try {
        // Check if contact already exists
        const existingContact = await prisma.instagramContact.findFirst({
          where: {
            organizationId: follower.organizationId,
            instagramNickname: follower.instagramNickname
          }
        });

        if (existingContact) {
          // Just mark as contacted
          await prisma.instagramFollower.update({
            where: { id: follower.id },
            data: {
              status: 'contacted',
              isContacted: true,
              updatedAt: new Date()
            }
          });
        } else {
          // Create simple contact with batch messages (no conversation analysis)
          const messageBatches = await prisma.messageBatch.findMany({
            where: {
              organizationId: follower.organizationId,
              isActive: true
            },
            include: {
              MessageBatchItem: {
                orderBy: { sequenceNumber: 'asc' }
              }
            }
          });

          const batchesWithMessages = messageBatches.filter(
            batch => batch.MessageBatchItem.length > 0
          );

          const randomBatch = batchesWithMessages.length > 0
            ? batchesWithMessages[Math.floor(Math.random() * batchesWithMessages.length)]
            : null;

          await prisma.instagramContact.create({
            data: {
              organizationId: follower.organizationId,
              userId: follower.userId,
              instagramId: follower.instagramId,
              instagramNickname: follower.instagramNickname,
              avatar: follower.avatar,
              followerCount: follower.followerCount,
              isVerifiedUser: follower.isVerified,
              stage: 'new',
              priority: 3, // Standard priority for new followers
              status: 'pending',
              messageCount: 0,
              isIgnored: false,
              isTakeControl: false,
              isConversionLinkSent: false,
              nextMessageAt: new Date(), // Ready for immediate messaging
              attackListStatus: 'pending',
              conversationSource: 'extension',
              batchId: randomBatch?.id
            }
          });

          // Mark follower as contacted
          await prisma.instagramFollower.update({
            where: { id: follower.id },
            data: {
              status: 'contacted',
              isContacted: true,
              updatedAt: new Date()
            }
          });
        }

        processedCount++;
        const orgName = follower.Organization.name;
        organizationStats.set(orgName, (organizationStats.get(orgName) || 0) + 1);

      } catch (error) {
        console.log(`   ⚠️ Error processing ${follower.instagramNickname}: ${error}`);
      }
    }

    // 4. Summary
    console.log('\n🎉 ROBUST FIX COMPLETED!\n');
    console.log('📊 Summary:');
    console.log(`   - Fixed ${stuckExtensions.length} stuck extension statuses`);
    console.log(`   - Stopped ${gatheringProcesses.length} conversation gathering processes`);
    console.log(`   - Processed ${processedCount} pending followers`);
    
    if (organizationStats.size > 0) {
      console.log('\n📋 Followers processed by organization:');
      for (const [orgName, count] of organizationStats) {
        console.log(`   - ${orgName}: ${count} followers`);
      }
    }

    console.log('\n✅ System is now ready for simple 50-follower messaging!');
    console.log('\n📝 What happens now:');
    console.log('   1. Chrome extension scrapes 50 followers ✅');
    console.log('   2. Followers get processed WITHOUT conversation gathering ✅');
    console.log('   3. Contacts are created with batch messages ✅');
    console.log('   4. Extension moves to ACTIVE status for messaging ✅');
    console.log('   5. No more stuck conversation gathering! ✅');

  } catch (error) {
    console.error('❌ Error during fix:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the fix
if (require.main === module) {
  fixConversationGatheringIssues()
    .then(() => {
      console.log('\n🚀 All issues fixed! System ready for 50-follower messaging.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Fix failed:', error);
      process.exit(1);
    });
}

export { fixConversationGatheringIssues };