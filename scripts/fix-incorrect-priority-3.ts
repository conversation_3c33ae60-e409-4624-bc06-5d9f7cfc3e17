#!/usr/bin/env npx tsx
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Fix contacts that were incorrectly assigned priority 3 during conversation gathering
 * Priority 3 should ONLY be for new followers without conversation history
 */
async function fixIncorrectPriority3() {
  try {
    console.log('🔍 Finding contacts with incorrect priority 3...');

    // Find contacts that have priority 3 but also have messages (indicating they have conversation history)
    const incorrectContacts = await prisma.instagramContact.findMany({
      where: {
        priority: 3,
        OR: [
          { messageCount: { gt: 0 } }, // Has messages
          { conversationSource: 'api' }, // From conversation gathering
          { 
            InstagramFollowUp: {
              some: {
                status: 'external' // Has AI-generated follow-ups from conversation gathering
              }
            }
          }
        ]
      },
      include: {
        InstagramFollowUp: {
          where: {
            status: 'external'
          }
        },
        _count: {
          select: {
            InstagramMessage: true
          }
        }
      }
    });

    console.log(`Found ${incorrectContacts.length} contacts with incorrect priority 3`);

    if (incorrectContacts.length === 0) {
      console.log('✅ No contacts need fixing!');
      return;
    }

    // Fix each contact
    for (const contact of incorrectContacts) {
      const messageCount = contact._count.InstagramMessage;
      const hasAIFollowUps = contact.InstagramFollowUp.length > 0;
      
      // Determine new priority based on engagement
      let newPriority = 4; // Default to priority 4 (needs nurturing)
      
      if (contact.stage === 'engaged' || contact.stage === 'qualified') {
        newPriority = 2; // Engaged users
      } else if (contact.stage === 'disqualified' || messageCount === 0) {
        newPriority = 5; // Low engagement
      }

      console.log(`📝 Updating ${contact.instagramNickname}:`);
      console.log(`   - Current: Priority ${contact.priority}, Stage: ${contact.stage}`);
      console.log(`   - Messages: ${messageCount}, AI Follow-ups: ${hasAIFollowUps}`);
      console.log(`   - New Priority: ${newPriority}`);

      // Update the contact
      await prisma.instagramContact.update({
        where: { id: contact.id },
        data: {
          priority: newPriority,
          conversationSource: 'api', // Ensure it's marked as API source
          updatedAt: new Date()
        }
      });
    }

    console.log(`✅ Successfully fixed ${incorrectContacts.length} contacts`);

    // Show summary
    const summary = await prisma.instagramContact.groupBy({
      by: ['priority'],
      _count: {
        id: true
      },
      orderBy: {
        priority: 'desc'
      }
    });

    console.log('\n📊 Priority Distribution After Fix:');
    const priorityLabels = {
      5: 'Highly Engaged',
      4: 'Engaged',
      3: 'New Followers (no conversation)',
      2: 'Low Engaged',
      1: 'Follow-up'
    };

    for (const group of summary) {
      console.log(`   Priority ${group.priority} (${priorityLabels[group.priority as keyof typeof priorityLabels]}): ${group._count.id} contacts`);
    }

  } catch (error) {
    console.error('❌ Error fixing incorrect priority 3:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
fixIncorrectPriority3().catch(console.error);