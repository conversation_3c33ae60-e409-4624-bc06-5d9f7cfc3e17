// Content Script - Handles Instagram DOM interactions

console.log('AISetter content script loaded');

// Logging function for consistency with original extension
const logNow = (...all) => {
  try {
    const timestamp = new Date().toISOString();
    console.log(`${timestamp}: `, ...all);
  } catch (error) {
    console.warn('error :: ', error);
  }
};

// Initialize injected script for Instagram DM handling (like original extension)
logNow('Initializing content script with localStorage approach');

const script = document.createElement('script');
script.src = chrome.runtime.getURL('inject/ijsource-localstorage.js');
script.onload = () => {
  logNow('ijsource-localstorage.js loaded successfully');
  script.remove();
};
script.onerror = () => {
  logNow('Failed to load ijsource-localstorage.js');
  script.remove();
};
document.head.appendChild(script);

// Message listener from background script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  logNow('Content script received:', request.action);
  
  switch (request.action) {
    case 'PING':
      // Simple ping to check if content script is loaded
      sendResponse({ success: true });
      break;
      
    case 'OPEN_FOLLOWERS_MODAL':
      openFollowersModal(request.username).then(sendResponse);
      return true; // Keep message channel open
      
    case 'SCRAPE_FOLLOWERS':
      scrapeFollowers(request.count, request.username).then(sendResponse);
      return true; // Keep message channel open
      
    case 'SEND_MESSAGE':
      sendMessage(request.username, request.message, request.type).then(sendResponse);
      return true;
      
    case 'VISIT_USER_MESSAGES':
      visitUserMessages(request.username).then(sendResponse);
      return true;
      
    case 'CHECK_NEW_FOLLOWERS':
      checkNewFollowers().then(sendResponse);
      return true;
      
    case 'GET_INSTA_INBOX':
      getInstagramStories().then(sendResponse);
      return true;
      
    case 'CLOSE_MODAL':
      closeModal();
      sendResponse({ success: true });
      break;
      
    default:
      sendResponse({ success: false, error: 'Unknown action' });
  }
});

// Get Instagram stories from activity inbox (like original extension)
async function getInstagramStories() {
  try {
    logNow('Getting Instagram stories from activity inbox...');
    
    const csrf_token = await getCsrfToken();
    if (!csrf_token) {
      throw new Error('Could not get CSRF token');
    }

    const inboxResponse = await fetch('https://i.instagram.com/api/v1/news/inbox/', {
      credentials: 'include',
      headers: {
        accept: 'application/json, text/plain, */*',
        Referer: 'https://www.instagram.com/',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'x-asbd-id': '129477',
        'X-IG-App-ID': '936619743392459',
        'x-instagram-ajax': '1',
        'X-CSRFToken': csrf_token,
        'x-requested-with': 'XMLHttpRequest',
      },
      body: null,
      method: 'POST',
    });
    
    const inboxData = await inboxResponse.json();
    
    if (inboxData.status === 'ok') {
      let stories = [];
      let total_stories = [...inboxData.new_stories, ...inboxData.old_stories];
      
      for (let i = 0; i < total_stories.length; i++) {
        const story = total_stories[i];
        if (story.story_type === 101 && story.type === 3) {
          stories.push({
            id: story.args.profile_id,
            username: story.args.profile_name,
            profile_image: story.args.profile_image,
            type: 0,
            timestamp: story.args.timestamp,
          });
        }
      }
      
      logNow(`Found ${stories.length} follower stories`);
      return {
        status: 'success',
        data: stories,
      };
    } else {
      return {
        status: 'failed',
        message: 'Failed to fetch inbox',
      };
    }
  } catch (error) {
    logNow('Error getting Instagram stories:', error);
    return {
      status: 'failed',
      message: error.message,
    };
  }
}

// Get CSRF token from cookies
async function getCsrfToken() {
  try {
    // Try to get from document cookies first
    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === 'csrftoken' && value) {
        return value;
      }
    }
    
    // Fallback: request from background script
    const response = await chrome.runtime.sendMessage({
      action: 'GET_COOKIE',
      data: { url: 'https://www.instagram.com', name: 'csrftoken' }
    });
    
    return response;
  } catch (error) {
    logNow('Error getting CSRF token:', error);
    return null;
  }
}

// Visit user messages (EXACT copy from original working extension)
async function visitUserMessages(username) {
  logNow('visitUserMessages called', { username, currentUrl: window.location.href });
  
  // Wait for page to fully load
  await delay(3000);
  
  let message_button = null;
  
  // Extended selectors for different button types (same as original)
  const buttonSelectors = [
    'div[role="button"]',
    'button',
    'a[role="button"]',
    'header button',
    'header div[role="button"]',
    'article button',
    'article div[role="button"]',
    '[data-testid*="message"]',
    '[data-testid*="send"]'
  ];
  
  const allButtons = [];
  for (const selector of buttonSelectors) {
    const elements = document.querySelectorAll(selector);
    allButtons.push(...Array.from(elements));
  }
  
  // Remove duplicates
  const uniqueButtons = Array.from(new Set(allButtons));
  logNow(`Found ${uniqueButtons.length} potential buttons to check for "Message" text.`);

  // Log all button texts for debugging with more details (from original)
  const buttonTexts = uniqueButtons.map(btn => ({
    text: btn.innerText.trim(),
    ariaLabel: btn.getAttribute('aria-label'),
    title: btn.getAttribute('title'),
    className: btn.className,
    tagName: btn.tagName,
    testId: btn.getAttribute('data-testid')
  })).filter(btn => btn.text.length > 0 || btn.ariaLabel || btn.testId);
  
  logNow('All button details found:', buttonTexts);

  // Try multiple text patterns for different languages (same as original)
  const messagePatterns = [
    'message',
    'wiadomość',
    'napisz',
    'send message',
    'wyślij wiadomość',
    'dm',
    'direct',
    'chat'
  ];

  for (const btn of uniqueButtons) {
    const buttonText = btn.innerText.toLowerCase().trim();
    const ariaLabel = (btn.getAttribute('aria-label') || '').toLowerCase();
    const title = (btn.getAttribute('title') || '').toLowerCase();
    const testId = (btn.getAttribute('data-testid') || '').toLowerCase();
    
    // Check all text sources for message patterns
    const allTexts = [buttonText, ariaLabel, title, testId].join(' ');
    
    for (const pattern of messagePatterns) {
      if (allTexts.includes(pattern)) {
        message_button = btn;
        logNow('Found message button by pattern:', {
          pattern,
          buttonText,
          ariaLabel,
          title,
          testId,
          element: btn
        });
        break;
      }
    }
    
    if (message_button) break;
  }

  if (message_button) {
    await delay(1000);
    logNow('visitUserMessages: Attempting to click message_button:', message_button);
    
    // Try multiple click methods (EXACT copy from original)
    try {
      // Method 1: Regular click
      message_button.click();
      await delay(2000);
      
      // Check if navigation happened
      if (window.location.href.includes('/direct/')) {
        logNow('✅ Successfully navigated to DM page via regular click');
      } else {
        // Method 2: Dispatch click event
        logNow('Regular click failed, trying event dispatch');
        const clickEvent = new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          view: window
        });
        message_button.dispatchEvent(clickEvent);
        await delay(2000);
        
        if (window.location.href.includes('/direct/')) {
          logNow('✅ Successfully navigated to DM page via event dispatch');
        } else {
          // Method 3: Focus and Enter key (from original)
          logNow('Event dispatch failed, trying focus + Enter');
          message_button.focus();
          await delay(500);
          const enterEvent = new KeyboardEvent('keydown', {
            key: 'Enter',
            code: 'Enter',
            keyCode: 13,
            which: 13,
            bubbles: true
          });
          message_button.dispatchEvent(enterEvent);
          await delay(2000);
          
          if (window.location.href.includes('/direct/')) {
            logNow('✅ Successfully navigated to DM page via Enter key');
          } else {
            logNow('❌ All click methods failed - DM page not opened');
          }
        }
      }
    } catch (error) {
      logNow('❌ Error clicking message button:', error);
    }
    
    logNow('visitUserMessages: After message button interaction', { finalUrl: window.location.href });
  } else {
    logNow('❌ visitUserMessages: Direct message button not found. Trying three-dots menu approach...');
    
    // Look for three-dots menu (Options) button
    let optionsButton = null;
    
    // Look for options button by aria-label and SVG content (from original)
    for (const btn of uniqueButtons) {
      const ariaLabel = (btn.getAttribute('aria-label') || '').toLowerCase();
      const svgTitle = btn.querySelector('svg title')?.textContent?.toLowerCase() || '';
      
      if (ariaLabel.includes('opcje') || ariaLabel.includes('options') ||
          svgTitle.includes('opcje') || svgTitle.includes('options')) {
        optionsButton = btn;
        logNow('Found options button:', { ariaLabel, svgTitle, element: btn });
        break;
      }
    }
    
    if (optionsButton) {
      logNow('📱 Clicking options button to reveal message option...');
      try {
        optionsButton.click();
        await delay(2000); // Wait for menu to appear
        
        // Now look for the message button in the dropdown menu
        const newButtons = document.querySelectorAll('button, div[role="button"]');
        let messageButtonInMenu = null;
        
        for (const btn of newButtons) {
          const buttonText = btn.innerText.toLowerCase().trim();
          // Exact match for Polish "Wyślij wiadomość" (from original)
          if (buttonText === 'wyślij wiadomość' ||
              buttonText.includes('send message') ||
              buttonText.includes('message') ||
              buttonText === 'wiadomość') {
            messageButtonInMenu = btn;
            logNow('Found message button in dropdown menu:', {
              text: buttonText,
              exactText: btn.innerText,
              element: btn
            });
            break;
          }
        }
        
        if (messageButtonInMenu) {
          logNow('📱 Clicking message button from dropdown menu...');
          messageButtonInMenu.click();
          // Wait for navigation with retry logic (from original)
          let navigationSuccess = false;
          for (let attempt = 1; attempt <= 3; attempt++) {
            await delay(3000);
            
            if (window.location.href.includes('/direct/')) {
              logNow('✅ Successfully navigated to DM page via dropdown menu');
              navigationSuccess = true;
              break;
            } else {
              logNow(`❌ Attempt ${attempt}/3 - Dropdown menu click failed, URL: ${window.location.href}`);
              
              if (attempt < 3) {
                // Try alternative approach (from original)
                logNow('🔄 Retrying with direct message button approach...');
                
                const directMessageBtn = document.querySelector('div[aria-label*="Message"], div[aria-label*="Wiadomość"], a[aria-label*="Message"], a[aria-label*="Wiadomość"]');
                if (directMessageBtn) {
                  logNow('Found direct message button, clicking...');
                  directMessageBtn.click();
                } else {
                  // Try clicking the message button from dropdown again
                  const allButtons = document.querySelectorAll('button, div[role="button"]');
                  const retryMessageBtn = Array.from(allButtons).find(btn => {
                    const text = btn.textContent?.toLowerCase() || '';
                    return text.includes('message') || text.includes('wiadomość') || text.includes('wyślij wiadomość');
                  });
                  if (retryMessageBtn) {
                    logNow('🔄 Retrying with found message button:', retryMessageBtn.textContent);
                    retryMessageBtn.click();
                  }
                }
              }
            }
          }
          
          if (!navigationSuccess) {
            logNow('❌ Failed to navigate to DM page after 3 attempts via dropdown menu');
          }
        } else {
          logNow('❌ Message button not found in dropdown menu');
          
          // Log what buttons are available in the menu (from original)
          const menuButtons = Array.from(newButtons).map(btn => ({
            text: btn.innerText.trim(),
            ariaLabel: btn.getAttribute('aria-label'),
            className: btn.className
          })).filter(btn => btn.text.length > 0);
          
          logNow('Available buttons in dropdown menu:', menuButtons);
        }
      } catch (error) {
        logNow('❌ Error interacting with options menu:', error);
      }
    } else {
      logNow('❌ Options button not found either. Available button details:', buttonTexts);
    }
  }
  
  logNow('visitUserMessages completed', { username, finalUrl: window.location.href });
}

// Open followers modal for a specific username
async function openFollowersModal(username) {
  try {
    console.log(`Opening followers modal for @${username}...`);
    
    // Check if we're on the correct profile page
    if (!window.location.pathname.includes(`/${username}/`)) {
      return {
        success: false,
        error: `Not on profile page for @${username}. Current URL: ${window.location.href}`
      };
    }
    
    // Find and click followers button
    const followersButton = await findFollowersButton();
    if (!followersButton) {
      return {
        success: false,
        error: 'Could not find followers button on profile page'
      };
    }
    
    console.log('Found followers button, clicking...');
    followersButton.click();
    
    // Wait for modal to open
    await delay(2000);
    
    // Check if modal opened
    const modal = document.querySelector('[role="dialog"]');
    if (!modal) {
      return {
        success: false,
        error: 'Followers modal did not open'
      };
    }
    
    console.log('Followers modal opened successfully');
    return { success: true };
    
  } catch (error) {
    console.error('Error opening followers modal:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Scrape followers (simplified - username provided from API)
async function scrapeFollowers(count = 50, username = null) {
  try {
    console.log(`Starting to scrape ${count} followers for @${username}...`);
    
    if (!username) {
      throw new Error('Username is required');
    }
    
    // Check if modal is already open, if not it should be opened by background script
    const modal = document.querySelector('[role="dialog"]');
    if (!modal) {
      throw new Error('Followers modal is not open');
    }
    
    // Get followers from the modal
    const followers = await scrapeFollowersFromModal(count);
    
    // Close modal
    closeModal();
    
    console.log(`Scraped ${followers.length} followers`);
    return { success: true, followers };
    
  } catch (error) {
    console.error('Scraping error:', error);
    return { success: false, error: error.message };
  }
}

// Removed getProfileLink - we get username directly from API

// Find followers button (simplified)
async function findFollowersButton() {
  // Wait for profile to load
  await waitForElement('header section');
  
  // Look for followers link
  const followersLink = document.querySelector('a[href*="/followers/"]');
  if (followersLink) {
    return followersLink;
  }
  
  // Fallback: look for text containing "followers"
  const links = document.querySelectorAll('a');
  for (const link of links) {
    if (link.textContent.toLowerCase().includes('follower')) {
      return link;
    }
  }
  
  return null;
}

// Scrape followers from the modal
async function scrapeFollowersFromModal(targetCount) {
  const followers = new Set();
  let scrollContainer = null;
  let lastHeight = 0;
  let noNewFollowersCount = 0;
  
  // Find scroll container - look for element with overflow-y: scroll
  const modal = document.querySelector('[role="dialog"]');
  if (!modal) {
    console.error('Modal not found');
    return [];
  }
  
  // First try specific selectors
  const containerSelectors = [
    'div._aano', // Common Instagram modal class
    'div[style*="overflow-y: scroll"]',
    'div[style*="overflow: scroll"]',
    'div[class*="scroll"]',
  ];
  
  for (const selector of containerSelectors) {
    const element = modal.querySelector(selector);
    if (element && element.scrollHeight > element.clientHeight) {
      scrollContainer = element;
      console.log(`Found scroll container with selector: ${selector}`);
      break;
    }
  }
  
  // If not found, check all divs in modal for computed overflow-y: scroll
  if (!scrollContainer) {
    console.log('Checking all divs for overflow-y: scroll...');
    const allDivs = modal.querySelectorAll('div');
    for (const div of allDivs) {
      const computedStyle = window.getComputedStyle(div);
      if (computedStyle.overflowY === 'scroll' || computedStyle.overflow === 'scroll') {
        if (div.scrollHeight > div.clientHeight) {
          scrollContainer = div;
          console.log('Found scroll container by computed style');
          break;
        }
      }
    }
  }
  
  // Final fallback - find any scrollable element in modal
  if (!scrollContainer) {
    console.log('Using fallback - checking all elements for scrollability...');
    const allElements = modal.querySelectorAll('*');
    for (const element of allElements) {
      if (element.scrollHeight > element.clientHeight && element.scrollHeight > 100) {
        scrollContainer = element;
        console.log(`Found scrollable element: ${element.tagName}.${element.className}`);
        break;
      }
    }
  }
  
  if (!scrollContainer) {
    console.error('Could not find scroll container');
    return [];
  }
  
  // Scroll and collect followers
  while (followers.size < targetCount) {
    // Get current followers in view
    const followerElements = scrollContainer.querySelectorAll('a[href^="/"][role="link"]');
    
    for (const element of followerElements) {
      const href = element.href;
      if (href && href.match(/instagram\.com\/([^\/]+)\/?$/)) {
        const username = href.split('/').filter(Boolean).pop();
        if (username && !username.includes('?') && username !== 'explore') {
          followers.add(username);
        }
      }
      
      if (followers.size >= targetCount) {
        break;
      }
    }
    
    // Check if we got new followers
    if (scrollContainer.scrollHeight === lastHeight) {
      noNewFollowersCount++;
      if (noNewFollowersCount > 3) {
        console.log('No more followers to load');
        break;
      }
    } else {
      noNewFollowersCount = 0;
      lastHeight = scrollContainer.scrollHeight;
    }
    
    // Scroll down
    scrollContainer.scrollTop = scrollContainer.scrollHeight;
    await delay(1500); // Wait for new followers to load
  }
  
  return Array.from(followers).slice(0, targetCount);
}

// Close modal
function closeModal() {
  // Find close button
  const closeButtons = document.querySelectorAll('svg[aria-label="Close"], button[aria-label="Close"]');
  for (const button of closeButtons) {
    const clickable = button.closest('button') || button.parentElement;
    if (clickable) {
      clickable.click();
      return;
    }
  }
  
  // Fallback: press Escape
  document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape', keyCode: 27 }));
}

// Send message to user (enhanced like original extension)
async function sendMessage(username, message, type) {
  try {
    logNow('sendMessage called', { username, message, currentUrl: window.location.href });

    // Resolve username to user ID (like original extension)
    const recipient_id = await resolveUsernameToUserId(username);
    if (!recipient_id) {
      logNow('❌ Failed to resolve username to user ID:', username);
      throw new Error(`Failed to resolve username '${username}' to a user ID.`);
    }
    logNow('✅ Resolved username to user ID:', { username, recipient_id });

    let viewerId = 'unknown';
    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === 'ds_user_id' && value) {
        viewerId = value;
        break;
      }
    }
    logNow('Viewer ID from cookies:', viewerId);

    const urlMatch = window.location.pathname.match(/\/direct\/t\/(\d+)/);
    let threadId = urlMatch ? urlMatch[1] : 'unknown';
    logNow('Current URL analysis:', {
      pathname: window.location.pathname,
      urlMatch: urlMatch ? urlMatch[0] : null,
      threadId
    });
    
    if (threadId === 'unknown') {
      logNow('No existing thread found, creating new one for recipient:', recipient_id);
      const group_thread_resp = await createGroupThread(recipient_id);

      if (group_thread_resp && group_thread_resp.status === 'ok') {
        threadId = group_thread_resp.thread_id;
        logNow('✅ New thread created successfully:', threadId);
        
        // Wait a moment for thread to be fully created
        await delay(1000);
        logNow('✅ Thread creation completed, ready for messaging');
        
      } else {
        logNow('❌ Failed to create new thread:', group_thread_resp);
        throw new Error('Failed to create message thread');
      }
    } else {
      logNow('✅ Using existing thread:', threadId);
    }

    // Use the original extension's robust message sending approach
    const messagePromise = new Promise((resolve, reject) => {
      const messageHandler = (event) => {
        if (event.data && event.data.type === 'INJECT_DISPATCH_DM_RESPONSE') {
          window.removeEventListener('message', messageHandler);
          if (event.data.ret === 1) {
            logNow('Message sent successfully!');
            resolve(true);
          } else {
            logNow('Message failed:', event.data);
            reject(new Error(`Message failed: ${event.data.error || event.data.status_code}`));
          }
        }
      };

      window.addEventListener('message', messageHandler);

      window.postMessage({
        type: 'INJECT_DISPATCH_DM_REQUEST',
        thread_id: threadId,
        viewer_id: viewerId,
        user: { id: recipient_id, username: username },
        text: message,
        debug: false
      }, '*');

      setTimeout(() => {
        window.removeEventListener('message', messageHandler);
        reject(new Error('Message timeout'));
      }, 15000);
    });

    const result = await messagePromise;
    logNow('Message sent successfully via injected script');
    return { success: true };

  } catch (error) {
    logNow('Error in sendMessage:', error);
    console.error('Error in sendMessage:', error);
    return { success: false, error: error.message };
  }
}

// Resolve username to user ID (from original extension)
async function resolveUsernameToUserId(username) {
  try {
    logNow('Attempting to resolve username to user ID:', username);
    const profileUrl = `https://www.instagram.com/api/v1/users/web_profile_info/?username=${username}`;
    const response = await fetch(profileUrl, {
      credentials: 'include',
      headers: {
        'User-Agent': navigator.userAgent,
        'Accept': 'application/json',
        'x-ig-app-id': '936619743392459'
      }
    });
    
    if (response.ok) {
      const json = await response.json();
      const userId = json?.data?.user?.id;
      if (userId) {
        logNow(`Resolved ${username} to user ID: ${userId}`);
        return userId;
      }
    }
    logNow('Could not resolve username to user ID via web_profile_info API');
    return null;
  } catch (error) {
    logNow('Error in resolveUsernameToUserId:', error);
    return null;
  }
}

// Create group thread (EXACT copy from original extension)
async function createGroupThread(recipent_id) {
  let u = 'https://i.instagram.com/api/v1/direct_v2/create_group_thread/';
  let csrf_token = await chrome.runtime.sendMessage({
    type: 'GET_COOKIE',
    data: { url: 'https://www.instagram.com', name: 'csrftoken' },
  });
  const s = { recipient_users: '["'.concat(recipent_id, '"]') };
  const l = new URLSearchParams([...Object.entries(s)]).toString();

  let response = await fetch(u, {
    credentials: 'include',
    headers: {
      accept: 'application/json, text/plain, */*',
      'content-type': 'application/x-www-form-urlencoded',
      Referer: 'https://www.instagram.com/',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'x-asbd-id': '129477',
      'X-IG-App-ID': '936619743392459',
      'x-instagram-ajax': '1',
      'X-CSRFToken': csrf_token,
      'x-requested-with': 'XMLHttpRequest',
    },
    body: l,
    method: 'POST',
  });
  const data = await response.json();
  return data;
}

// Find Message button on profile
async function findMessageButton() {
  await waitForElement('header section');
  
  // Common selectors for Message button
  const selectors = [
    'div[role="button"]:has(div:contains("Message"))',
    'button:contains("Message")',
    'div[dir="auto"]:contains("Message")',
  ];
  
  // First try exact selectors
  for (const selector of ['button', 'div[role="button"]']) {
    const elements = document.querySelectorAll(selector);
    for (const element of elements) {
      if (element.textContent.trim() === 'Message') {
        return element;
      }
    }
  }
  
  // Fallback: look for any clickable with "Message" text
  const allElements = document.querySelectorAll('*');
  for (const element of allElements) {
    if (element.textContent.trim() === 'Message' && 
        (element.tagName === 'BUTTON' || element.getAttribute('role') === 'button')) {
      return element;
    }
  }
  
  return null;
}

// Find message input in DM
async function findMessageInput() {
  // Wait for DM page to load
  await waitForElement('textarea, input[placeholder*="Message"]', 5000);
  
  // Common selectors for message input
  const selectors = [
    'textarea[placeholder*="Message"]',
    'textarea[aria-label*="Message"]',
    'div[contenteditable="true"][aria-label*="Message"]',
    'div[contenteditable="true"][role="textbox"]',
  ];
  
  for (const selector of selectors) {
    const element = document.querySelector(selector);
    if (element) {
      return element;
    }
  }
  
  return null;
}

// Type message into input
async function typeMessage(input, text) {
  // Focus input
  input.focus();
  input.click();
  await delay(500);
  
  // Clear existing text
  input.textContent = '';
  input.value = '';
  
  // Type character by character for more natural behavior
  for (const char of text) {
    if (input.tagName === 'TEXTAREA' || input.tagName === 'INPUT') {
      input.value += char;
      input.dispatchEvent(new Event('input', { bubbles: true }));
    } else {
      // For contenteditable divs
      input.textContent += char;
      input.dispatchEvent(new Event('input', { bubbles: true }));
    }
    await delay(randomBetween(50, 150));
  }
  
  await delay(500);
}

// Send message in DM
async function sendMessageInDM() {
  // Find send button
  const sendSelectors = [
    'button[type="submit"]',
    'button:has(svg[aria-label*="Send"])',
    'div[role="button"]:has(svg)',
  ];
  
  for (const selector of sendSelectors) {
    const buttons = document.querySelectorAll(selector);
    for (const button of buttons) {
      // Check if it's likely a send button
      const svg = button.querySelector('svg');
      if (button.type === 'submit' || (svg && !svg.getAttribute('aria-label')?.includes('emoji'))) {
        button.click();
        await delay(1000);
        return;
      }
    }
  }
  
  // Fallback: press Enter
  const input = await findMessageInput();
  if (input) {
    input.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter', keyCode: 13 }));
  }
}

// Check for new followers in Activity Inbox (enhanced like original extension)
async function checkNewFollowers() {
  try {
    logNow('Checking for new followers via Activity Inbox...');
    
    // Use the same approach as original extension - get stories from API
    const result = await getInstagramStories();
    
    if (result.status === 'success' && result.data) {
      const stories = result.data;
      logNow(`Found ${stories.length} activity stories to process`);
      
      // Filter to new followers (story_type 101, type 3 indicates new followers)
      const newFollowers = stories.map(story => ({
        username: story.username,
        id: story.id,
        timestamp: story.timestamp,
        followedAt: new Date(story.timestamp * 1000)
      }));
      
      logNow(`Found ${newFollowers.length} new followers from activity inbox`);
      return {
        success: true,
        followers: newFollowers.map(f => f.username),
        data: newFollowers
      };
    } else {
      logNow('No new followers found or failed to fetch activity inbox');
      return { success: true, followers: [] };
    }
    
  } catch (error) {
    logNow('Error checking new followers:', error);
    return { success: false, error: error.message };
  }
}

// Helper functions
async function waitForElement(selector, timeout = 10000) {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    const element = document.querySelector(selector);
    if (element) {
      return element;
    }
    await delay(500);
  }
  
  throw new Error(`Element not found: ${selector}`);
}

async function waitForPageLoad() {
  return new Promise((resolve) => {
    if (document.readyState === 'complete') {
      resolve();
    } else {
      window.addEventListener('load', resolve);
      // Timeout after 10 seconds
      setTimeout(resolve, 10000);
    }
  });
}

function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function randomBetween(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Log that content script is ready
console.log('AISetter content script ready');