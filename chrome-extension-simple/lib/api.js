// API Service Module - Handles all communication with AISetter backend

const API_BASE_URL = 'https://app.aisetter.pl';

class ApiService {
  constructor() {
    this.apiKey = null;
    this.organizationId = null;
    this.organizationName = null;
  }

  // Set API key for all requests
  setApiKey(apiKey) {
    this.apiKey = apiKey;
  }

  // Helper method for API requests
  async request(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers
    };

    if (this.apiKey) {
      headers['X-API-Key'] = this.apiKey;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || `HTTP ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error(`API Error (${endpoint}):`, error);
      throw error;
    }
  }

  // Verify API key and get organization info
  async verifyApiKey(apiKey) {
    try {
      const tempApiKey = this.apiKey;
      this.apiKey = apiKey; // Temporarily set for this request
      
      const result = await this.request('/api/verify-api-key', {
        method: 'POST'
      });

      if (result.success && result.data) {
        this.organizationId = result.data.organizationId;
        this.organizationName = result.data.organizationName;
        this.apiKey = apiKey; // Keep the valid API key
        return {
          success: true,
          organizationId: result.data.organizationId,
          organizationName: result.data.organizationName
        };
      }

      this.apiKey = tempApiKey; // Restore previous key on failure
      return {
        success: false,
        error: result.message || 'Invalid API key'
      };
    } catch (error) {
      this.apiKey = null;
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Get Chrome extension settings
  async getSettings() {
    try {
      const result = await this.request('/api/chrome-extension/settings', {
        method: 'GET'
      });

      if (result.success) {
        return result.data;
      }

      throw new Error(result.message || 'Failed to get settings');
    } catch (error) {
      console.error('Error getting settings:', error);
      return null;
    }
  }

  // Get timing settings with caching
  async getTimingSettings() {
    try {
      // Check if we have cached settings that are less than 5 minutes old
      const cached = await this.getCachedTimingSettings();
      if (cached) {
        return cached;
      }

      const settings = await this.getSettings();
      if (settings) {
        const timingSettings = {
          timeBetweenDMsMin: settings.timeBetweenDMsMin || 2,  // 2 minutes (corrected from seconds)
          timeBetweenDMsMax: settings.timeBetweenDMsMax || 10, // 10 minutes (corrected from seconds)
          messagesBeforeBreakMin: settings.messagesBeforeBreakMin || 14,  // 14-23 messages before break
          messagesBeforeBreakMax: settings.messagesBeforeBreakMax || 23,
          breakDurationMin: settings.breakDurationMin || 10,   // 10 minutes break
          breakDurationMax: settings.breakDurationMax || 20,   // 20 minutes break
          scrapingIntervalMin: settings.scrapingIntervalMin || 15,
          scrapingIntervalMax: settings.scrapingIntervalMax || 30,
          monitoringInterval: 30 // Default 30 seconds for monitoring new followers
        };

        // Cache the settings
        await this.cacheTimingSettings(timingSettings);
        return timingSettings;
      }

      // Return default settings if API fails
      return this.getDefaultTimingSettings();
    } catch (error) {
      console.error('Error getting timing settings:', error);
      return this.getDefaultTimingSettings();
    }
  }

  // Get default timing settings
  getDefaultTimingSettings() {
    return {
      timeBetweenDMsMin: 2,   // 2 minutes (corrected from 8 seconds)
      timeBetweenDMsMax: 10,  // 10 minutes (corrected from 15 seconds)
      messagesBeforeBreakMin: 14, // 14-23 messages before break
      messagesBeforeBreakMax: 23,
      breakDurationMin: 10,   // 10 minutes
      breakDurationMax: 20,   // 20 minutes
      scrapingIntervalMin: 15,
      scrapingIntervalMax: 30,
      monitoringInterval: 30
    };
  }

  // Cache timing settings in Chrome storage
  async cacheTimingSettings(settings) {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      await chrome.storage.local.set({
        cachedTimingSettings: settings,
        timingCacheTimestamp: Date.now()
      });
    }
  }

  // Get cached timing settings
  async getCachedTimingSettings() {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      const result = await chrome.storage.local.get(['cachedTimingSettings', 'timingCacheTimestamp']);
      const cacheAge = Date.now() - (result.timingCacheTimestamp || 0);
      const maxCacheAge = 5 * 60 * 1000; // 5 minutes

      if (result.cachedTimingSettings && cacheAge < maxCacheAge) {
        console.log('Using cached timing settings');
        return result.cachedTimingSettings;
      }
    }
    return null;
  }

  // Get random delay between DMs based on backend settings
  async getRandomDelayBetweenDMs() {
    const settings = await this.getTimingSettings();
    const min = settings.timeBetweenDMsMin * 60000; // Convert MINUTES to milliseconds (was incorrectly * 1000)
    const max = settings.timeBetweenDMsMax * 60000; // Convert MINUTES to milliseconds (was incorrectly * 1000)
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  // Get random monitoring interval based on backend settings
  async getMonitoringInterval() {
    const settings = await this.getTimingSettings();
    return settings.monitoringInterval * 1000; // Convert to milliseconds
  }

  // Get delay between message lines within the same conversation batch
  // This is the only timing that can be hardcoded - natural typing delay within single conversation
  getDelayBetweenMessageLines() {
    // Short delay between lines in the same batch conversation (e.g., "HEJ" then "dzieki za follow")
    // This simulates natural typing and doesn't need to be configurable
    return Math.floor(Math.random() * (3000 - 1000 + 1)) + 1000; // 1-3 seconds
  }

  // Update extension status
  async updateStatus(status, currentActivity = null) {
    try {
      const data = {
        extensionStatus: status,
        isConnected: true,
        lastConnectionAt: new Date().toISOString()
      };

      if (currentActivity) {
        data.currentActivity = currentActivity;
      }

      const result = await this.request('/api/chrome-extension/status', {
        method: 'PUT',
        body: JSON.stringify(data)
      });

      return result.success;
    } catch (error) {
      console.error('Error updating status:', error);
      return false;
    }
  }

  // Send scraped followers to backend
  async sendFollowers(followers, isInitialScraping = false) {
    try {
      const data = {
        followers: followers,
        startPosition: 0,
        totalFollowers: followers.length,
        isComplete: true,
        isInitialScraping: isInitialScraping
      };

      const result = await this.request('/api/chrome-extension/process-followers', {
        method: 'POST',
        body: JSON.stringify(data)
      });

      if (!result.success) {
        throw new Error(result.error || 'Failed to send followers');
      }

      return {
        success: true,
        processed: result.data?.processed || followers.length,
        newFollowers: result.data?.newFollowers || 0,
        shouldContinueScraping: result.data?.shouldContinueScraping ?? true,
        duplicateRate: result.data?.duplicateRate || 0,
        recommendation: result.data?.recommendation || ''
      };
    } catch (error) {
      console.error('Error sending followers:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Send followers in batches with duplicate detection
  async sendFollowersBatch(followers, batchNumber = 1) {
    try {
      const data = {
        followers: followers,
        startPosition: (batchNumber - 1) * followers.length,
        totalFollowers: followers.length,
        isComplete: false, // Not complete since we're doing continuous scraping
        isInitialScraping: false
      };

      const result = await this.request('/api/chrome-extension/process-followers', {
        method: 'POST',
        body: JSON.stringify(data)
      });

      if (!result.success) {
        throw new Error(result.error || 'Failed to send followers batch');
      }

      return {
        success: true,
        processed: result.data?.processed || followers.length,
        newFollowers: result.data?.newFollowers || 0,
        shouldContinueScraping: result.data?.shouldContinueScraping ?? true,
        duplicateRate: result.data?.duplicateRate || 0,
        recommendation: result.data?.recommendation || ''
      };
    } catch (error) {
      console.error('Error sending followers batch:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Get messages to send (attack list v2)
  async getMessagesToSend() {
    try {
      if (!this.organizationId) {
        // Try to get organization ID from API key
        const verifyResult = await this.verifyApiKey(this.apiKey);
        if (!verifyResult.success) {
          throw new Error('Failed to verify API key');
        }
      }

      const result = await this.request(`/api/chrome-extension/attack-list-v2?organizationId=${this.organizationId}`, {
        method: 'GET'
      });

      if (result.success && result.data && result.data.messages) {
        return result.data.messages;
      }

      return [];
    } catch (error) {
      console.error('Error getting messages to send:', error);
      return [];
    }
  }

  // Mark message as sent (v2)
  async markMessageSent(messageId) {
    try {
      const data = {
        messageId: messageId,
        organizationId: this.organizationId
      };

      const result = await this.request('/api/chrome-extension/mark-sent-v2', {
        method: 'POST',
        body: JSON.stringify(data)
      });

      if (result.success) {
        return {
          success: true,
          nextMessage: result.data?.nextMessage || null
        };
      }

      return { success: false, error: result.error };
    } catch (error) {
      console.error('Error marking message sent:', error);
      return { success: false, error: error.message };
    }
  }

  // Check scraping eligibility
  async checkScrapingEligibility() {
    try {
      const result = await this.request('/api/chrome-extension/scraping-eligibility', {
        method: 'GET'
      });

      if (result.success && result.data) {
        return {
          isEligible: result.data.isEligible,
          extensionStatus: result.data.extensionStatus,
          reason: result.data.reason
        };
      }

      return {
        isEligible: false,
        extensionStatus: 'UNKNOWN',
        reason: 'Failed to check eligibility'
      };
    } catch (error) {
      console.error('Error checking scraping eligibility:', error);
      return {
        isEligible: false,
        extensionStatus: 'ERROR',
        reason: error.message
      };
    }
  }

  // Get extension status from backend
  async getExtensionStatus() {
    try {
      const result = await this.request('/api/chrome-extension/status', {
        method: 'GET'
      });

      if (result.success && result.data) {
        return result.data;
      }

      return null;
    } catch (error) {
      console.error('Error getting extension status:', error);
      return null;
    }
  }

  // Update scraping timestamp
  async updateScrapingTimestamp(followersScraped) {
    try {
      const data = {
        scrapingType: 'standard',
        followersScraped: followersScraped,
        isComplete: true
      };

      const result = await this.request('/api/chrome-extension/update-scraping-timestamp', {
        method: 'POST',
        body: JSON.stringify(data)
      });

      return result.success;
    } catch (error) {
      console.error('Error updating scraping timestamp:', error);
      return false;
    }
  }
}

// Export as singleton
const apiService = new ApiService();

// Make it available globally for background script
if (typeof globalThis !== 'undefined') {
  globalThis.ApiService = apiService;
}

export default apiService;