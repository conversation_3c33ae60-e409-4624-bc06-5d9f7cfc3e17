// Popup JavaScript - Simple and Clean

// DOM Elements
const elements = {
  apiKeyInput: document.getElementById('apiKeyInput'),
  toggleApiKey: document.getElementById('toggleApiKey'),
  saveApiKey: document.getElementById('saveApiKey'),
  connectionStatus: document.getElementById('connectionStatus'),
  statusText: document.getElementById('statusText'),
  controlSection: document.getElementById('controlSection'),
  currentStatus: document.getElementById('currentStatus'),
  messagesSent: document.getElementById('messagesSent'),
  followersScraped: document.getElementById('followersScraped'),
  startBtn: document.getElementById('startBtn'),
  stopBtn: document.getElementById('stopBtn'),
  refreshStatus: document.getElementById('refreshStatus')
};

// State
let isRunning = false;
let isApiKeyVisible = false;

// Initialize popup
document.addEventListener('DOMContentLoaded', async () => {
  await loadState();
  await updateStatus();
  
  // Set up event listeners
  elements.toggleApiKey.addEventListener('click', toggleApiKeyVisibility);
  elements.saveApiKey.addEventListener('click', saveApiKey);
  elements.startBtn.addEventListener('click', startExtension);
  elements.stopBtn.addEventListener('click', stopExtension);
  elements.refreshStatus.addEventListener('click', (e) => {
    e.preventDefault();
    updateStatus();
  });
  
  // Update status every 2 seconds when popup is open
  setInterval(updateStatus, 2000);
});

// Load saved state from storage
async function loadState() {
  try {
    const result = await chrome.storage.local.get(['apiKey', 'organizationName', 'isConnected']);
    
    if (result.apiKey) {
      elements.apiKeyInput.value = result.apiKey;
      
      if (result.isConnected) {
        showConnected(result.organizationName || 'Organization');
      }
    }
  } catch (error) {
    console.error('Error loading state:', error);
  }
}

// Update status from background
async function updateStatus() {
  try {
    const response = await chrome.runtime.sendMessage({ action: 'GET_STATUS' });
    
    if (response) {
      isRunning = response.isRunning;
      
      // Update UI based on status
      elements.currentStatus.textContent = response.status || 'Idle';
      elements.currentStatus.className = 'status-text';
      
      if (response.status?.includes('Error')) {
        elements.currentStatus.classList.add('error');
      } else if (response.isRunning) {
        elements.currentStatus.classList.add('active');
      }
      
      // Update stats
      elements.messagesSent.textContent = response.messagesSent || '0';
      elements.followersScraped.textContent = response.followersScraped || '0';
      
      // Update buttons
      if (isRunning) {
        elements.startBtn.style.display = 'none';
        elements.stopBtn.style.display = 'block';
      } else {
        elements.startBtn.style.display = 'block';
        elements.stopBtn.style.display = 'none';
      }
      
      // Disable buttons during certain states
      const isBusy = response.status?.includes('Processing') || 
                     response.status?.includes('Scraping') ||
                     response.status?.includes('Sending');
      
      elements.startBtn.disabled = isBusy;
      elements.stopBtn.disabled = isBusy;
    }
  } catch (error) {
    console.error('Error updating status:', error);
  }
}

// Toggle API key visibility
function toggleApiKeyVisibility() {
  isApiKeyVisible = !isApiKeyVisible;
  elements.apiKeyInput.type = isApiKeyVisible ? 'text' : 'password';
  elements.toggleApiKey.textContent = isApiKeyVisible ? '👁️‍🗨️' : '👁';
}

// Save and verify API key
async function saveApiKey() {
  const apiKey = elements.apiKeyInput.value.trim();
  
  if (!apiKey) {
    showError('Please enter an API key');
    return;
  }
  
  // Show loading state
  elements.saveApiKey.disabled = true;
  elements.saveApiKey.innerHTML = 'Verifying... <span class="loading"></span>';
  
  try {
    // Send to background script for verification
    const response = await chrome.runtime.sendMessage({
      action: 'SAVE_API_KEY',
      apiKey: apiKey
    });
    
    if (response.success) {
      showConnected(response.organizationName);
      
      // Save to storage
      await chrome.storage.local.set({
        apiKey: apiKey,
        organizationName: response.organizationName,
        isConnected: true
      });
      
      showSuccess('API key saved successfully!');
    } else {
      showError(response.error || 'Invalid API key');
    }
  } catch (error) {
    console.error('Error saving API key:', error);
    showError('Failed to verify API key');
  } finally {
    elements.saveApiKey.disabled = false;
    elements.saveApiKey.textContent = 'Save API Key';
  }
}

// Start extension
async function startExtension() {
  try {
    elements.startBtn.disabled = true;
    elements.startBtn.innerHTML = 'Starting... <span class="loading"></span>';
    
    const response = await chrome.runtime.sendMessage({ action: 'START' });
    
    if (response.success) {
      isRunning = true;
      await updateStatus();
      showSuccess('Extension started!');
    } else {
      showError(response.error || 'Failed to start');
    }
  } catch (error) {
    console.error('Error starting extension:', error);
    showError('Failed to start extension');
  } finally {
    elements.startBtn.disabled = false;
    elements.startBtn.textContent = 'START';
  }
}

// Stop extension
async function stopExtension() {
  try {
    elements.stopBtn.disabled = true;
    elements.stopBtn.innerHTML = 'Stopping... <span class="loading"></span>';
    
    const response = await chrome.runtime.sendMessage({ action: 'STOP' });
    
    if (response.success) {
      isRunning = false;
      await updateStatus();
      showSuccess('Extension stopped!');
    } else {
      showError(response.error || 'Failed to stop');
    }
  } catch (error) {
    console.error('Error stopping extension:', error);
    showError('Failed to stop extension');
  } finally {
    elements.stopBtn.disabled = false;
    elements.stopBtn.textContent = 'STOP';
  }
}

// UI Helper Functions
function showConnected(organizationName) {
  elements.connectionStatus.className = 'connection-status connected';
  elements.statusText.textContent = `Connected to ${organizationName}`;
  elements.controlSection.style.display = 'block';
}

function showDisconnected() {
  elements.connectionStatus.className = 'connection-status disconnected';
  elements.statusText.textContent = 'Not Connected';
  elements.controlSection.style.display = 'none';
}

function showError(message) {
  // Create temporary error message
  const errorDiv = document.createElement('div');
  errorDiv.style.cssText = `
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #f56565;
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    z-index: 1000;
    animation: slideDown 0.3s ease;
  `;
  errorDiv.textContent = message;
  document.body.appendChild(errorDiv);
  
  setTimeout(() => {
    errorDiv.remove();
  }, 3000);
}

function showSuccess(message) {
  // Create temporary success message
  const successDiv = document.createElement('div');
  successDiv.style.cssText = `
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #48bb78;
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    z-index: 1000;
    animation: slideDown 0.3s ease;
  `;
  successDiv.textContent = message;
  document.body.appendChild(successDiv);
  
  setTimeout(() => {
    successDiv.remove();
  }, 3000);
}

// Add animation styles
const style = document.createElement('style');
style.textContent = `
  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateX(-50%) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
  }
`;
document.head.appendChild(style);