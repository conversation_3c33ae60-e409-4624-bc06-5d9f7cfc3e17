/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  color: #333;
  background: #f5f5f5;
  width: 380px;
  min-height: 400px;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 400px;
}

/* Header */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header h1 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 5px;
}

.version {
  font-size: 12px;
  opacity: 0.9;
}

/* Sections */
.section {
  padding: 20px;
  background: white;
  margin: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.section h2 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #444;
}

/* Connection Status */
.connection-status {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 15px;
  font-weight: 500;
}

.connection-status.connected {
  background: #e8f5e9;
  color: #2e7d32;
}

.connection-status.disconnected {
  background: #ffebee;
  color: #c62828;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
  background: currentColor;
}

/* API Input Group */
.api-input-group {
  display: flex;
  gap: 5px;
  margin-bottom: 10px;
}

.api-input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 13px;
  font-family: monospace;
  transition: border-color 0.2s;
}

.api-input:focus {
  outline: none;
  border-color: #667eea;
}

.btn-icon {
  padding: 8px 12px;
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s;
}

.btn-icon:hover {
  background: #e0e0e0;
}

/* Buttons */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  width: 100%;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #5a67d8;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.btn-success {
  background: #48bb78;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #38a169;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(72, 187, 120, 0.3);
}

.btn-danger {
  background: #f56565;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #e53e3e;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(245, 101, 101, 0.3);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* API Help */
.api-help {
  margin-top: 10px;
  text-align: center;
  color: #666;
  font-size: 12px;
}

.api-help a {
  color: #667eea;
  text-decoration: none;
}

.api-help a:hover {
  text-decoration: underline;
}

/* Status Display */
.status-display {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 15px;
}

.status-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.status-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* Stats */
.stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-bottom: 20px;
}

.stat-item {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 11px;
  color: #666;
  margin-bottom: 5px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  display: block;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

/* Control Buttons */
.control-buttons {
  display: flex;
  gap: 10px;
}

/* Footer */
.footer {
  margin-top: auto;
  padding: 15px;
  text-align: center;
  font-size: 12px;
  color: #666;
  border-top: 1px solid #e0e0e0;
  background: white;
}

.footer a {
  color: #667eea;
  text-decoration: none;
  margin: 0 5px;
}

.footer a:hover {
  text-decoration: underline;
}

/* Loading State */
.loading {
  display: inline-block;
  width: 14px;
  height: 14px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 8px;
  vertical-align: middle;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Status Colors */
.status-text.active {
  color: #48bb78;
}

.status-text.error {
  color: #f56565;
}

.status-text.warning {
  color: #ed8936;
}

/* Responsive adjustments */
@media (max-width: 400px) {
  body {
    width: 350px;
  }
}