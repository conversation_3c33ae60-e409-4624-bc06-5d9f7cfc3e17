<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AISetter Simple</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="container">
    <!-- Header -->
    <div class="header">
      <h1>AISetter</h1>
      <div class="version">v1.0.0</div>
    </div>

    <!-- API Key Section -->
    <div class="section api-section">
      <h2>API Configuration</h2>
      
      <div id="connectionStatus" class="connection-status disconnected">
        <span class="status-indicator"></span>
        <span id="statusText">Not Connected</span>
      </div>

      <div class="api-input-group">
        <input 
          type="password" 
          id="apiKeyInput" 
          placeholder="Enter your API key" 
          class="api-input"
        />
        <button id="toggleApiKey" class="btn-icon">👁</button>
      </div>

      <button id="saveApiKey" class="btn btn-primary">Save API Key</button>
      
      <div class="api-help">
        <small>Get your API key from: 
          <a href="https://app.aisetter.pl" target="_blank">AISetter Dashboard</a>
        </small>
      </div>
    </div>

    <!-- Control Section (shown when connected) -->
    <div id="controlSection" class="section control-section" style="display: none;">
      <h2>Extension Control</h2>
      
      <!-- Status Display -->
      <div class="status-display">
        <div class="status-label">Current Status:</div>
        <div id="currentStatus" class="status-text">Idle</div>
      </div>

      <!-- Stats -->
      <div class="stats">
        <div class="stat-item">
          <span class="stat-label">Messages Sent:</span>
          <span id="messagesSent" class="stat-value">0</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Followers Scraped:</span>
          <span id="followersScraped" class="stat-value">0</span>
        </div>
      </div>

      <!-- Control Buttons -->
      <div class="control-buttons">
        <button id="startBtn" class="btn btn-success">START</button>
        <button id="stopBtn" class="btn btn-danger" style="display: none;">STOP</button>
      </div>
    </div>

    <!-- Footer -->
    <div class="footer">
      <a href="https://app.aisetter.pl" target="_blank">Dashboard</a>
      <span>•</span>
      <a href="#" id="refreshStatus">Refresh</a>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>