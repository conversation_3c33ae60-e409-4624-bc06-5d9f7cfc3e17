// Background Script - Main extension logic

import apiService from './lib/api.js';

// Extension state
const state = {
  isRunning: false,
  status: 'Idle',
  extensionStatus: 'FRESH_START',
  messagesSent: 0,
  followersScraped: 0,
  initialScrapingDone: false,
  currentTask: null,
  lastError: null,
  followerCheckIsRunning: false,
  monitorStartTimestamp: 0
};

// Timers
let mainLoopInterval = null;
let monitoringInterval = null;

// Enhanced message sending with retry mechanism (from original extension)
async function sendMessageWithRetry(tabId, message, operationName, maxRetries = 3) {
  let lastError = null;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // Check if tab still exists
      await chrome.tabs.get(tabId);
      
      const result = await chrome.tabs.sendMessage(tabId, message);
      return result;
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        break;
      }
      
      // Try to recover based on error type
      if (error.message?.includes('Receiving end does not exist')) {
        const delayMs = 3000 + (attempt * 2000);
        console.log(`Content script not ready, waiting ${delayMs}ms before retry ${attempt}/${maxRetries}`);
        await delay(delayMs);
      } else if (error.message?.includes('The tab was closed')) {
        throw error; // Don't retry if tab was closed
      } else {
        const delayMs = 1000 * attempt;
        await delay(delayMs);
      }
    }
  }
  
  const finalError = lastError || new Error(`${operationName} failed after ${maxRetries} attempts`);
  throw finalError;
}

// Content script readiness verification (from original extension)
async function ensureContentScriptReady(tabId, maxAttempts = 8) {
  let attempts = 0;
  let started = false;
  
  while (attempts < maxAttempts && !started && state.isRunning) {
    try {
      // First, check if tab is still valid and on Instagram
      const currentTab = await chrome.tabs.get(tabId);
      if (!currentTab.url?.includes('instagram.com')) {
        throw new Error('Tab is not on Instagram');
      }
      
      // Try to ping the content script
      const response = await chrome.tabs.sendMessage(tabId, { action: 'PING' });
      
      if (response && response.success) {
        started = true;
        console.log('Content script is ready');
      }
      
    } catch (error) {
      attempts++;
      const isLastAttempt = attempts >= maxAttempts;
      
      console.log(`Content script readiness check failed (attempt ${attempts}/${maxAttempts}):`, error.message);
      
      if (error.message?.includes('Receiving end does not exist')) {
        await delay(5000); // Longer delay for content script loading
      } else if (error.message?.includes('The tab was closed')) {
        throw error; // Re-throw tab closed errors
      } else {
        await delay(3000);
      }
      
      // Force refresh the page if we're having persistent issues
      if (attempts === Math.floor(maxAttempts / 2)) {
        try {
          console.log('Forcing page refresh due to persistent connection issues');
          await chrome.tabs.reload(tabId);
          await delay(5000); // Wait for page to fully reload
        } catch (refreshError) {
          console.error('Failed to refresh tab:', refreshError);
        }
      }
    }
  }

  if (!started) {
    throw new Error(`Could not establish connection with content script after ${attempts} attempts. Content script may not be loaded or Instagram page may need refresh.`);
  }
  
  return true;
}

// Initialize extension
chrome.runtime.onInstalled.addListener(() => {
  console.log('AISetter Extension installed');
  loadState();
});

// Load state from storage
async function loadState() {
  try {
    const stored = await chrome.storage.local.get([
      'apiKey',
      'organizationId', 
      'extensionStatus',
      'initialScrapingDone',
      'messagesSent',
      'followersScraped'
    ]);
    
    if (stored.apiKey) {
      apiService.setApiKey(stored.apiKey);
      apiService.organizationId = stored.organizationId;
    }
    
    if (stored.extensionStatus) {
      state.extensionStatus = stored.extensionStatus;
    }
    
    state.initialScrapingDone = stored.initialScrapingDone || false;
    state.messagesSent = stored.messagesSent || 0;
    state.followersScraped = stored.followersScraped || 0;
    
  } catch (error) {
    console.error('Error loading state:', error);
  }
}

// Save state to storage
async function saveState() {
  try {
    await chrome.storage.local.set({
      extensionStatus: state.extensionStatus,
      initialScrapingDone: state.initialScrapingDone,
      messagesSent: state.messagesSent,
      followersScraped: state.followersScraped
    });
  } catch (error) {
    console.error('Error saving state:', error);
  }
}

// Update status
function updateStatus(status, isError = false) {
  state.status = status;
  if (isError) {
    state.lastError = status;
  }
  console.log(`[Status] ${status}`);
}

// Check for new followers during wait times (like original extension)
async function checkActivityInboxForNewFollowers() {
  if (state.followerCheckIsRunning) {
    return [];
  }

  state.followerCheckIsRunning = true;

  try {
    // Get Instagram tab
    const tabs = await chrome.tabs.query({ url: 'https://www.instagram.com/*' });
    if (tabs.length === 0) {
      return [];
    }

    const tab = tabs[0];
    
    // Get Instagram stories from activity inbox
    const inboxResult = await sendMessageWithRetry(tab.id, {
      action: 'GET_INSTA_INBOX'
    }, 'Get Instagram Inbox');

    if (!inboxResult || inboxResult.status !== 'success') {
      return [];
    }

    const stories = inboxResult.data || [];

    // Get already processed followers from storage to avoid duplicates
    const processedResult = await chrome.storage.local.get(['processedActivityFollowers']);
    const processedFollowers = new Set(processedResult.processedActivityFollowers || []);

    // Filter stories to only new followers after monitor start
    const newFollowers = [];
    let skippedOldFollowers = 0;
    let skippedProcessedFollowers = 0;

    for (const story of stories) {
      const story_timestamp = story.timestamp * 1000; // Convert to milliseconds
      
      // Check if this follower is from after monitoring started
      const monitorStartTime = state.monitorStartTimestamp || 0;
      if (story_timestamp < monitorStartTime) {
        skippedOldFollowers++;
        continue;
      }

      // Check if we've already processed this follower from Activity Inbox
      if (processedFollowers.has(story.username)) {
        skippedProcessedFollowers++;
        continue;
      }

      // This is a new follower since monitoring started and not yet processed
      newFollowers.push({
        instagramNickname: story.username,
        instagramId: story.id,
        isVerified: false,
        timestamp: story.timestamp,
        followedAt: new Date(story_timestamp)
      });
    }

    // Send new followers to API if any
    if (newFollowers.length > 0) {
      console.log(`Found ${newFollowers.length} new followers, sending to API...`);
      
      const result = await apiService.sendFollowers(newFollowers, false); // false = not initial scraping
      
      if (result.success) {
        // Mark these followers as processed to avoid reprocessing
        const newProcessedFollowers = newFollowers.map(f => f.instagramNickname);
        processedFollowers.forEach(username => newProcessedFollowers.push(username));
        
        // Keep only the last 1000 processed followers to avoid unbounded growth
        const limitedProcessedFollowers = newProcessedFollowers.slice(-1000);
        
        await chrome.storage.local.set({
          processedActivityFollowers: limitedProcessedFollowers
        });
        
        console.log(`Successfully processed ${newFollowers.length} new followers`);
      }
    }

    return newFollowers;

  } catch (error) {
    console.error('Error checking for new followers:', error);
    return [];
  } finally {
    state.followerCheckIsRunning = false;
  }
}

// Message handler for popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  switch (request.action) {
    case 'GET_STATUS':
      sendResponse({
        isRunning: state.isRunning,
        status: state.status,
        messagesSent: state.messagesSent,
        followersScraped: state.followersScraped,
        extensionStatus: state.extensionStatus
      });
      break;

    case 'GET_COOKIE':
      // Handle cookie requests from content script
      chrome.cookies.get({
        url: request.data.url,
        name: request.data.name
      }).then(cookie => {
        sendResponse(cookie?.value || null);
      }).catch(error => {
        console.error('Error getting cookie:', error);
        sendResponse(null);
      });
      return true; // Keep message channel open for async response
      
    case 'SAVE_API_KEY':
      handleSaveApiKey(request.apiKey)
        .then(sendResponse)
        .catch(error => {
          console.error('Error handling API key:', error);
          sendResponse({ success: false, error: error.message });
        });
      return true; // Keep message channel open for async response
      
    case 'START':
      startExtension()
        .then(sendResponse)
        .catch(error => {
          console.error('Error starting extension:', error);
          sendResponse({ success: false, error: error.message });
        });
      return true;
      
    case 'STOP':
      stopExtension()
        .then(sendResponse)
        .catch(error => {
          console.error('Error stopping extension:', error);
          sendResponse({ success: false, error: error.message });
        });
      return true;
      
    default:
      sendResponse({ success: false, error: 'Unknown action' });
  }
});

// Handle API key save and verification
async function handleSaveApiKey(apiKey) {
  try {
    updateStatus('Verifying API key...');
    
    apiService.setApiKey(apiKey);
    const result = await apiService.verifyApiKey(apiKey);
    
    if (result.success) {
      // Save to storage
      await chrome.storage.local.set({
        apiKey: apiKey,
        organizationId: result.organizationId,
        organizationName: result.organizationName
      });
      
      updateStatus('API key verified');
      
      // Get extension status from backend
      const status = await apiService.getExtensionStatus();
      if (status) {
        state.extensionStatus = status.extensionStatus;
        state.followersScraped = status.totalFollowersScraped || 0;
        await saveState();
      }
      
      return {
        success: true,
        organizationName: result.organizationName
      };
    } else {
      updateStatus('Invalid API key', true);
      return {
        success: false,
        error: result.error
      };
    }
  } catch (error) {
    updateStatus(`API key error: ${error.message}`, true);
    return {
      success: false,
      error: error.message
    };
  }
}

// Start extension
async function startExtension() {
  if (state.isRunning) {
    return { success: false, error: 'Already running' };
  }
  
  try {
    // Check API key
    const stored = await chrome.storage.local.get(['apiKey', 'organizationId']);
    if (!stored.apiKey) {
      return { success: false, error: 'No API key configured' };
    }
    
    apiService.setApiKey(stored.apiKey);
    apiService.organizationId = stored.organizationId;
    
    state.isRunning = true;
    updateStatus('Starting...');
    
    // Set monitor start timestamp for new follower tracking (like original extension)
    state.monitorStartTimestamp = Date.now();
    
    // Get session history for recovery
    const lastSession = await chrome.storage.local.get(['lastSessionTimestamp', 'hadPreviousSession']);
    
    if (lastSession.hadPreviousSession && lastSession.lastSessionTimestamp) {
      const offlineDuration = state.monitorStartTimestamp - lastSession.lastSessionTimestamp;
      const offlineHours = Math.round(offlineDuration / (1000 * 60 * 60));
      console.log(`Extension was offline for ${offlineHours} hours`);
    } else {
      // Fresh start - clear processed followers list
      await chrome.storage.local.set({ processedActivityFollowers: [] });
    }
    
    // Mark current session timestamp
    await chrome.storage.local.set({
      lastSessionTimestamp: state.monitorStartTimestamp,
      hadPreviousSession: true
    });
    
    // Check if initial scraping is needed OR if we need to catch up on new followers
    const eligibility = await apiService.checkScrapingEligibility();
    
    if (eligibility.extensionStatus === 'FRESH_START' && !state.initialScrapingDone) {
      // Need to do initial scraping
      updateStatus('Preparing initial follower scraping...');
      await performInitialScraping();
    } else {
      // Check for new followers gained while offline (continuous scraping)
      updateStatus('Checking for new followers gained while offline...');
      await performContinuousScraping();
    }
    
    // Update backend status
    await apiService.updateStatus('ACTIVE', 'Extension started');
    
    return { success: true };
    
  } catch (error) {
    state.isRunning = false;
    updateStatus(`Start error: ${error.message}`, true);
    return { success: false, error: error.message };
  }
}

// Stop extension
async function stopExtension() {
  state.isRunning = false;
  
  // Clear intervals
  if (mainLoopInterval) {
    clearInterval(mainLoopInterval);
    mainLoopInterval = null;
  }
  
  if (monitoringInterval) {
    clearInterval(monitoringInterval);
    monitoringInterval = null;
  }
  
  updateStatus('Stopped');
  
  // Update backend status
  await apiService.updateStatus('IDLE', 'Extension stopped by user');
  
  return { success: true };
}

// Perform initial scraping (50 followers)
async function performInitialScraping() {
  try {
    updateStatus('Getting Instagram settings...');
    
    // Get Instagram username from backend settings
    const settings = await apiService.getSettings();
    if (!settings || !settings.instagramUsername) {
      throw new Error('Instagram username not found. Please connect your Instagram account in the dashboard first.');
    }
    
    const username = settings.instagramUsername;
    updateStatus(`Opening Instagram profile: @${username}`);
    
    // Create or get Instagram tab
    const tabs = await chrome.tabs.query({ url: 'https://www.instagram.com/*' });
    let tab;
    
    if (tabs.length > 0) {
      tab = tabs[0];
      // Navigate to user's profile
      await chrome.tabs.update(tab.id, {
        url: `https://www.instagram.com/${username}/`,
        active: true
      });
    } else {
      // Create new tab with user's profile
      tab = await chrome.tabs.create({
        url: `https://www.instagram.com/${username}/`,
        active: true
      });
    }
    
    // Wait for page to load completely
    await waitForTabLoad(tab.id);
    
    // Additional wait for content script initialization
    await delay(3000);
    
    // Wait for content script to be ready
    let contentScriptReady = false;
    for (let i = 0; i < 3; i++) {
      try {
        const pingResponse = await chrome.tabs.sendMessage(tab.id, { action: 'PING' });
        if (pingResponse && pingResponse.success) {
          contentScriptReady = true;
          break;
        }
      } catch (error) {
        await delay(2000);
      }
    }
    
    if (!contentScriptReady) {
      throw new Error('Content script not ready. Please refresh Instagram.');
    }
    
    updateStatus('Opening followers modal and scraping...');
    
    // Open followers modal
    const openResponse = await chrome.tabs.sendMessage(tab.id, {
      action: 'OPEN_FOLLOWERS_MODAL',
      username: username
    });
    
    if (!openResponse || !openResponse.success) {
      throw new Error(openResponse?.error || 'Failed to open followers modal');
    }
    
    await delay(3000); // Wait for modal to open
    
    // Scrape followers
    const response = await chrome.tabs.sendMessage(tab.id, {
      action: 'SCRAPE_FOLLOWERS',
      count: 50,
      username: username
    });
    
    if (response.success && response.followers) {
      const followers = response.followers.map(username => ({
        instagramNickname: username,
        isVerified: false
      }));
      
      updateStatus(`Sending ${followers.length} followers to server...`);
      
      // Send to backend
      const result = await apiService.sendFollowers(followers, true);
      
      if (result.success) {
        state.followersScraped = followers.length;
        updateStatus(`Initial scraping complete: ${followers.length} followers`);
        
        // Update backend status
        await apiService.updateStatus('SCRAPED_50', 'Initial scraping complete');
        
        // Start normal operation
        setTimeout(() => {
          updateStatus('Starting message processing...');
          startMainLoop();
        }, 3000);
        
      } else {
        throw new Error(result.error || 'Failed to send followers');
      }
    } else {
      throw new Error(response.error || 'Failed to scrape followers');
    }
    
  } catch (error) {
    updateStatus(`Scraping error: ${error.message}`, true);
    state.isRunning = false;
    throw error;
  }
}

// Perform continuous scraping to catch up on new followers
async function performContinuousScraping() {
  try {
    updateStatus('Getting Instagram settings...');
    
    // Get Instagram username from backend settings
    const settings = await apiService.getSettings();
    if (!settings || !settings.instagramUsername) {
      throw new Error('Instagram username not found. Please connect your Instagram account in the dashboard first.');
    }
    
    const username = settings.instagramUsername;
    updateStatus(`Opening Instagram profile: @${username}`);
    
    // Create or get Instagram tab
    const tabs = await chrome.tabs.query({ url: 'https://www.instagram.com/*' });
    let tab;
    
    if (tabs.length > 0) {
      tab = tabs[0];
      // Navigate to user's profile
      await chrome.tabs.update(tab.id, {
        url: `https://www.instagram.com/${username}/`,
        active: true
      });
    } else {
      // Create new tab with user's profile
      tab = await chrome.tabs.create({
        url: `https://www.instagram.com/${username}/`,
        active: true
      });
    }
    
    // Wait for page to load completely
    await waitForTabLoad(tab.id);
    await delay(3000);
    
    // Wait for content script to be ready
    let contentScriptReady = false;
    for (let i = 0; i < 3; i++) {
      try {
        const pingResponse = await chrome.tabs.sendMessage(tab.id, { action: 'PING' });
        if (pingResponse && pingResponse.success) {
          contentScriptReady = true;
          break;
        }
      } catch (error) {
        await delay(2000);
      }
    }
    
    if (!contentScriptReady) {
      throw new Error('Content script not ready. Please refresh Instagram.');
    }
    
    // Start continuous scraping process
    let batchNumber = 1;
    let totalNewFollowers = 0;
    let shouldContinue = true;
    
    updateStatus('Opening followers modal for continuous scraping...');
    
    // Open followers modal
    const openResponse = await chrome.tabs.sendMessage(tab.id, {
      action: 'OPEN_FOLLOWERS_MODAL',
      username: username
    });
    
    if (!openResponse || !openResponse.success) {
      throw new Error(openResponse?.error || 'Failed to open followers modal');
    }
    
    await delay(3000); // Wait for modal to open
    
    // Continuous scraping loop - scrape in batches until duplicates found
    while (shouldContinue && state.isRunning) {
      updateStatus(`Scraping batch ${batchNumber} (checking for new followers)...`);
      
      // Scrape current batch of followers
      const response = await chrome.tabs.sendMessage(tab.id, {
        action: 'SCRAPE_FOLLOWERS',
        count: 50,
        username: username
      });
      
      if (!response.success || !response.followers || response.followers.length === 0) {
        updateStatus('No more followers found - scraping complete');
        break;
      }
      
      const followers = response.followers.map(username => ({
        instagramNickname: username,
        isVerified: false
      }));
      
      updateStatus(`Sending batch ${batchNumber} (${followers.length} followers) to server...`);
      
      // Send batch to backend for duplicate detection
      const result = await apiService.sendFollowersBatch(followers, batchNumber);
      
      if (result.success) {
        totalNewFollowers += result.newFollowers;
        
        updateStatus(`Batch ${batchNumber}: ${result.newFollowers} new, ${result.processed - result.newFollowers} duplicates (${result.duplicateRate}% duplicate rate)`);
        
        // Check if we should continue scraping
        shouldContinue = result.shouldContinueScraping;
        
        if (!shouldContinue) {
          updateStatus(`Stopping scraping: ${result.recommendation}`);
          break;
        }
        
        batchNumber++;
        
        // Small delay between batches to avoid overwhelming Instagram
        await delay(2000);
        
      } else {
        throw new Error(result.error || 'Failed to send followers batch');
      }
      
      // Safety limit - don't scrape more than 10 batches (500 followers) in one session
      if (batchNumber > 10) {
        updateStatus('Reached safety limit - stopping scraping');
        break;
      }
    }
    
    // Close modal
    try {
      await chrome.tabs.sendMessage(tab.id, { action: 'CLOSE_MODAL' });
    } catch (error) {
      console.log('Could not close modal:', error);
    }
    
    if (totalNewFollowers > 0) {
      updateStatus(`Continuous scraping complete: ${totalNewFollowers} new followers found and processed`);
      state.followersScraped += totalNewFollowers;
      await saveState();
      
      // Update backend status
      await apiService.updateStatus('ACTIVE', `Caught up: ${totalNewFollowers} new followers processed`);
    } else {
      updateStatus('No new followers found - already up to date');
      await apiService.updateStatus('ACTIVE', 'No new followers - ready for messaging');
    }
    
    // Start normal message processing
    setTimeout(() => {
      updateStatus('Starting message processing...');
      startMainLoop();
    }, 3000);
    
  } catch (error) {
    updateStatus(`Continuous scraping error: ${error.message}`, true);
    console.error('Continuous scraping error:', error);
    
    // Fall back to normal operation
    setTimeout(() => {
      updateStatus('Starting message processing (scraping failed)...');
      startMainLoop();
    }, 3000);
  }
}

// Main processing loop (simplified)
function startMainLoop() {
  if (mainLoopInterval) {
    clearInterval(mainLoopInterval);
  }
  
  processMessages(); // Run immediately
  mainLoopInterval = setInterval(processMessages, 60000); // Every minute
}

// Process messages from attack list
async function processMessages() {
  if (!state.isRunning || state.currentTask) {
    return;
  }
  
  try {
    state.currentTask = 'processing';
    
    // Get messages to send
    const messages = await apiService.getMessagesToSend();
    
    if (messages.length === 0) {
      updateStatus('No messages to send');
      state.currentTask = null;
      return;
    }
    
    updateStatus(`Found ${messages.length} messages to send`);
    
    // Get or create Instagram tab
    const tabs = await chrome.tabs.query({ url: 'https://www.instagram.com/*' });
    let tab;
    
    if (tabs.length > 0) {
      tab = tabs[0];
      // Make sure tab is active to prevent throttling
      await chrome.tabs.update(tab.id, { active: true });
    } else {
      tab = await chrome.tabs.create({
        url: 'https://www.instagram.com/',
        active: true // Keep active to prevent throttling
      });
      await waitForTabLoad(tab.id);
    }
    
    // Ensure content script is ready before processing messages
    try {
      await ensureContentScriptReady(tab.id);
      updateStatus('Content script ready, starting message processing...');
    } catch (error) {
      throw new Error(`Content script connection failed: ${error.message}`);
    }
    
    // Process each message
    for (const message of messages) {
      if (!state.isRunning) break;
      
      try {
        // Step 1: Navigate to profile
        updateStatus(`Navigating to profile: @${message.contactUsername}`);
        const profileUrl = `https://www.instagram.com/${message.contactUsername}/`;
        await chrome.tabs.update(tab.id, { url: profileUrl });
        await waitForTabLoad(tab.id);
        await delay(4000);

        // Step 2: Click Message Button with retry
        updateStatus(`Clicking message button for @${message.contactUsername}`);
        await sendMessageWithRetry(
          tab.id,
          {
            action: 'VISIT_USER_MESSAGES',
            username: message.contactUsername
          },
          `Visit Messages for ${message.contactUsername}`
        );
        await delay(6000); // Wait for DM page to load

        // Step 3: Send the message(s) using robust retry logic
        const batchMessages = message.message.split(' | ').map(msg => msg.trim()).filter(msg => msg.length > 0);
        let batchSuccess = true;
        let sentMessagesCount = 0;

        for (let i = 0; i < batchMessages.length; i++) {
          const individualMessage = batchMessages[i];
          
          if (!state.isRunning) {
            batchSuccess = false;
            break;
          }
          
          updateStatus(`Sending message ${i + 1}/${batchMessages.length} to @${message.contactUsername}`);
          
          const sendResult = await sendMessageWithRetry(
            tab.id,
            {
              action: 'SEND_MESSAGE',
              username: message.contactUsername,
              message: individualMessage,
              type: message.messageType
            },
            `Send Message ${i + 1}/${batchMessages.length} to ${message.contactUsername}`
          );

          if (sendResult && sendResult.success) {
            sentMessagesCount++;
            if (i < batchMessages.length - 1) {
              const lineDelay = apiService.getDelayBetweenMessageLines();
              await delay(lineDelay); // Wait between message lines (hardcoded natural typing delay)
            }
          } else {
            batchSuccess = false;
            break;
          }
        }

        if (batchSuccess && sentMessagesCount === batchMessages.length) {
          // Mark message as sent (v2 API)
          const markSentResult = await apiService.markMessageSent(message.id);
          
          if (markSentResult.success) {
            state.messagesSent++;
            await saveState();
            
            const statusMessage = markSentResult.nextMessage
              ? `All messages sent to @${message.contactUsername} (${sentMessagesCount}/${batchMessages.length}). Next: @${markSentResult.nextMessage.contactUsername}`
              : `All messages sent to @${message.contactUsername} (${sentMessagesCount}/${batchMessages.length})`;
            
            updateStatus(statusMessage);
          } else {
            updateStatus(`Messages sent to @${message.contactUsername} but failed to update server`);
          }
        } else {
          console.error(`Failed to send complete batch to @${message.contactUsername}: ${sentMessagesCount}/${batchMessages.length} sent`);
          updateStatus(`Partial send to @${message.contactUsername}: ${sentMessagesCount}/${batchMessages.length} messages sent`);
        }

      } catch (error) {
        console.error(`Error processing message for @${message.contactUsername}:`, error);
        updateStatus(`Error sending to @${message.contactUsername}: ${error.message}`);
      }
      
      // Wait between different users with new follower monitoring (using backend timing settings)
      if (state.isRunning) {
        const waitTime = await apiService.getRandomDelayBetweenDMs();
        const monitoringInterval = await apiService.getMonitoringInterval();
        updateStatus(`⏳ Waiting ${Math.round(waitTime/60000)} minutes before next message (monitoring for new followers)`);
        
        // During wait time, monitor for new followers using Activity Inbox
        const waitStartTime = Date.now();
        
        while (Date.now() - waitStartTime < waitTime && state.isRunning) {
          // Check for new followers during wait time
          await checkActivityInboxForNewFollowers();
          
          // Wait for monitoring interval before next check or until wait time is complete
          const remainingWait = waitTime - (Date.now() - waitStartTime);
          const nextCheckDelay = Math.min(monitoringInterval, remainingWait);
          
          if (nextCheckDelay > 0) {
            await delay(nextCheckDelay);
          }
        }
      }
    }
    
    updateStatus('Message processing complete');
    
  } catch (error) {
    updateStatus(`Message error: ${error.message}`, true);
  } finally {
    state.currentTask = null;
  }
}

// Removed startMonitoring and checkNewFollowers - simplified version focuses on messaging

// Helper functions
async function waitForTabLoad(tabId) {
  return new Promise((resolve) => {
    const listener = (id, info) => {
      if (id === tabId && info.status === 'complete') {
        chrome.tabs.onUpdated.removeListener(listener);
        setTimeout(resolve, 2000);
      }
    };
    chrome.tabs.onUpdated.addListener(listener);
  });
}

function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function randomBetween(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}