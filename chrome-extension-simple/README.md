# AISetter Chrome Extension (Simplified Version)

A clean, simplified Chrome extension for Instagram DM automation with AISetter backend integration.

## Features

- ✅ **One-time Initial Scraping**: Automatically scrapes 50 most recent followers on first run
- ✅ **Automated Messaging**: Sends batch messages from attack list with natural delays
- ✅ **New Follower Monitoring**: Checks Activity Inbox for new followers
- ✅ **Simple UI**: Clean popup with just essential controls
- ✅ **API Integration**: Connects to AISetter backend for settings and message management

## Installation

1. Download or clone this `chrome-extension-simple` folder
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" (top right)
4. Click "Load unpacked"
5. Select the `chrome-extension-simple` folder
6. The extension icon will appear in your toolbar

## Setup

1. Click the extension icon to open the popup
2. Get your API key from: https://app.aisetter.pl/organizations/[your-org]/chrome-extension/api-key
3. Paste the API key and click "Save API Key"
4. You'll see "Connected to [Your Organization]"
5. Click "START" to begin operation

## How It Works

### Extension Flow

```
1. START clicked
   ↓
2. Check Extension Status
   ↓
3. If FRESH_START → Perform initial scraping (50 followers)
   ↓
4. Send followers to backend → Creates contacts in attack list
   ↓
5. Main Loop (every 30 seconds):
   - Get messages from attack list
   - Send messages with delays
   - Mark as sent
   ↓
6. Monitoring Loop (every 2 minutes):
   - Check Activity Inbox
   - Send new followers to backend
```

### Message Format

The extension handles batch messages separated by `|`:

```
"Hej | dzieki za follow | potrzebujesz jakis porad"
```

Each part is sent with a 10-25 second random delay between them.

## Extension States

- **FRESH_START**: Never run before, needs initial scraping
- **SCRAPED_50**: Initial scraping complete
- **ACTIVE**: Running and processing messages
- **IDLE**: Stopped by user

## Continuous Follower Scraping

The extension now implements continuous follower scraping to handle new followers gained while offline:

### How it works:

1. **On startup**: Extension checks for new followers every time it starts
2. **Batch scraping**: Scrapes followers in batches of 50
3. **Duplicate detection**: Backend detects duplicates and indicates when to stop
4. **Smart stopping**: Stops when 80%+ of scraped followers are duplicates
5. **Catch-up**: Processes all new followers gained while extension was offline

### Scraping Strategy:

- Starts from most recent followers (top of followers list)
- Scrapes in batches until duplicates are found
- Sends each batch to backend for duplicate detection
- Continues until backend says "stop scraping"
- Processes new followers with AI analysis or batch messages

### Safety Limits:

- Maximum 10 batches (500 followers) per session
- 2-second delay between batches
- Automatic timeout and error handling

## File Structure

```
chrome-extension-simple/
├── manifest.json          # Extension configuration
├── background.js          # Main logic and state management
├── content.js            # Instagram DOM interactions
├── popup/
│   ├── popup.html        # UI structure
│   ├── popup.css         # Styling
│   └── popup.js          # UI logic
├── lib/
│   └── api.js           # Backend API communication
└── icons/               # Extension icons
```

## API Endpoints Used

1. **POST** `/api/verify-api-key` - Verify API key
2. **GET** `/api/chrome-extension/settings` - Get timing settings
3. **GET** `/api/chrome-extension/status` - Get extension status
4. **PUT** `/api/chrome-extension/status` - Update status
5. **POST** `/api/chrome-extension/process-followers` - Send scraped followers
6. **GET** `/api/chrome-extension/messages-to-send` - Get attack list messages
7. **POST** `/api/chrome-extension/mark-sent` - Mark message as sent
8. **GET** `/api/chrome-extension/scraping-eligibility` - Check if scraping allowed

## Key Simplifications

### Removed Features

- ❌ Complex state machines
- ❌ Conversation gathering
- ❌ Continuous scraping (only initial 50 + monitoring)
- ❌ Follow-up scheduling
- ❌ Progressive scraping
- ❌ Natural pause periods
- ❌ Message limits
- ❌ Smart focus modes

### Kept Features

- ✅ Initial scraping (50 followers)
- ✅ Attack list processing
- ✅ Batch message support
- ✅ New follower monitoring
- ✅ Simple START/STOP control

## Technical Details

### Storage Schema

```javascript
{
  apiKey: string,
  organizationId: string,
  organizationName: string,
  extensionStatus: string,
  initialScrapingDone: boolean,
  messagesSent: number,
  followersScraped: number
}
```

### Message Processing

- Checks for messages every 30 seconds
- Sends with 10-15 second delays between users
- Batch messages have 10-25 second delays between parts
- Marks as sent immediately after sending

### Follower Monitoring

- Checks Activity Inbox every 2 minutes
- Only processes new followers (not already in system)
- Sends to backend for processing

## Troubleshooting

### Extension not starting

- Check if API key is valid
- Ensure you're logged into Instagram
- Check Chrome console for errors (F12)

### Messages not sending

- Verify Instagram is open in a tab
- Check if attack list has messages ready
- Look for errors in extension popup

### Followers not scraping

- Make sure you're on your profile page
- Check if followers list is accessible
- Verify initial scraping hasn't already been done

## Development

### Testing

1. Load extension in Chrome
2. Open developer console (F12)
3. Check background script: chrome://extensions/ → "Inspect views: service worker"
4. Check content script: Instagram tab console
5. Monitor network tab for API calls

### Debugging

- All major actions are logged to console
- Status updates shown in popup
- API errors displayed in popup notifications

## Security Notes

- API key stored in Chrome local storage
- All API calls use HTTPS
- No credentials stored in code
- Extension only has permissions for Instagram and AISetter domains

## Support

For issues or questions:

1. Check the browser console for errors
2. Verify API key is correct
3. Ensure Instagram is logged in
4. Contact AISetter support

## Version

Current Version: 1.0.0

## License

Private - AISetter Internal Use Only
