import Browser from 'webextension-polyfill'
import { GeneralConfig } from '@shared/config'
import DataLayer from '@shared/datalayer'
import { MessageTypes } from '@shared/messaging'
import delay from './Utils/delay'
import { ApiService } from '@shared/api-service'
import { FollowUpManager } from './follow-up-manager'

const heartbeatAlarmName = 'heartbeat'
const followUpAlarmName = 'followUpCheck'
let heartbeatScheduledAlreadyRunning = false
let scheduledScanIsAlive = false
let running = false;
let followerCheckIsRunning = false;
let monitor_start_timestamp = 0;
let conversationGatheringPollingActive = false;

// Hard stop mechanism - track all active operations
let activeTimeouts: Set<NodeJS.Timeout> = new Set();
let activeIntervals: Set<NodeJS.Timeout> = new Set();
let abortController: AbortController | null = null;

// Create heartbeat alarm if it doesn't exist
async function createHeartbeatAlarm() {
  const alarms = await Browser.alarms.getAll()
  const alarmExists = alarms.some((alarm) => alarm.name === heartbeatAlarmName)

  if (!alarmExists) {
    Browser.alarms.create(heartbeatAlarmName, {
      periodInMinutes: 0.5,
    })
  }
}

// Create follow-up check alarm if it doesn't exist
async function createFollowUpAlarm() {
  const alarms = await Browser.alarms.getAll()
  const alarmExists = alarms.some((alarm) => alarm.name === followUpAlarmName)

  if (!alarmExists) {
    Browser.alarms.create(followUpAlarmName, {
      periodInMinutes: 1,
    })
  }
}

async function setStatus(text: string) {
  let config: any = await DataLayer.getConfig()
  const updatedConfig = {
    ...config,
    status: text,
  }
  await DataLayer.setConfig(updatedConfig)
}

// Enhanced status function with immediate broadcasting
async function broadcastStatusChange(status: string, power: boolean = running) {
  await setStatus(status)
  
  // Notify popup immediately for instant feedback
  try {
    Browser.runtime.sendMessage({
      type: 'STATUS_CHANGED',
      data: { status, power }
    }).catch(() => {
      // Ignore errors if no listeners
    })
  } catch (error) {
    // Ignore broadcasting errors
  }
}

// Cancellable delay for hard stop mechanism
async function cancellableDelay(ms: number): Promise<void> {
  if (abortController?.signal.aborted) {
    throw new Error('Operation cancelled')
  }
  
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(resolve, ms)
    activeTimeouts.add(timeout)
    
    const cleanup = () => {
      clearTimeout(timeout)
      activeTimeouts.delete(timeout)
    }
    
    // Listen for abort signal
    if (abortController) {
      abortController.signal.addEventListener('abort', () => {
        cleanup()
        reject(new Error('Operation cancelled'))
      })
    }
    
    // Clean up on completion
    timeout.unref?.()
  })
}

// Enhanced API call with retry mechanism
async function apiCallWithRetry<T>(
  apiCall: () => Promise<T>,
  operationName: string,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error | null = null
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await apiCall()
      return result
    } catch (error) {
      lastError = error as Error
      
      if (attempt === maxRetries) {
        break
      }
      
      // Exponential backoff with jitter
      const delay = baseDelay * Math.pow(2, attempt - 1) + Math.random() * 1000
      
      try {
        await cancellableDelay(delay)
      } catch (cancelError) {
        throw cancelError
      }
    }
  }
  
  const finalError = lastError || new Error(`${operationName} failed after ${maxRetries} attempts`)
  throw finalError
}

// Enhanced message sending with retry, error recovery
async function sendMessageWithRetry(
  tabId: number,
  message: any,
  operationName: string,
  maxRetries: number = 3
): Promise<any> {
  let lastError: Error | null = null
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // Check if tab still exists
      await Browser.tabs.get(tabId)
      
      const result = await Browser.tabs.sendMessage(tabId, message)
      return result
    } catch (error) {
      lastError = error as Error
      
      if (attempt === maxRetries) {
        break
      }
      
      // Try to recover based on error type
      if (error instanceof Error) {
        if (error.message?.includes('Receiving end does not exist')) {
          const delayMs = 3000 + (attempt * 2000)
          await cancellableDelay(delayMs)
        } else if (error.message?.includes('The tab was closed')) {
          throw error
        } else {
          const delayMs = 1000 * attempt
          await cancellableDelay(delayMs)
        }
      }
    }
  }
  
  const finalError = lastError || new Error(`${operationName} failed after ${maxRetries} attempts`)
  throw finalError
}

// Enhanced hard stop function with comprehensive cleanup
async function performHardStop() {
  try {
    // Phase 1: Set all control flags to stop immediately
    running = false
    scheduledScanIsAlive = false
    followerCheckIsRunning = false
    conversationGatheringPollingActive = false
    heartbeatScheduledAlreadyRunning = false
    
    // Phase 2: Abort all pending async operations
    if (abortController) {
      abortController.abort()
      abortController = null
    }
    
    // Phase 3: Clear all timeouts and intervals
    activeTimeouts.forEach(timeout => {
      try {
        clearTimeout(timeout)
      } catch (e) {
        // Ignore errors
      }
    })
    activeTimeouts.clear()
    
    activeIntervals.forEach(interval => {
      try {
        clearInterval(interval)
      } catch (e) {
        // Ignore errors
      }
    })
    activeIntervals.clear()
    
    // Phase 4: Reset state variables
    const previousState = { monitor_start_timestamp, lastScrapingCheckTime }
    monitor_start_timestamp = 0
    lastScrapingCheckTime = 0
    
    // Phase 5: Clean up temporary storage
    try {
      const itemsToRemove = [
        'processedActivityFollowers',
        'lastScrapingSession',
        'nextScrapingAllowedAt'
      ]
      
      await Browser.storage.local.remove(itemsToRemove)
    } catch (error) {
      // Ignore storage errors
    }
    
    // Phase 6: Update session tracking
    try {
      const sessionData = {
        lastSessionTimestamp: Date.now(),
        hadPreviousSession: true
      }
      await Browser.storage.local.set(sessionData)
    } catch (error) {
      // Ignore storage errors
    }
    
    // Phase 7: Send stop message to all content scripts
    try {
      await sendStopMessage()
    } catch (error) {
      // Ignore errors
    }
    
    // Phase 8: Clean up tabs
    try {
      const secTabId = await DataLayer.getSecTabId()
      if (secTabId) {
        const tabId = parseInt(secTabId)
        try {
          const tab = await Browser.tabs.get(tabId)
          if (tab) {
            await DataLayer.setSecTabId('')
          }
        } catch (tabError) {
          await DataLayer.setSecTabId('')
        }
      }
    } catch (error) {
      // Ignore tab cleanup errors
    }
    
    // Phase 9: Update extension status
    await broadcastStatusChange('🛑 Extension stopped - All resources cleaned up', false)
    
    // Phase 10: Update API status with retry mechanism
    try {
      const apiKeyResult = await Browser.storage.local.get(['apiKey'])
      if (apiKeyResult.apiKey) {
        await apiCallWithRetry(
          () => ApiService.updateExtensionStatus(
            apiKeyResult.apiKey,
            'STOPPED',
            'Extension manually stopped - All processes terminated',
            false
          ),
          'Update API Status on Stop',
          2,
          1000
        )
      }
    } catch (error) {
      // Ignore API update errors
    }
    
  } catch (error) {
    throw error
  }
}

// Enhanced cleanup function for extension shutdown/unload
async function performExtensionShutdown() {
  try {
    // Perform hard stop if still running
    if (running) {
      await performHardStop()
    }
    
    // Additional shutdown-specific cleanup
    await Browser.alarms.clearAll()
    
    // Update shutdown timestamp
    await Browser.storage.local.set({
      lastShutdownTimestamp: Date.now(),
      shutdownGracefully: true
    })
    
  } catch (error) {
    // Mark shutdown as non-graceful
    try {
      await Browser.storage.local.set({
        lastShutdownTimestamp: Date.now(),
        shutdownGracefully: false
      })
    } catch (storageError) {
      // Ignore storage errors
    }
  }
}

// Enhanced cleanup function for window/tab closing
async function performWindowCleanup() {
  try {
    // Clear any window-specific state
    if (running) {
      // Don't perform full stop, just pause operations
      scheduledScanIsAlive = false
      followerCheckIsRunning = false
      
      // Update status to indicate paused state
      await setStatus('⏸️ Paused - Browser window closed')
    }
    
  } catch (error) {
    // Ignore window cleanup errors
  }
}

// Listener cleanup function
function cleanupListeners() {
  try {
    // Note: Chrome extension listeners are automatically cleaned up
    // but we can track active listeners if needed
  } catch (error) {
    // Ignore listener cleanup errors
  }
}

// Register shutdown handlers
Browser.runtime.onSuspend?.addListener(() => {
  performExtensionShutdown()
})

Browser.windows?.onRemoved?.addListener(() => {
  performWindowCleanup()
})

// Enhanced session recovery function
async function performSessionRecovery() {
  try {
    const sessionData = await Browser.storage.local.get([
      'lastShutdownTimestamp',
      'shutdownGracefully',
      'lastSessionTimestamp',
      'hadPreviousSession'
    ])
    
    const now = Date.now()
    const lastShutdown = sessionData.lastShutdownTimestamp
    const shutdownGraceful = sessionData.shutdownGracefully
    const lastSession = sessionData.lastSessionTimestamp
    
    if (lastShutdown) {
      const downtime = Math.round((now - lastShutdown) / (1000 * 60)) // minutes
      const downtimeHours = Math.round(downtime / 60 * 10) / 10 // hours with 1 decimal
      
      if (!shutdownGraceful) {
        // Clear any potentially corrupted temporary data
        const itemsToRemove = [
          'processedActivityFollowers',
          'nextScrapingAllowedAt'
        ]
        
        await Browser.storage.local.remove(itemsToRemove)
      }
      
      // Store recovery info for use in startup logic
      const recoveryInfo = {
        downtimeMinutes: downtime,
        wasGracefulShutdown: shutdownGraceful,
        recoveryTimestamp: now
      }
      
      await Browser.storage.local.set({ recoveryInfo })
    }
    
  } catch (error) {
    // Don't rethrow - session recovery failure shouldn't prevent extension startup
  }
}

// Perform session recovery on startup
performSessionRecovery()

createHeartbeatAlarm()
createFollowUpAlarm()

const getMainTab = async () => {
  const mainTabId = await DataLayer.getMainTabId()
  let tab = null
  if (mainTabId) {
    try {
      tab = await Browser.tabs.get(parseInt(mainTabId))
    } catch (error) {
      // Tab was closed, create a new one
      tab = null
    }
  }

  if (!tab) {
    tab = await Browser.tabs.create({
      url: 'https://www.instagram.com/direct/inbox/',
      active: false,
    })
    await DataLayer.setMainTabId(tab.id!.toString())
  }
  
  await Browser.tabs.update(tab.id!, { autoDiscardable: false });
  await delay(2000);
  return tab;
}

async function waitForTabToLoad(tab: Browser.Tabs.Tab) {
  if (!tab || !tab.id) {
    throw new Error('Invalid tab object.')
  }

  const tabInfo = await Browser.tabs.get(tab.id)
  if (tabInfo.status === 'complete') {
    return tab
  }

  await new Promise((resolve) => {
    const listener = (tabId: any, changeInfo: any) => {
      if (tabId === tab.id && changeInfo.status === 'complete') {
        Browser.tabs.onUpdated.removeListener(listener)
        resolve(true)
      }
    }
    Browser.tabs.onUpdated.addListener(listener)
  })

  return tab
}

// Get random delay between min and max values
const getRandomDelay = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

// Check if current time is within natural pause period
const isWithinNaturalPause = (pauseStart: string, pauseStop: string): boolean => {
  const now = new Date()
  const currentTime = now.getHours() * 60 + now.getMinutes()
  
  const [startHour, startMin] = pauseStart.split(':').map(Number)
  const [stopHour, stopMin] = pauseStop.split(':').map(Number)
  
  const startTime = startHour * 60 + startMin
  const stopTime = stopHour * 60 + stopMin
  
  if (startTime <= stopTime) {
    return currentTime >= startTime && currentTime <= stopTime
  } else {
    // Overnight pause (e.g., 23:00 to 07:00)
    return currentTime >= startTime || currentTime <= stopTime
  }
}

// Check if there are messages to send
const checkIfHasMessages = async (apiKey: string): Promise<boolean> => {
  try {
    const messagesToSendResult = await ApiService.getMessagesToSend(apiKey)
    return !!(messagesToSendResult.success &&
              messagesToSendResult.data &&
              messagesToSendResult.data.length > 0)
  } catch (error) {
    return false
  }
}

// Check if initial scraping is needed and perform if required
const checkAndPerformInitialScraping = async (apiKey: string, settings: any) => {
  // Check scraping eligibility from API to get authoritative status
  const eligibilityResult = await ApiService.checkScrapingEligibility(
    apiKey,
    false, // not continuous mode
    false  // no messages consideration
  )
  
  if (eligibilityResult.success && eligibilityResult.data) {
    const eligibility = eligibilityResult.data
    
    // Only allow initial scraping if status is FRESH_START
    if (eligibility.extensionStatus !== 'FRESH_START') {
      await setStatus(`✅ Initial scraping already completed`)
      return
    }
    
    // If API says not eligible (even in FRESH_START), respect that decision
    if (!eligibility.isEligible) {
      await setStatus(`✅ Initial scraping already completed`)
      return
    }
  }

  // Check if we're in natural pause period
  const isInPause = isWithinNaturalPause(settings.pauseStart, settings.pauseStop)
  if (isInPause) {
    await setStatus(`😴 Natural pause active - No scraping (${settings.pauseStart} - ${settings.pauseStop})`)
    return
  }

  // All conditions passed - trigger initial scraping only
  try {
    if (eligibilityResult.success && eligibilityResult.data?.extensionStatus === 'FRESH_START') {
      await setStatus('🚀 Starting initial scraping session...')
      await performInitialScraping()
    } else {
      await setStatus('✅ Initial scraping already completed - no further scraping needed')
      return
    }
  } catch (error) {
    await setStatus('❌ Initial scraping error - will retry later')
  }
}

// This function is no longer needed - only initial scraping is performed

// Enhanced API-driven messaging flow
const runApiDrivenMessaging = async () => {
  if (scheduledScanIsAlive) {
    return
  }

  // Double check that the extension is still supposed to be running
  if (!running) {
    return
  }

  scheduledScanIsAlive = true

  try {
    // Get API key
    const apiKeyResult = await Browser.storage.local.get(['apiKey'])
    const apiKey = apiKeyResult.apiKey
    
    if (!apiKey) {
      await setStatus('❌ No API key - Extension disabled')
      return
    }

    // Get Chrome extension settings for timing
    const settingsResult = await apiCallWithRetry(
      () => ApiService.getChromeExtensionSettings(apiKey),
      'Get Chrome Extension Settings',
      3,
      1000
    )
    
    if (!settingsResult.success || !settingsResult.data) {
      await setStatus('❌ Failed to fetch settings - Extension stopped')
      return
    }

    const settings = settingsResult.data

    // Check if we're in natural pause period
    if (isWithinNaturalPause(settings.pauseStart, settings.pauseStop)) {
      await setStatus(`😴 Natural pause active (${settings.pauseStart} - ${settings.pauseStop})`)
      return
    }

    // 🚀 SIMPLIFIED: Check extension status and handle SCRAPED_50 → ACTIVE transition
    const extensionStatusResult = await ApiService.getExtensionStatus(apiKey)
    if (extensionStatusResult.success && extensionStatusResult.data) {
      const currentStatus = extensionStatusResult.data.extensionStatus
      
      if (currentStatus === 'SCRAPED_50') {
        // Update status directly to ACTIVE - no conversation gathering needed
        await ApiService.updateExtensionStatus(apiKey, 'ACTIVE',
          'Initial scraping complete - Ready for messaging',
          true)

        await setStatus('✅ Ready for messaging - Starting message processing...')

        // Continue with normal message processing (don't return, let it fall through)
      }
    }

    const messagesToSendResult = await ApiService.getMessagesToSend(apiKey)
    
    // Check if daily limit has been reached
    if (messagesToSendResult.metadata?.dailyLimitReached) {
      await setStatus('⏸️ Daily message limit reached - waiting for reset')
      return
    }
    
    if (!messagesToSendResult.success || !messagesToSendResult.data || messagesToSendResult.data.length === 0) {
      await setStatus('✅ No messages ready to send')
      return
    }

    const messagesToSend = messagesToSendResult.data

    let tab = await getMainTab()

    // Retry loop to ensure content script is ready with better error handling
    let attempts = 0;
    let started = false;
    const maxAttempts = 8; // Increased attempts
    
    while (attempts < maxAttempts && !started && running) {
      try {
        // First, check if tab is still valid and on Instagram
        const currentTab = await Browser.tabs.get(tab.id!);
        if (!currentTab.url?.includes('instagram.com')) {
          const freshTab = await getMainTab();
          tab = freshTab;
        }
        
        // Try to send the START_PROCESS message
        const response = await Browser.tabs.sendMessage(tab.id!, {
          type: MessageTypes.START_PROCESS,
          data: {},
        });
        
        started = true;
        
      } catch (error: any) {
        attempts++;
        const isLastAttempt = attempts >= maxAttempts;
        
        if (error.message?.includes('Receiving end does not exist')) {
          await delay(5000); // Longer delay for content script loading
        } else if (error.message?.includes('The tab was closed')) {
          try {
            tab = await getMainTab();
            await delay(2000);
          } catch (tabError) {
            if (isLastAttempt) break;
          }
        } else {
          await delay(3000);
        }
        
        // Force refresh the page if we're having persistent issues
        if (attempts === Math.floor(maxAttempts / 2)) {
          try {
            await Browser.tabs.reload(tab.id!);
            await delay(5000); // Wait for page to fully reload
          } catch (refreshError) {
            // Ignore refresh errors
          }
        }
      }
    }

    if (!started) {
      throw new Error(`Could not establish connection with content script after ${attempts} attempts. Content script may not be loaded or Instagram page may need refresh.`);
    }

    let messagesSent = 0
    let messagesSkipped = 0
    const messagesBeforeBreak = getRandomDelay(settings.messagesBeforeBreakMin, settings.messagesBeforeBreakMax)

    for (const messageToSend of messagesToSend) {
      if (!running) {
        break
      }

      // Check if we need a break
      if (messagesSent > 0 && messagesSent % messagesBeforeBreak === 0) {
        const breakDuration = getRandomDelay(settings.breakDurationMin, settings.breakDurationMax)
        await setStatus(`😴 Break time: ${breakDuration} minutes after ${messagesSent} messages (monitoring for new followers)`)
        
        // During break time, monitor for new followers using Activity Inbox
        const breakStartTime = Date.now()
        const breakDurationMs = breakDuration * 60 * 1000 // Convert to milliseconds
        
        while (Date.now() - breakStartTime < breakDurationMs) {
          // Check for new followers during break time
          await checkActivityInboxForNewFollowers()
          
          // Wait 30 seconds before next check or until break time is complete
          const remainingBreak = breakDurationMs - (Date.now() - breakStartTime)
          const nextCheckDelay = Math.min(30000, remainingBreak) // 30 seconds or remaining time
          
          if (nextCheckDelay > 0) {
            await delay(nextCheckDelay)
          }
        }
      }

      // Add delay between DMs (except for the first message)
      if (messagesSent > 0) {
        const timeBetweenDMs = getRandomDelay(settings.timeBetweenDMsMin, settings.timeBetweenDMsMax)
        await setStatus(`⏳ Waiting ${timeBetweenDMs} minutes before next DM (monitoring for new followers)`)
        
        // During wait time, monitor for new followers using Activity Inbox
        const waitStartTime = Date.now()
        const waitDuration = timeBetweenDMs * 60 * 1000 // Convert to milliseconds
        
        while (Date.now() - waitStartTime < waitDuration) {
          // Check for new followers during wait time
          await checkActivityInboxForNewFollowers()
          
          // Wait 30 seconds before next check or until wait time is complete
          const remainingWait = waitDuration - (Date.now() - waitStartTime)
          const nextCheckDelay = Math.min(30000, remainingWait) // 30 seconds or remaining time
          
          if (nextCheckDelay > 0) {
            await delay(nextCheckDelay)
          }
        }
      }

      if (!running) {
        break
      }
      
      try {
        // Step 0: Double-check contact status before sending (defensive programming)
        const contactStatusResult = await ApiService.getContactStatus(apiKey, messageToSend.id)
        
        if (!contactStatusResult.success) {
          messagesSkipped++
          continue
        }
        
        if (contactStatusResult.data?.stage === 'disqualified') {
          messagesSkipped++
          continue
        }
        
        // Step 1: Navigate to Profile
        await setStatus(`🧭 Navigating to profile: ${messageToSend.username}`)
        const profileUrl = `https://www.instagram.com/${messageToSend.username}/`
        await Browser.tabs.update(tab.id!, { url: profileUrl })
        await waitForTabToLoad(tab)
        await delay(4000)

        // Step 2: Click Message Button with retry
        await setStatus(`🖱️ Clicking message button for ${messageToSend.username}`)
        await sendMessageWithRetry(
          tab.id!,
          { type: MessageTypes.VISIT_USER_MESSAGES, data: { recipent_username: messageToSend.username } },
          `Visit Messages for ${messageToSend.username}`
        )
        await cancellableDelay(6000) // Wait for DM page to load

        // Step 3: Send the message(s)
        const batchMessages = messageToSend.message.split(' | ').map(msg => msg.trim()).filter(msg => msg.length > 0)
        let batchSuccess = true
        let sentMessagesCount = 0

        for (let i = 0; i < batchMessages.length; i++) {
          const individualMessage = batchMessages[i]
          
          if (!running) {
            batchSuccess = false
            break
          }
          
          await setStatus(`📤 Sending message ${i + 1}/${batchMessages.length} to ${messageToSend.username}`)
          
          const sendResult = await sendMessageWithRetry(
            tab.id!,
            {
              type: MessageTypes.MESSAGE_PROFILE,
              data: {
                recipent_id: messageToSend.username, // In this flow, this is the username
                text: individualMessage,
              },
            },
            `Send Message ${i + 1}/${batchMessages.length} to ${messageToSend.username}`
          )

          if (sendResult === true) {
            sentMessagesCount++
            if (i < batchMessages.length - 1) {
              await delay(25000) // Wait between message lines
            }
          } else {
            batchSuccess = false
            break
          }
        }

        if (batchSuccess && sentMessagesCount === batchMessages.length) {
          // Mark message as sent - handle both batch and batch_sequence types
          if (messageToSend.type === 'batch') {
            // For batch messages, indicate completion to trigger follow-up creation
            const markSentData = {
              contactId: messageToSend.id,
              messageType: 'batch_sequence' as const,
              sequenceNumber: batchMessages.length, // Total number of messages sent
              batchCompleted: true // This triggers follow-up creation
            }
            await ApiService.markMessageSent(apiKey, markSentData)
          } else if (messageToSend.type === 'followup') {
            // For follow-up messages, mark the specific follow-up as sent
            const markSentData = {
              contactId: messageToSend.id,
              messageType: 'followup' as const,
              followUpId: messageToSend.followUpId,
              sequenceNumber: messageToSend.sequenceNumber
            }
            await ApiService.markMessageSent(apiKey, markSentData)
          }

          messagesSent++
        } else {
          messagesSkipped++
        }

      } catch (error) {
        messagesSkipped++
      }
    }

    await setStatus(`✅ Completed - Sent: ${messagesSent}, Skipped: ${messagesSkipped}`)

  } catch (error) {
    await setStatus(`❌ Error in messaging: ${error instanceof Error ? error.message : 'Unknown error'}`)
  } finally {
    scheduledScanIsAlive = false
  }
}


const setupScheduledScan = async () => {
  // Don't start a new scan if one is already running
  if (scheduledScanIsAlive) {
    return
  }

  const config = await DataLayer.getConfig()
  
  // Check both config.power AND running state
  if (!config.power || !running) {
    running = false
    return
  }

  await runApiDrivenMessaging() // Call the new main function
}

const runScheduledScanHeartbeat = async () => {
  if (heartbeatScheduledAlreadyRunning) {
    return
  }
  heartbeatScheduledAlreadyRunning = true
  try {
    await setupScheduledScan()
  } catch (error) {
    // Ignore heartbeat errors
  }
  heartbeatScheduledAlreadyRunning = false
}

const sendStopMessage = async () => {
    try {
        const main_tab = await getMainTab()
        await Browser.tabs.sendMessage(main_tab.id!, {
            type: MessageTypes.STOP_PROCESS,
        })
    } catch(e) {
        // ignore
    }
}

// Helper function to send followers in batches respecting API limits
const sendFollowersInBatches = async (apiKey: string, followers: any[], context: string, isInitialScraping: boolean = false) => {
  if (followers.length === 0) return

  const BATCH_SIZE = 10 // API limit for lastScrapedUsernames
  const batches = []
  
  // Split followers into batches
  for (let i = 0; i < followers.length; i += BATCH_SIZE) {
    batches.push(followers.slice(i, i + BATCH_SIZE))
  }

  let previousBatchUsernames: string[] = []

  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i]

    const sendResult = await ApiService.sendScrapedFollowers(
      apiKey,
      batch,
      i * BATCH_SIZE, // startPosition
      followers.length, // totalFollowers
      i === batches.length - 1, // isComplete (true for last batch)
      previousBatchUsernames, // Send previous batch usernames for tracking
      isInitialScraping // Indicate if this is initial scraping
    )

    if (sendResult.success) {
      // Update previousBatchUsernames for next batch
      previousBatchUsernames = batch.map(f => f.instagramNickname).slice(-10) // Keep last 10
    }

    // Add delay between batches to avoid overwhelming the API
    if (i < batches.length - 1) {
      await delay(2000)
    }
  }
}

// Function to check for new followers via Activity Inbox (MONITORING)
const checkActivityInboxForNewFollowers = async () => {
  if (followerCheckIsRunning) {
    return []
  }

  followerCheckIsRunning = true

  try {
    // Get API key
    const apiKeyResult = await Browser.storage.local.get(['apiKey'])
    const apiKey = apiKeyResult.apiKey
    if (!apiKey) {
      return []
    }

    // Get main tab (don't create new tabs for monitoring)
    const tab = await getMainTab()
    
    // Get Instagram stories from activity inbox
    const inboxResult = await Browser.tabs.sendMessage(tab.id!, {
      type: MessageTypes.GET_INSTA_INBOX,
      data: { config: {} }
    })

    if (!inboxResult || inboxResult.status !== 'success') {
      return []
    }

    const stories = inboxResult.data || []

    // Get already processed followers from storage to avoid duplicates
    const processedResult = await Browser.storage.local.get(['processedActivityFollowers'])
    const processedFollowers = new Set(processedResult.processedActivityFollowers || [])

    // Filter stories to only new followers after monitor start
    const newFollowers = []
    let skippedOldFollowers = 0
    let skippedProcessedFollowers = 0

    for (const story of stories) {
      const story_timestamp = story.timestamp * 1000 // Convert to milliseconds
      
      // Check if this follower is from after monitoring started (dmPastFollowers logic)
      if (story_timestamp < monitor_start_timestamp) {
        skippedOldFollowers++
        continue
      }

      // Check if we've already processed this follower from Activity Inbox
      if (processedFollowers.has(story.username)) {
        skippedProcessedFollowers++
        continue
      }

      // This is a new follower since monitoring started and not yet processed
      newFollowers.push({
        instagramNickname: story.username,
        instagramId: story.id,
        isVerified: false,
        timestamp: story.timestamp,
        followedAt: new Date(story_timestamp)
      })
    }

    // Send new followers to API if any (in batches to respect API limits)
    if (newFollowers.length > 0) {
      await sendFollowersInBatches(apiKey, newFollowers, 'MONITORING')
      
      // Mark these followers as processed to avoid reprocessing
      const newProcessedFollowers = newFollowers.map(f => f.instagramNickname)
      processedFollowers.forEach(username => newProcessedFollowers.push(username))
      
      // Keep only the last 1000 processed followers to avoid unbounded growth
      const limitedProcessedFollowers = newProcessedFollowers.slice(-1000)
      
      await Browser.storage.local.set({ 
        processedActivityFollowers: limitedProcessedFollowers 
      })
    }

    return newFollowers

  } catch (error) {
    return []
  } finally {
    followerCheckIsRunning = false
  }
}

// Catch-up scraping removed - only activity inbox monitoring remains

// Function to poll conversation gathering status and handle transitions
const pollConversationGatheringStatus = async (apiKey: string) => {
  if (conversationGatheringPollingActive) {
    return
  }

  conversationGatheringPollingActive = true

  try {
    await setStatus('🔄 Checking conversation gathering progress...')

    // Keep polling until conversations are gathered or an error occurs
    let maxPollAttempts = 180 // 180 attempts * 10 seconds = 30 minutes max wait
    let pollAttempts = 0
    let gatheringComplete = false

    while (pollAttempts < maxPollAttempts && running && !gatheringComplete) {
      pollAttempts++
      
      try {
        const gatheringResult = await ApiService.checkConversationGatheringStatus(apiKey)
        
        if (!gatheringResult.success) {
          await setStatus(`⚠️ Error checking conversation gathering: ${gatheringResult.message}`)
          
          // Wait before retrying (cancellable for hard stop)
          await cancellableDelay(10000)
          continue
        }

        const status = gatheringResult.data
        if (!status) {
          await setStatus('⚠️ No conversation gathering status data')
          await cancellableDelay(10000)
          continue
        }

        if (status.error) {
          await setStatus(`❌ Conversation gathering error: ${status.error}`)
          gatheringComplete = true // Exit polling on error
          break
        }

        if (status.isComplete) {
          // Update extension status to CONVERSATIONS_GATHERED_READY
          try {
            await ApiService.updateExtensionStatus(apiKey, 'CONVERSATIONS_GATHERED_READY', 
              'All conversations processed - ready for attack list creation', 
              true)
            
            await setStatus('✅ All conversations processed - Ready for attack list creation!')
            gatheringComplete = true
            
          } catch (error) {
            await setStatus('⚠️ Conversations processed but status update failed')
          }
          
          break
        }

        if (status.isGathering) {
          // Show progress if available
          let progressMessage = '🔄 Processing Instagram conversations'
          
          if (status.progress) {
            const { processedContacts, totalContacts, percentComplete } = status.progress
            progressMessage += ` (${processedContacts}/${totalContacts} - ${percentComplete}%)`
          }
          
          if (status.estimatedTimeRemaining) {
            progressMessage += ` - Est. ${status.estimatedTimeRemaining} remaining`
          }
          
          await setStatus(progressMessage)
        } else {
          // Not gathering and not complete - may be waiting to start
          await setStatus('🔄 Waiting for conversation gathering to begin...')
        }

        // Wait 10 seconds before next poll (cancellable for hard stop)
        await cancellableDelay(10000)

      } catch (error) {
        await setStatus(`⚠️ Error polling conversation status: ${error instanceof Error ? error.message : 'Unknown error'}`)
        
        // Wait before retrying (cancellable for hard stop)
        await cancellableDelay(10000)
      }
    }

    if (pollAttempts >= maxPollAttempts && !gatheringComplete) {
      await setStatus('⏰ Conversation gathering timeout - Please check dashboard')
    }

  } catch (error) {
    await setStatus(`❌ Conversation gathering polling error: ${error instanceof Error ? error.message : 'Unknown error'}`)
  } finally {
    conversationGatheringPollingActive = false
  }
}

// Function to perform initial scraping of 250 most recent followers (FRESH_START only)
const performInitialScraping = async () => {
  if (followerCheckIsRunning) {
    return
  }

  // Check if initial scraping was already completed
  const localCheck = await Browser.storage.local.get(['initialScrapingCompleted'])
  if (localCheck.initialScrapingCompleted) {
    await setStatus('✅ Initial scraping already completed')
    return
  }

  followerCheckIsRunning = true

  try {
    await setStatus('🚀 Initial scraping: Getting 50 most recent followers...')
    
    // Clear any existing progressive scraping state to start fresh
    await Browser.storage.local.set({
      scrapingPosition: 0,
      totalFollowersScraped: 0,
      lastKnownFollowers: []
    })

    // Get API key
    const apiKeyResult = await Browser.storage.local.get(['apiKey'])
    const apiKey = apiKeyResult.apiKey
    if (!apiKey) {
      return
    }

    // Get or create Instagram tab for scraping
    const instagramTabId = await DataLayer.getSecTabId()
    let tab = null
    
    if (instagramTabId) {
      try {
        tab = await Browser.tabs.get(parseInt(instagramTabId))
        if (!tab.url?.includes('instagram.com')) {
          await Browser.tabs.update(tab.id!, { url: 'https://www.instagram.com/' })
        }
      } catch (error) {
        tab = await Browser.tabs.create({
          url: 'https://www.instagram.com/',
          active: false,
        })
        await DataLayer.setSecTabId(tab.id!.toString())
      }
    } else {
      tab = await Browser.tabs.create({
        url: 'https://www.instagram.com/',
        active: false,
      })
      await DataLayer.setSecTabId(tab.id!.toString())
    }

    // 🔥 CRITICAL FIX: Ensure tab is active during scraping to prevent throttling
    if (tab && tab.id) {
      try {
        // Make tab active (focused) to prevent browser throttling
        await Browser.tabs.update(tab.id, { active: true })
        if (tab.windowId) {
          await Browser.windows.update(tab.windowId, { focused: true })
        }
        
        // Wait for tab to become fully active
        await delay(1000)
      } catch (error) {
        // Continue anyway
      }
    }

    await waitForTabToLoad(tab)
    await delay(3000)

    // Get user's profile username
    let username
    try {
      const profileLinkResult = await Browser.tabs.sendMessage(tab.id!, {
        type: MessageTypes.GET_PROFILE_URL
      })
      
      if (profileLinkResult) {
        username = profileLinkResult
      } else {
        // Navigate to Instagram home and look for profile navigation
        await Browser.tabs.update(tab.id!, { url: 'https://www.instagram.com/' })
        await waitForTabToLoad(tab)
        await delay(3000)
        
        const profileNavResult = await Browser.tabs.sendMessage(tab.id!, {
          type: MessageTypes.GET_PROFILE_URL
        })
        
        if (profileNavResult) {
          username = profileNavResult
        }
      }
    } catch (error) {
      // Ignore profile info errors
    }

    if (!username) {
      // Try clicking profile element with "Profil" or "Profile" text
      try {
        const profileClickResult = await Browser.tabs.sendMessage(tab.id!, {
          type: MessageTypes.CLICK_PROFILE_ELEMENT
        })

        if (profileClickResult && profileClickResult.status === 'success') {
          username = profileClickResult.username
        } else {
          await setStatus('❌ Could not find or click profile element - initial scraping failed')
          return
        }
      } catch (error) {
        await setStatus('❌ Error clicking profile element - initial scraping failed')
        return
      }
    }

    // Navigate to user's profile if not already there
    if (!tab.url?.includes(`/${username}/`)) {
      try {
        const profileClickResult = await Browser.tabs.sendMessage(tab.id!, {
          type: MessageTypes.CLICK_PROFILE_ELEMENT
        })

        if (profileClickResult && profileClickResult.status === 'success') {
          // Successfully navigated via clicking
        } else {
          await Browser.tabs.update(tab.id!, {
            url: `https://www.instagram.com/${username}/`
          })
          await waitForTabToLoad(tab)
          await delay(3000)
        }
      } catch (error) {
        await Browser.tabs.update(tab.id!, {
          url: `https://www.instagram.com/${username}/`
        })
        await waitForTabToLoad(tab)
        await delay(3000)
      }
    }

    // Open followers panel
    const openResult = await Browser.tabs.sendMessage(tab.id!, {
      type: MessageTypes.OPEN_FOLLOWERS_PANEL,
      data: { username }
    })

    if (openResult.status !== 'success') {
      await setStatus('❌ Failed to open followers panel - initial scraping failed')
      return
    }

    // 🚀 INITIAL SCRAPING: Always get 50 most recent followers (skipCount = 0)
    await setStatus('🚀 Scraping 50 most recent followers...')
    const scrapingResult = await Browser.tabs.sendMessage(tab.id!, {
      type: MessageTypes.GET_FOLLOWERS_LIST_BY_COUNT,
      data: {
        count: 50,
        skipCount: 0 // Always start from the beginning for initial scraping
      }
    })

    if (!scrapingResult || !scrapingResult.followers || !Array.isArray(scrapingResult.followers)) {
      await setStatus('❌ Failed to scrape followers - initial scraping failed')
      return
    }

    const scrapedFollowers = scrapingResult.followers

    // Convert to API format
    const initialFollowers = []
    for (const followerUrl of scrapedFollowers) {
      const username = followerUrl.split('/').filter(Boolean).pop()
      if (username) {
        initialFollowers.push({
          instagramNickname: username,
          isVerified: false
        })
      }
    }

    // Send initial followers to API
    if (initialFollowers.length > 0) {
      await setStatus(`🚀 Sending ${initialFollowers.length} initial followers to API...`)
      await sendFollowersInBatches(apiKey, initialFollowers, 'INITIAL_SCRAPING', true)
      
      // Store these as baseline followers
      const storedFollowers = initialFollowers.map(f => ({ username: f.instagramNickname })).slice(0, 500)
      await Browser.storage.local.set({
        lastKnownFollowers: storedFollowers,
        scrapingPosition: 50, // Set position after initial scraping
        totalFollowersScraped: initialFollowers.length,
        lastScrapingSession: new Date().toISOString(),
        initialScrapingCompleted: true
      })
      
      // Update extension status to indicate initial scraping is complete
      try {
        await ApiService.updateExtensionStatus(apiKey, 'SCRAPED_50',
          `Initial scraping completed: ${initialFollowers.length} followers`,
          true, {
            totalFollowersScraped: initialFollowers.length,
            lastScrapedPosition: 50,
            allFollowersScraped: true // Mark as complete since we only do 50
          })
        
        await setStatus(`✅ Initial scraping complete: ${initialFollowers.length} followers sent to API`)
        
        // 🚀 INITIAL: Transition directly to ACTIVE status for immediate messaging
        // Update status directly to ACTIVE to begin messaging immediately
        await ApiService.updateExtensionStatus(apiKey, 'ACTIVE',
          'Initial scraping complete - Ready for messaging',
          true)
        
        await setStatus('✅ Ready for messaging - Initial setup complete')
        
      } catch (error) {
        await setStatus(`⚠️ Initial scraping complete but status update failed: ${initialFollowers.length} followers`)
      }
    } else {
      await setStatus('⚠️ Initial scraping found no followers')
      
      // Still update status to indicate scraping was attempted
      try {
        await ApiService.updateExtensionStatus(apiKey, 'ACTIVE', 
          'Initial scraping found no followers', 
          true, {
            totalFollowersScraped: 0,
            lastScrapedPosition: 0,
            allFollowersScraped: false
          })
      } catch (error) {
        // Ignore status update errors
      }
    }

  } catch (error) {
    await setStatus('❌ Error during initial scraping')
  } finally {
    followerCheckIsRunning = false
  }
}

// This function is no longer needed - only activity inbox monitoring remains

Browser.runtime.onMessage.addListener(async (message) => {
  // Only log non-status messages to reduce console spam
  if (message.type !== MessageTypes.GET_STATUS &&
      message.type !== MessageTypes.GET_API_KEY &&
      message.type !== MessageTypes.GET_API_STATUS) {
    // Message received
  }

  await createHeartbeatAlarm()

  if (message.type === MessageTypes.START_PROCESS) {
    let config = await DataLayer.getConfig()
    await DataLayer.setConfig({
      ...config,
      power: true,
    })
    running = true
    
    // Create new abort controller for this session
    abortController = new AbortController()
    
    // 🔥 FIX: Perform catch-up scraping after every restart to capture missed followers
    const lastSession = await Browser.storage.local.get(['lastSessionTimestamp', 'hadPreviousSession'])
    
    monitor_start_timestamp = Date.now() // Always set current time as monitoring start
    
    if (lastSession.hadPreviousSession && lastSession.lastSessionTimestamp) {
      const offlineDuration = monitor_start_timestamp - lastSession.lastSessionTimestamp
      const offlineHours = Math.round(offlineDuration / (1000 * 60 * 60))
      
      // Don't clear processed followers - we want to avoid reprocessing
      
      // Note: Catch-up scraping removed - only activity inbox monitoring remains
      
    } else {
      // Fresh start - clear processed followers list
      await Browser.storage.local.set({ processedActivityFollowers: [] })
    }
    
    // Mark current session timestamp
    await Browser.storage.local.set({
      lastSessionTimestamp: monitor_start_timestamp,
      hadPreviousSession: true
    })
    
    await broadcastStatusChange('🚀 Extension started', true)
    
    // 🔥 FIX: Check extension status from API before starting scraping
    try {
      const apiKeyResult = await Browser.storage.local.get(['apiKey'])
      const apiKey = apiKeyResult.apiKey
      
      if (apiKey) {
        const statusResult = await ApiService.getExtensionStatus(apiKey)
        
        if (statusResult.success && statusResult.data) {
          const apiStatus = statusResult.data
          
          // Update local scraping position from API
          if (apiStatus.lastScrapedPosition !== undefined) {
            await Browser.storage.local.set({
              scrapingPosition: apiStatus.lastScrapedPosition,
              totalFollowersScraped: apiStatus.totalFollowersScraped || 0
            })
          }
          
          // Only perform initial scraping if in FRESH_START status
          if (apiStatus.extensionStatus === 'FRESH_START') {
            setTimeout(async () => {
              await performInitialScraping()
            }, 5000) // Delay to let extension fully initialize
          } else if (apiStatus.extensionStatus === 'SCRAPED_50') {
            setTimeout(async () => {
              await ApiService.updateExtensionStatus(apiKey, 'ACTIVE',
                'Extension restarted - Ready for messaging',
                true)
              await setStatus('✅ Ready for messaging - Extension restarted')
            }, 5000) // Delay to let extension fully initialize
          }
        } else {
          // If we can't get status, don't assume anything - wait for explicit confirmation
          await setStatus('⚠️ Could not verify extension status - scraping disabled')
        }
      }
    } catch (error) {
      // Ignore status check errors
    }
    
    return true
  } else if (message.type === MessageTypes.STOP_PROCESS) {
    try {
      // Perform comprehensive hard stop with all cleanup phases
      await performHardStop()
      
      // Update config to reflect stopped state
      let config = await DataLayer.getConfig()
      await DataLayer.setConfig({
        ...config,
        power: false,
        status: 'Extension stopped - All resources cleaned up',
      })
      
      // Additional cleanup for user-initiated stop
      
      // Clean up any remaining listeners
      cleanupListeners()
      
      // Record graceful user stop
      await Browser.storage.local.set({
        lastUserStopTimestamp: Date.now(),
        userStoppedGracefully: true
      })
      
      return { success: true, message: 'Extension stopped with comprehensive cleanup' }
      
    } catch (error) {
      // Fallback to basic config update
      let config = await DataLayer.getConfig()
      await DataLayer.setConfig({
        ...config,
        power: false,
        status: 'Extension stopped - Cleanup error occurred',
      })
      
      // Record non-graceful stop
      try {
        await Browser.storage.local.set({
          lastUserStopTimestamp: Date.now(),
          userStoppedGracefully: false,
          lastStopError: error instanceof Error ? error.message : 'Unknown error'
        })
      } catch (storageError) {
        // Ignore storage errors
      }
      
      return { success: false, message: 'Extension stopped but cleanup errors occurred' }
    }
  } else if (message.type === MessageTypes.GET_COOKIE) {
    let cookie = await Browser.cookies.get({ url: message.data.url, name: message.data.name })
    return cookie?.value
  } else if (message.type === MessageTypes.SAVE_SETTINGS) {
    let config = await DataLayer.getConfig()
    await DataLayer.setConfig({
      ...config,
      ...message.data,
    })
    return true
  } else if (message.type === MessageTypes.GET_SETTINGS) {
    let config = await DataLayer.getConfig()
    return config
  } else if (message.type === MessageTypes.GET_STATUS) {
    // Don't log GET_STATUS to reduce console spam
    let config = await DataLayer.getConfig()
    return {
      ...config,
      power: running,
      status: (await DataLayer.getConfig()).status,
    }
  } else if (message.type === MessageTypes.SAVE_API_KEY) {
    await Browser.storage.local.set({ apiKey: message.data.apiKey })
    return true
  } else if (message.type === MessageTypes.GET_API_KEY) {
    // Don't log GET_API_KEY to reduce console spam
    const result = await Browser.storage.local.get(['apiKey'])
    return result.apiKey || ''
  } else if (message.type === MessageTypes.VERIFY_API_KEY) {
    try {
      const result = await ApiService.verifyApiKey(message.data.apiKey)
      const apiStatus = {
        isAuthenticated: result.success,
        organizationName: result.data?.organizationName,
        organizationSlug: result.data?.organizationSlug,
        lastVerified: new Date().toISOString(),
        error: result.success ? undefined : result.message
      }
      await Browser.storage.local.set({ apiStatus })
      return apiStatus
    } catch (error) {
      const apiStatus = {
        isAuthenticated: false,
        error: 'Failed to verify API key'
      }
      await Browser.storage.local.set({ apiStatus })
      return apiStatus
    }
  } else if (message.type === MessageTypes.GET_API_STATUS) {
    // Don't log GET_API_STATUS to reduce console spam
    const result = await Browser.storage.local.get(['apiStatus'])
    return result.apiStatus || { isAuthenticated: false }
  } else if (message.type === MessageTypes.RESET_SCRAPING_PROGRESS) {
      // Reset progressive scraping position to start from beginning
      await Browser.storage.local.set({
        scrapingPosition: 0,
        totalFollowersScraped: 0,
        lastScrapingSession: new Date().toISOString()
      })
      
      await setStatus('✅ Progressive scraping position reset - will start from beginning')
      
      return { success: true, message: 'Progressive scraping position reset to start from the beginning.' }
  } else if (message.type === MessageTypes.CLEAR_SCRAPING_WAIT) {
      // Clear the local storage nextScrapingAllowedAt to sync with API reset
      await Browser.storage.local.remove(['nextScrapingAllowedAt'])
      
      await setStatus('✅ Scraping wait period cleared - ready to scrape')
      
      return { success: true, message: 'Local scraping wait period cleared successfully.' }
  } else if (message.type === MessageTypes.START_FOLLOWER_SCRAPING) {
      // Manual trigger for scraping check - get API key and settings
      try {
        const apiKeyResult = await Browser.storage.local.get(['apiKey'])
        const apiKey = apiKeyResult.apiKey
        
        if (!apiKey) {
          await setStatus('❌ No API key - Cannot check scraping')
          return { success: false, message: 'No API key configured' }
        }

        const settingsResult = await ApiService.getChromeExtensionSettings(apiKey)
        
        if (!settingsResult.success || !settingsResult.data) {
          await setStatus('❌ Failed to fetch settings - Cannot check scraping')
          return { success: false, message: 'Failed to fetch settings from API' }
        }

        const settings = settingsResult.data
        
        // Trigger the initial scraping check manually (if needed)
        await checkAndPerformInitialScraping(apiKey, settings)
        
        return { success: true, message: 'Scraping check triggered successfully' }
      } catch (error) {
        await setStatus('❌ Error checking scraping conditions')
        return { success: false, message: 'Error checking scraping conditions' }
      }
  } else if (message.type === MessageTypes.CHECK_CONVERSATION_GATHERING_STATUS) {
      try {
        const apiKeyResult = await Browser.storage.local.get(['apiKey'])
        const apiKey = apiKeyResult.apiKey
        
        if (!apiKey) {
          return { success: false, message: 'No API key configured' }
        }

        const result = await ApiService.checkConversationGatheringStatus(apiKey)
        return result
      } catch (error) {
        return { success: false, message: 'Error checking conversation gathering status' }
      }
  } else if (message.type === MessageTypes.POLL_CONVERSATION_GATHERING) {
      try {
        const apiKeyResult = await Browser.storage.local.get(['apiKey'])
        const apiKey = apiKeyResult.apiKey
        
        if (!apiKey) {
          await setStatus('❌ No API key - Cannot poll conversation gathering')
          return { success: false, message: 'No API key configured' }
        }

        // Start polling in the background
        if (!conversationGatheringPollingActive) {
          setTimeout(async () => {
            await pollConversationGatheringStatus(apiKey)
          }, 1000)
          
          return { success: true, message: 'Conversation gathering polling started' }
        } else {
          return { success: false, message: 'Conversation gathering polling already active' }
        }
      } catch (error) {
        await setStatus('❌ Error starting conversation gathering polling')
        return { success: false, message: 'Error starting conversation gathering polling' }
      }
  } else if (message.type === MessageTypes.TEST_MULTI_ORG_ISOLATION) {
      try {
        const apiKeyResult = await Browser.storage.local.get(['apiKey'])
        const apiKey = apiKeyResult.apiKey
        
        if (!apiKey) {
          return { success: false, message: 'No API key configured for testing' }
        }
        
        // Test multi-organization isolation using our test endpoint
        const baseUrl = 'https://app.aisetter.pl' // Same as ApiService BASE_URL
        const response = await apiCallWithRetry(
          () => fetch(`${baseUrl}/api/test/multi-org-isolation`, {
            method: 'GET',
            headers: {
              'X-API-Key': apiKey,
              'Content-Type': 'application/json'
            }
          }),
          'Multi-Organization Isolation Test',
          2,
          1000
        )
        
        if (!response.ok) {
          const errorText = await response.text()
          
          return {
            success: false,
            message: `Test endpoint error: ${response.status} ${response.statusText}`,
            details: errorText
          }
        }
        
        const result = await response.json()
        
        if (result.success && result.summary?.allPassed) {
          await setStatus('✅ Multi-organization isolation verified - System is secure')
        } else {
          await setStatus('⚠️ Multi-organization isolation test detected security issues')
        }
        
        return {
          success: result.success,
          message: result.success
            ? `✅ Isolation verified: ${result.summary?.passedTests}/${result.summary?.totalTests} tests passed`
            : `❌ Isolation issues detected: ${result.summary?.failedTests} failed tests`,
          testResults: result.testResults,
          summary: result.summary,
          recommendations: result.recommendations
        }
        
      } catch (error) {
        await setStatus('❌ Error during multi-organization isolation test')
        return {
          success: false,
          message: 'Error during isolation test',
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }
  }
})

Browser.alarms.onAlarm.addListener(async (alarm) => {
  if (alarm.name === 'heartbeat') {
    setTimeout(async () => {
      await runScheduledScanHeartbeat()
    }, 1)
  } else if (alarm.name === 'followUpCheck') {
    // This can be re-implemented if follow-ups are needed
  // Note: newFollowersCheck alarm removed - now monitoring during wait times instead
  }
})
