/* eslint-disable */

import { useEffect, useState } from "react"
import { Badge } from "./ui/badge"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "./ui/card"
import { Progress } from "./ui/progress"
import Browser from "webextension-polyfill"
import { MessageTypes } from "../../shared/messaging"

interface StatusInfo {
  apiConnected: boolean;
  organizationName?: string;
  pendingFollowUps: number;
  lastFollowUpCheck?: string;
  extensionVersion: string;
  botRunning: boolean;
  currentStatus?: string;
  scrapingEnabled?: boolean;
  nextScrapingAllowed?: string | null;
  inNaturalPause?: boolean;
  conversationGathering?: {
    isGathering: boolean;
    isComplete: boolean;
    progress?: {
      totalContacts: number;
      processedContacts: number;
      percentComplete: number;
    };
    estimatedTimeRemaining?: string;
  };
}

export function StatusMonitor() {
  const [status, setStatus] = useState<StatusInfo>({
    apiConnected: false,
    pendingFollowUps: 0,
    extensionVersion: '1.0.0',
    botRunning: false
  })

  useEffect(() => {
    const updateStatus = async () => {
      try {
        // Get API status
        const apiStatus = await Browser.runtime.sendMessage({
          type: MessageTypes.GET_API_STATUS
        })

        // Get pending follow-ups
        const followUpsResult = await Browser.runtime.sendMessage({
          type: MessageTypes.FETCH_PENDING_FOLLOWUPS
        })

        // Get bot status
        const botStatus = await Browser.runtime.sendMessage({
          type: MessageTypes.GET_STATUS
        })

        // Get extension version
        const manifest = Browser.runtime.getManifest()

        // Get Chrome Extension settings for scraping info
        let scrapingInfo = { scrapingEnabled: false, nextScrapingAllowed: null }
        let conversationGatheringInfo = undefined
        
        if (apiStatus?.isAuthenticated) {
          try {
            // Check conversation gathering status
            const gatheringResult = await Browser.runtime.sendMessage({
              type: MessageTypes.CHECK_CONVERSATION_GATHERING_STATUS
            })
            
            if (gatheringResult?.success && gatheringResult.data) {
              conversationGatheringInfo = gatheringResult.data
            }
          } catch (error) {
            // Silently ignore to reduce console spam
          }
        }

        setStatus({
          apiConnected: apiStatus?.isAuthenticated || false,
          organizationName: apiStatus?.organizationName,
          pendingFollowUps: followUpsResult?.success ? (followUpsResult.data?.length || 0) : 0,
          lastFollowUpCheck: apiStatus?.lastVerified,
          extensionVersion: manifest.version,
          botRunning: botStatus?.power || false,
          currentStatus: botStatus?.status,
          scrapingEnabled: scrapingInfo.scrapingEnabled,
          nextScrapingAllowed: scrapingInfo.nextScrapingAllowed,
          conversationGathering: conversationGatheringInfo
        })
      } catch (error) {
        // Silently ignore errors to reduce console spam
      }
    }

    updateStatus()
    const interval = setInterval(updateStatus, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [])

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-sm font-medium">Extension Status</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* API Connection */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">API Connection</span>
          {status.apiConnected ? (
            <Badge variant="default" className="bg-green-500 text-xs">
              Connected
            </Badge>
          ) : (
            <Badge variant="destructive" className="text-xs">
              Disconnected
            </Badge>
          )}
        </div>

        {/* Organization */}
        {status.organizationName && (
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Organization</span>
            <span className="text-xs font-medium">{status.organizationName}</span>
          </div>
        )}

        {/* Bot Status */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Bot Status</span>
          {status.botRunning ? (
            <Badge variant="default" className="bg-blue-500 text-xs">
              Running
            </Badge>
          ) : (
            <Badge variant="outline" className="text-xs">
              Stopped
            </Badge>
          )}
        </div>

        {/* Pending Follow-ups */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Pending Follow-ups</span>
          {status.pendingFollowUps > 0 ? (
            <Badge variant="default" className="bg-orange-500 text-xs">
              {status.pendingFollowUps}
            </Badge>
          ) : (
            <Badge variant="outline" className="text-xs">
              0
            </Badge>
          )}
        </div>

        {/* Conversation Gathering Status */}
        {status.conversationGathering && (
          <div className="space-y-2 pt-2 border-t">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Conversations</span>
              {status.conversationGathering.isComplete ? (
                <Badge variant="default" className="bg-green-500 text-xs">
                  Ready
                </Badge>
              ) : status.conversationGathering.isGathering ? (
                <Badge variant="default" className="bg-blue-500 text-xs">
                  Processing
                </Badge>
              ) : (
                <Badge variant="outline" className="text-xs">
                  Waiting
                </Badge>
              )}
            </div>
            
            {status.conversationGathering.progress && (
              <div className="space-y-1">
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Progress</span>
                  <span>
                    {status.conversationGathering.progress.processedContacts}/
                    {status.conversationGathering.progress.totalContacts} 
                    ({status.conversationGathering.progress.percentComplete}%)
                  </span>
                </div>
                <Progress 
                  value={status.conversationGathering.progress.percentComplete} 
                  className="h-1"
                />
              </div>
            )}
            
            {status.conversationGathering.estimatedTimeRemaining && (
              <div className="flex items-center justify-between">
                <span className="text-xs text-muted-foreground">Est. Time</span>
                <span className="text-xs">{status.conversationGathering.estimatedTimeRemaining}</span>
              </div>
            )}
          </div>
        )}

        {/* Extension Version */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Version</span>
          <span className="text-xs font-mono">{status.extensionVersion}</span>
        </div>

        {/* Current Status */}
        {status.currentStatus && (
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Current Status</span>
            <span className="text-xs font-medium max-w-[120px] truncate" title={status.currentStatus}>
              {status.currentStatus}
            </span>
          </div>
        )}

        {/* Last Check */}
        {status.lastFollowUpCheck && (
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Last Check</span>
            <span className="text-xs">
              {new Date(status.lastFollowUpCheck).toLocaleTimeString()}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

/* eslint-enable */
