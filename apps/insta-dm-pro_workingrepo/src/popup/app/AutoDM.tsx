
/* eslint-disable */

import { useEffect, useState } from "react"
import { Checkbox } from "../components/ui/checkbox"
import { Label } from "../components/ui/label"
import { Button } from "../components/ui/button"
import Browser from "webextension-polyfill"
import { MessageTypes } from "../../shared/messaging"
import { RangeSlider } from "../components/ui/range_slider"
import { guard } from "../../shared/common-utils"

export default function AutoDM() {
  const [waitTime, setWaitTime] = useState([1, 2])
  const [dmCount, setDmCount] = useState([10, 20])
  const [dmBreakTime, setDMBreakTime] = useState([5,10]); // in mins
  const [followerAge, setFollowerAge] = useState([1, 1])
  const [onlyZeroMessages, setOnlyZeroMessages] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [dmPastFollowers, setDMPastFollowers] = useState(false);

  // Past follower specific settings (no wait time - immediate DM)
  const [pastFollowerTimeBetweenDM, setPastFollowerTimeBetweenDM] = useState([2, 3])
  const [pastFollowerDMCount, setPastFollowerDMCount] = useState([5, 10])
  const [pastFollowerBreakTime, setPastFollowerBreakTime] = useState([10, 15])

  useEffect(() => {
    Browser.runtime.sendMessage({
      type: MessageTypes.GET_SETTINGS
    }).then((settings) => {
      setWaitTime(guard(settings.waitTime, [1, 1]))
      setDmCount(guard(settings.dmCount, [10, 20]))
      setFollowerAge(guard(settings.followerAge, [0, 1]))
      setDMBreakTime(guard(settings.dmBreakTime, [5,10]));
      setOnlyZeroMessages(settings.onlyZeroMessages || false)
      setDMPastFollowers(settings.dmPastFollowers || false);

      // Load past follower specific settings
      setPastFollowerTimeBetweenDM(guard(settings.pastFollowerTimeBetweenDM, [2,3]));
      setPastFollowerDMCount(guard(settings.pastFollowerDMCount, [5,10]));
      setPastFollowerBreakTime(guard(settings.pastFollowerBreakTime, [10,15]));

      setIsLoading(false)
    })
  }, [])

  const validateSettings = () => {
    const errors = []

    if (waitTime[0] >= waitTime[1]) {
      errors.push("Wait time: minimum must be less than maximum")
    }
    if (dmCount[0] >= dmCount[1]) {
      errors.push("DM count: minimum must be less than maximum")
    }
    if (dmBreakTime[0] >= dmBreakTime[1]) {
      errors.push("Break time: minimum must be less than maximum")
    }
    if (followerAge[0] >= followerAge[1]) {
      errors.push("Past followers: minimum must be less than maximum")
    }

    // Validate past follower specific settings
    if (pastFollowerTimeBetweenDM[0] >= pastFollowerTimeBetweenDM[1]) {
      errors.push("Past follower time between DMs: minimum must be less than maximum")
    }
    if (pastFollowerDMCount[0] >= pastFollowerDMCount[1]) {
      errors.push("Past follower DM count: minimum must be less than maximum")
    }
    if (pastFollowerBreakTime[0] >= pastFollowerBreakTime[1]) {
      errors.push("Past follower break time: minimum must be less than maximum")
    }

    return errors
  }

  const handleSave = () => {
    const validationErrors = validateSettings()

    if (validationErrors.length > 0) {
      alert("Please fix the following errors:\n" + validationErrors.join("\n"))
      return
    }

    setIsSaving(true)
    console.log("Saving Auto DM settings:", { waitTime, dmCount, followerAge, onlyZeroMessages, dmPastFollowers })
    Browser.runtime.sendMessage({
      type: MessageTypes.SAVE_SETTINGS,
      data: {
        waitTime,
        dmCount,
        followerAge,
        onlyZeroMessages,
        dmBreakTime,
        dmPastFollowers,
        pastFollowerTimeBetweenDM,
        pastFollowerDMCount,
        pastFollowerBreakTime
      }
    }).then(() => {
      setTimeout(() => {
        setIsSaving(false)
      }, 1000);
    })
  }

  return (
    <div className="space-y-8">
      <h1 className="text-3xl font-bold">Auto DM Settings</h1>

    {isLoading ? <div>Loading...</div> : <>
    <div className="space-y-4">
    <Label>How long wait before DM? ({waitTime.join('-')} minutes)</Label>
    <RangeSlider value={waitTime} onValueChange={(value: number[]) => setWaitTime(value)} min={1} max={99} step={1} />
    </div>

    <div className="space-y-4">
    <Label>How many DM's before break? ({dmCount.join('-')})</Label>
    <RangeSlider value={dmCount} onValueChange={(value: number[]) => setDmCount(value)} min={1} max={500} step={1} />
    </div>

    <div className="space-y-4">
    <Label>DM Break Time? ({dmBreakTime.join('-')})</Label>
    <RangeSlider value={dmBreakTime} onValueChange={(value: number[]) => setDMBreakTime(value)} min={1} max={99} step={1} />
    </div>

    {/* <div className="space-y-4">
    <Label>Past how many Days DM followers? ({followerAge.join('-')} days)</Label>
    <RangeSlider value={followerAge} onValueChange={(value: number[]) => setFollowerAge(value)} min={0} max={365} step={1} />
    </div> */}
    <div className="space-y-4">
    <Label>How many of past followers? ({followerAge.join('-')})</Label>
    <RangeSlider value={followerAge} onValueChange={(value: number[]) => setFollowerAge(value)} min={0} max={1000} step={1} />
    </div>

    <div className="flex items-center space-x-2">
    <Checkbox
        id="zeroMessages"
        checked={onlyZeroMessages}
        onCheckedChange={(checked) => setOnlyZeroMessages(checked as boolean)}
    />
    <Label htmlFor="zeroMessages">Only DM people that have 0 messages history?</Label>
    </div>

    <div className="flex items-center space-x-2">
    <Checkbox
        id="dmPastFollowers"
        checked={dmPastFollowers}
        onCheckedChange={(checked) => setDMPastFollowers(checked as boolean)}
    />
    <Label htmlFor="dmPastFollowers">DM Past Followers?</Label>
    </div>

    {dmPastFollowers && (
      <div className="space-y-6 p-4 border rounded-lg bg-gray-50">
        <h3 className="text-lg font-semibold text-blue-600">Past Followers Settings</h3>
        <p className="text-sm text-gray-600">Past followers are DMed immediately (no wait time)</p>

        <div className="space-y-4">
          <Label>How many DM's before break for past followers? ({pastFollowerDMCount.join('-')})</Label>
          <RangeSlider value={pastFollowerDMCount} onValueChange={(value: number[]) => setPastFollowerDMCount(value)} min={1} max={500} step={1} />
        </div>

        <div className="space-y-4">
          <Label>DM Break Time for past followers? ({pastFollowerBreakTime.join('-')} minutes)</Label>
          <RangeSlider value={pastFollowerBreakTime} onValueChange={(value: number[]) => setPastFollowerBreakTime(value)} min={1} max={99} step={1} />
        </div>

        <div className="space-y-4">
          <Label>Time between DMs for past followers? ({pastFollowerTimeBetweenDM.join('-')} minutes)</Label>
          <RangeSlider value={pastFollowerTimeBetweenDM} onValueChange={(value: number[]) => setPastFollowerTimeBetweenDM(value)} min={1} max={99} step={1} />
        </div>
      </div>
    )}
    </>}

      <Button onClick={handleSave} disabled={isSaving || isLoading}>{isSaving ? "Saving..." : "Save Settings"}</Button>
    </div>
  )
}


/* eslint-disable */