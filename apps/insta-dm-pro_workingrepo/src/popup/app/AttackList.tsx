import { useState, useEffect } from 'react';
import Browser from 'webextension-polyfill';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { MessageTypes } from "../../shared/messaging";
import { ApiService, AttackListResult } from '../../shared/api-service';

export default function AttackList() {
  const [attackListData, setAttackListData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastRefresh, setLastRefresh] = useState(null);

  useEffect(() => {
    loadAttackList();
    // Auto-refresh every 30 seconds
    const interval = setInterval(loadAttackList, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadAttackList = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Get API key from background script
      const apiKey = await Browser.runtime.sendMessage({
        type: MessageTypes.GET_API_KEY
      });

      if (!apiKey) {
        setError('No API key configured. Please set up your API key first.');
        return;
      }

      // Fetch attack list data
      const result = await ApiService.getAttackList(apiKey);
      
      if (result.success) {
        setAttackListData(result);
        setLastRefresh(new Date());
      } else {
        setError(result.message || 'Failed to load attack list');
      }
    } catch (err) {
      console.error('Error loading attack list:', err);
      setError('Failed to connect to API. Make sure the dashboard is running.');
    } finally {
      setIsLoading(false);
    }
  };





  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">🎯 Attack List</h2>
        <Button 
          onClick={loadAttackList} 
          disabled={isLoading}
          variant="outline"
          size="sm"
        >
          {isLoading ? "Loading..." : "Refresh"}
        </Button>
      </div>

      {/* Summary Card */}
      {attackListData && (
        <Card>
          <CardHeader>
            <CardTitle>Summary</CardTitle>
            <CardDescription>
              Current attack list status
              {lastRefresh && (
                <span className="block text-xs mt-1">
                  Last updated: {lastRefresh.toLocaleTimeString()}
                </span>
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Contacts Ready:</span>
                <span className="ml-2 text-lg font-bold text-green-600">
                  {attackListData.total || 0}
                </span>
              </div>
              <div>
                <span className="font-medium">Total in Database:</span>
                <span className="ml-2 text-lg font-bold">
                  {attackListData.metadata?.totalInDatabase || 0}
                </span>
              </div>
              <div>
                <span className="font-medium">Ready for Messaging:</span>
                <span className="ml-2 text-lg font-bold">
                  {attackListData.metadata?.readyForMessaging || 0}
                </span>
              </div>
              <div>
                <span className="font-medium">Priority Breakdown:</span>
                <div className="text-xs mt-1">
                  P5: {attackListData.metadata?.priorityBreakdown?.priority5 || 0} | 
                  P4: {attackListData.metadata?.priorityBreakdown?.priority4 || 0} | 
                  P3: {attackListData.metadata?.priorityBreakdown?.priority3 || 0} | 
                  P2: {attackListData.metadata?.priorityBreakdown?.priority2 || 0} | 
                  P1: {attackListData.metadata?.priorityBreakdown?.priority1 || 0}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error State */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="text-red-800">
              <strong>Error:</strong> {error}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Loading State */}
      {isLoading && !attackListData && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-muted-foreground">
              Loading attack list...
            </div>
          </CardContent>
        </Card>
      )}

      {/* Contacts List */}
      {attackListData && attackListData.data && attackListData.data.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Contacts Ready ({attackListData.total || 0})</h3>
          {attackListData.data.map((contact, index) => (
            <Card key={contact.id} className="border-l-4 border-l-green-500">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base">
                    @{contact.username}
                  </CardTitle>
                  <div className="flex gap-2">
                    <Badge variant="outline" className="text-xs">
                      P{contact.priority}
                    </Badge>
                    <Badge className={contact.stage === 'new' ? 'bg-blue-500' : 'bg-green-500'}>
                      {contact.stage}
                    </Badge>
                  </div>
                </div>
                <CardDescription className="text-xs">
                  Contact ID: {contact.id} | Status: {contact.attackListStatus || 'pending'}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {/* Message Status */}
                {contact.messageStatus && (
                  <div>
                    <span className="text-sm font-medium">Message Progress:</span>
                    <div className="mt-1 flex items-center gap-2">
                      <Badge variant="outline">
                        {contact.messageStatus.sent}/{contact.messageStatus.total}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        Sequence: {contact.messageStatus.currentSequence}
                      </span>
                    </div>
                  </div>
                )}

                {/* Suggested Message */}
                {contact.suggestedMessage && (
                  <div>
                    <span className="text-sm font-medium">Suggested Message:</span>
                    <div className="mt-1 p-2 bg-gray-50 rounded text-sm">
                      {contact.suggestedMessage.text}
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      Batch: {contact.suggestedMessage.batchName}
                    </div>
                  </div>
                )}

                {/* Planned Messages */}
                {contact.plannedMessages && contact.plannedMessages.messages.length > 0 && (
                  <div>
                    <span className="text-sm font-medium">
                      {contact.plannedMessages.type === 'ai_followups' ? 'AI Follow-ups' : 'Batch Messages'} ({contact.plannedMessages.messages.length}):
                    </span>
                    <div className="mt-2 space-y-2">
                      {contact.plannedMessages.messages.map((msg, mIndex) => (
                        <div key={mIndex} className="flex items-start gap-2">
                          <Badge variant="outline" className="text-xs">
                            {msg.sequenceNumber}
                          </Badge>
                          <div className="flex-1 p-2 bg-blue-50 rounded text-sm">
                            {msg.message}
                          </div>
                          {msg.delayMinutes && (
                            <Badge variant="outline" className="text-xs">
                              +{msg.delayMinutes}m
                            </Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Timing Info */}
                <div className="text-xs text-muted-foreground">
                  {contact.nextMessageAt && (
                    <div>Next message at: {new Date(contact.nextMessageAt).toLocaleString()}</div>
                  )}
                  {contact.lastInteractionAt && (
                    <div>Last interaction: {new Date(contact.lastInteractionAt).toLocaleString()}</div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Empty State */}
      {attackListData && attackListData.data && attackListData.data.length === 0 && (
        <Card>
          <CardContent className="pt-6 text-center">
            <div className="text-muted-foreground">
              <div className="text-4xl mb-2">📭</div>
              <div className="font-medium">No contacts ready for messaging</div>
              <div className="text-sm mt-1">
                All contacts are either already processed or not yet ready for messaging.
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}