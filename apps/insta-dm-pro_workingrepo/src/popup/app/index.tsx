import { useState, useEffect } from 'react';
import Browser from 'webextension-polyfill';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { MessageTypes } from "../../shared/messaging";
import AttackList from './AttackList';
import Settings from './Settings';

interface ApiStatus {
  isAuthenticated: boolean;
  organizationName?: string;
  organizationSlug?: string;
  lastVerified?: string;
  error?: string;
}

interface BotStatus {
  power: boolean;
  status: string;
  currentDMCount?: number;
  dmBreakLimit?: number;
  timeUntilNextDM?: number;
  remainingFollowers?: number;
  totalFollowers?: number;
}

export default function MainApp() {
  const [apiKey, setApiKey] = useState("");
  const [showApiKey, setShowApiKey] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [apiStatus, setApiStatus] = useState<ApiStatus>({ isAuthenticated: false });
  const [botStatus, setBotStatus] = useState<BotStatus>({ power: false, status: 'Stopped' });
  const [currentActivity, setCurrentActivity] = useState('');
  const [isResetting, setIsResetting] = useState(false);

  useEffect(() => {
    loadApiSettings();
    updateBotStatus(); // Initial update
    const interval = setInterval(updateBotStatus, 5000); // Changed from 1000ms to 5000ms (5 seconds)
    
    // Listen for immediate status changes from background script
    const handleMessage = (message: any) => {
      if (message.type === 'STATUS_CHANGED') {
        setBotStatus(prev => ({
          ...prev,
          power: message.data.power,
          status: message.data.status
        }));
        setCurrentActivity(message.data.status);
      }
    };
    
    Browser.runtime.onMessage.addListener(handleMessage);
    
    return () => {
      clearInterval(interval);
      Browser.runtime.onMessage.removeListener(handleMessage);
    };
  }, []);

  const loadApiSettings = async () => {
    try {
      setIsLoading(true);
      
      // Get API key from background script
      const storedApiKey = await Browser.runtime.sendMessage({
        type: MessageTypes.GET_API_KEY
      });
      
      if (storedApiKey) {
        setApiKey(storedApiKey);
        // Get API status
        const status = await Browser.runtime.sendMessage({
          type: MessageTypes.GET_API_STATUS
        });
        setApiStatus(status || { isAuthenticated: false });
      }
    } catch (error) {
      console.error('Error loading API settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateBotStatus = async () => {
    try {
      // Only update if popup is visible
      if (document.visibilityState !== 'visible') return;
      
      const status = await Browser.runtime.sendMessage({ type: MessageTypes.GET_STATUS });
      setBotStatus(status);
      
      // Format current activity
      if (status.power) {
        if (status.timeUntilNextDM && status.timeUntilNextDM > 0) {
          setCurrentActivity(`Waiting ${formatTime(status.timeUntilNextDM)} before next DM`);
        } else if (status.status) {
          setCurrentActivity(status.status);
        } else {
          setCurrentActivity('Processing...');
        }
      } else {
        setCurrentActivity('Bot is stopped');
      }
    } catch (error) {
      console.error('Error updating bot status:', error);
    }
  };

  const formatTime = (seconds: number) => {
    if (seconds <= 0) return "0s";
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return mins > 0 ? `${mins}m ${secs}s` : `${secs}s`;
  };

  const handleSaveApiKey = async () => {
    if (!apiKey.trim()) {
      alert("Please enter an API key");
      return;
    }

    try {
      setIsSaving(true);
      
      // Save API key via background script
      await Browser.runtime.sendMessage({
        type: MessageTypes.SAVE_API_KEY,
        data: { apiKey: apiKey.trim() }
      });
      
      // Verify the API key
      await handleVerifyApiKey();
    } catch (error) {
      console.error('Error saving API key:', error);
      alert('Failed to save API key');
    } finally {
      setIsSaving(false);
    }
  };

  const handleVerifyApiKey = async () => {
    if (!apiKey.trim()) {
      alert("Please enter an API key");
      return;
    }

    try {
      setIsVerifying(true);
      
      const result = await Browser.runtime.sendMessage({
        type: MessageTypes.VERIFY_API_KEY,
        data: { apiKey: apiKey.trim() }
      });
      
      setApiStatus(result);
      
      if (result.isAuthenticated) {
        alert(`Successfully authenticated with organization: ${result.organizationName}`);
      } else {
        alert(`Authentication failed: ${result.error || 'Invalid API key'}`);
      }
    } catch (error) {
      console.error('Error verifying API key:', error);
      alert('Failed to verify API key');
    } finally {
      setIsVerifying(false);
    }
  };

  const toggleBot = async () => {
    try {
      // Optimistic update for immediate feedback
      setBotStatus(prev => ({ ...prev, power: !prev.power }));
      setCurrentActivity(botStatus.power ? 'Stopping...' : 'Starting...');
      
      await Browser.runtime.sendMessage(
        botStatus.power ? { type: MessageTypes.STOP_PROCESS } : { type: MessageTypes.START_PROCESS }
      );
      
      // Force immediate status update as backup
      setTimeout(updateBotStatus, 100);
    } catch (error) {
      console.error('Error toggling bot:', error);
      // Revert optimistic update on error
      updateBotStatus();
      alert('Failed to toggle bot');
    }
  };

  const handleResetScrapingProgress = async () => {
    try {
      setIsResetting(true);
      
      const result = await Browser.runtime.sendMessage({
        type: MessageTypes.RESET_SCRAPING_PROGRESS
      });
      
      if (result.success) {
        alert('Scraping progress reset successfully! Status set back to FRESH_START.');
      } else {
        alert(`Failed to reset scraping progress: ${result.message}`);
      }
    } catch (error) {
      console.error('Error resetting scraping progress:', error);
      alert('Failed to reset scraping progress');
    } finally {
      setIsResetting(false);
    }
  };

  if (isLoading) {
    return <div className="flex items-center justify-center h-screen">Loading...</div>;
  }

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <h1 className="text-3xl font-bold text-center">AISetter</h1>
      
      {/* API Key Card */}
      <Card>
        <CardHeader>
          <CardTitle>API Configuration</CardTitle>
          <CardDescription>
            Connect to your AISetter dashboard to enable automation features.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* API Status */}
          <div className="flex items-center gap-2">
            <Label>Status:</Label>
            {apiStatus.isAuthenticated ? (
              <Badge variant="default" className="bg-green-500">
                Connected to {apiStatus.organizationName}
              </Badge>
            ) : (
              <Badge variant="destructive">
                Not Connected
              </Badge>
            )}
          </div>
          
          {/* API Key Input */}
          <div className="space-y-2">
            <Label htmlFor="apiKey">API Key</Label>
            <div className="flex gap-2">
              <Input
                id="apiKey"
                type={showApiKey ? "text" : "password"}
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="Enter your API key from AISetter dashboard"
                className="font-mono"
              />
              <Button
                variant="outline"
                onClick={() => setShowApiKey(!showApiKey)}
                type="button"
              >
                {showApiKey ? "Hide" : "Show"}
              </Button>
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              onClick={handleSaveApiKey}
              disabled={isSaving || isVerifying || !apiKey.trim()}
            >
              {isSaving ? "Saving..." : "Save & Verify"}
            </Button>
            {apiStatus.isAuthenticated && (
              <Button
                variant="outline"
                onClick={handleResetScrapingProgress}
                disabled={isResetting}
              >
                {isResetting ? "Resetting..." : "Reset Scraping"}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
      
      {/* Bot Control Card - Only show when API is authenticated */}
      {apiStatus.isAuthenticated && (
        <Card>
          <CardHeader>
            <CardTitle>Bot Control</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Start/Stop Button */}
            <div className="flex justify-center">
              <Button 
                onClick={toggleBot} 
                variant={botStatus.power ? "destructive" : "default"} 
                size="lg"
                className="w-48"
              >
                {botStatus.power ? "STOP BOT" : "START BOT"}
              </Button>
            </div>
            
            {/* Current Status */}
            <div className="text-center space-y-2">
              <div className="flex items-center justify-center gap-2">
                <span className="text-sm text-muted-foreground">Status:</span>
                <Badge variant={botStatus.power ? "default" : "outline"} className={botStatus.power ? "bg-green-500" : ""}>
                  {botStatus.power ? "Running" : "Stopped"}
                </Badge>
              </div>
              
              {/* Current Activity */}
              <div className="text-sm text-muted-foreground">
                {currentActivity}
              </div>
              
              {/* Progress Info */}
              {botStatus.power && botStatus.currentDMCount !== undefined && (
                <div className="mt-4 space-y-1 text-sm">
                  <div>DMs sent: {botStatus.currentDMCount} / {botStatus.dmBreakLimit}</div>
                  <div>Followers remaining: {botStatus.remainingFollowers} / {botStatus.totalFollowers}</div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
