'use server';

import { revalidatePath } from 'next/cache';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { prisma } from '@workspace/database/client';

export async function clearAllInstagramContacts() {
  try {
    const ctx = await getAuthOrganizationContext();

    // First delete all related data
    // Get all contact IDs for this organization
    const contactIds = await prisma.instagramContact.findMany({
      where: {
        organizationId: ctx.organization.id
      },
      select: {
        id: true
      }
    });

    const contactIdList = contactIds.map(c => c.id);

    if (contactIdList.length > 0) {
      // Delete messages
      await prisma.instagramMessage.deleteMany({
        where: {
          contactId: {
            in: contactIdList
          }
        }
      });

      // Delete follow-ups (MessageList)
      await prisma.messageList.deleteMany({
        where: {
          contactId: {
            in: contactIdList
          }
        }
      });
    }

    // Finally delete contacts
    const deleteResult = await prisma.instagramContact.deleteMany({
      where: {
        organizationId: ctx.organization.id
      }
    });

    // Revalidate pages
    revalidatePath('/organizations/[slug]/instagram/contacts', 'page');
    revalidatePath('/organizations/[slug]/home', 'page');
    revalidatePath('/organizations/[slug]/chrome-extension/attack-list', 'page');

    return { 
      success: true, 
      message: `Successfully cleared ${deleteResult.count} contacts and all related data`,
      deletedCount: deleteResult.count
    };
  } catch (error) {
    console.error('Error clearing Instagram contacts:', error);
    return { success: false, error: 'Failed to clear contacts' };
  }
}