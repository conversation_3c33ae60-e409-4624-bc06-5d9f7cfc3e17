import 'server-only';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { prisma } from '@workspace/database/client';
import { InstagramContactDto } from '~/types/dtos/instagram-contact-dto';
import { SortDirection } from '~/types/sort-direction';

export async function getInstagramContacts(): Promise<InstagramContactDto[]> {
  const ctx = await getAuthOrganizationContext();

  const contacts = await prisma.instagramContact.findMany({
        where: {
          organizationId: ctx.organization.id
        },
        select: {
          id: true,
          instagramId: true,
          instagramNickname: true,
          avatar: true,
          messageCount: true,
          stage: true,
          isIgnored: true,
          isConversionLinkSent: true,
          isTakeControl: true,
          createdAt: true,
          updatedAt: true
        },
        orderBy: {
          updatedAt: SortDirection.Desc
        }
      });

      return contacts.map(contact => ({
        id: contact.id,
        instagramId: contact.instagramId,
        instagramUsername: contact.instagramNickname || 'Unknown User',
        avatar: contact.avatar,
        messageCount: contact.messageCount,
        stage: contact.stage,
        isIgnored: contact.isIgnored,
        isLinkSent: contact.isConversionLinkSent,
        isTakeControl: contact.isTakeControl,
        messages: [], // Simplified - messages relation removed
        followUps: [], // Backward compatibility - empty for now
        createdAt: contact.createdAt,
        updatedAt: contact.updatedAt
      }));

}
