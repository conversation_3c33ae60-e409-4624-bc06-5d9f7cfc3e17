import 'server-only';

import { unstable_noStore as noStore } from 'next/cache';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { prisma } from '@workspace/database/client';

export interface InstagramFollowUpDto {
  id: string;
  contactId: string;
  contactUsername: string;
  contactAvatar: string | null;
  sequenceNumber: number;
  message: string;
  scheduledTime: Date;
  status: 'pending' | 'sent' | 'failed' | 'external';
  sentAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
  type: 'new';
  isWithin24HourWindow?: boolean;
  latest24HourTime?: string | null;
}

export interface LegacyInstagramFollowUpDto {
  id: string;
  contactId: string;
  contactUsername: string;
  contactAvatar: string | null;
  followUpNumber: number;
  message: string;
  scheduledTime: Date | null;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  type: 'legacy';
  isWithin24HourWindow?: boolean;
  latest24HourTime?: string | null;
}

export const getInstagramFollowUps = async (): Promise<{
  newFollowUps: InstagramFollowUpDto[];
  legacyFollowUps: LegacyInstagramFollowUpDto[];
}> => {
  noStore();

  const ctx = await getAuthOrganizationContext();

  // Get follow-ups from MessageList table
  const newFollowUps = await prisma.messageList.findMany({
    where: {
      status: { in: ['PENDING', 'SENT', 'FAILED'] },
      organizationId: ctx.organization.id
    },
    include: {
      InstagramContact: {
        select: {
          instagramNickname: true,
          avatar: true
        }
      }
    },
    orderBy: [
      { scheduledTime: 'asc' },
      { sequenceNumber: 'asc' }
    ]
  });

  // Legacy follow-up fields were migrated to MessageList table
  const legacyContacts: any[] = [];

  // Transform MessageList follow-ups
  const transformedNewFollowUps: InstagramFollowUpDto[] = newFollowUps.map(followUp => ({
    id: followUp.id,
    contactId: followUp.contactId,
    contactUsername: followUp.InstagramContact.instagramNickname,
    contactAvatar: followUp.InstagramContact.avatar,
    sequenceNumber: followUp.sequenceNumber || 1,
    message: followUp.messageContent,
    scheduledTime: followUp.scheduledTime,
    status: followUp.status.toLowerCase() as 'pending' | 'sent' | 'failed',
    sentAt: followUp.sentAt,
    createdAt: followUp.createdAt,
    updatedAt: followUp.updatedAt,
    type: 'new' as const
  }));

  // Transform legacy follow-ups
  const transformedLegacyFollowUps: LegacyInstagramFollowUpDto[] = [];

  // Legacy follow-up fields were migrated to MessageList table
  // No legacy processing needed

  // Sort legacy follow-ups by scheduled time
  transformedLegacyFollowUps.sort((a, b) => {
    if (!a.scheduledTime && !b.scheduledTime) return 0;
    if (!a.scheduledTime) return 1;
    if (!b.scheduledTime) return -1;
    return a.scheduledTime.getTime() - b.scheduledTime.getTime();
  });

  return {
    newFollowUps: transformedNewFollowUps,
    legacyFollowUps: transformedLegacyFollowUps
  };
};
