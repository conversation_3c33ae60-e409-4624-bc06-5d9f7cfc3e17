import 'server-only';

import { unstable_cache as cache } from 'next/cache';
import { addDays, subDays, startOfDay, endOfDay } from 'date-fns';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { prisma } from '@workspace/database/client';
import { InstagramContactStage } from '@workspace/database';

import {
  Caching,
  OrganizationCacheKey
} from '~/data/caching';
import {
  InstagramStatisticsDto,
  InstagramStatisticsFiltersDto,
  InstagramStatisticsTrendDto,
  InstagramStatisticsSummaryDto
} from '~/types/dtos/instagram-statistics-dto';
import { getInstagramFollowerCount, getInstagramFollowerCountForOrganization } from './get-instagram-business-info';

const defaultRevalidateTimeInSeconds = 3600; // 1 hour

export async function getInstagramStatistics(
  filters: InstagramStatisticsFiltersDto = { dateRange: 'last_30_days' }
): Promise<InstagramStatisticsDto> {
  const ctx = await getAuthOrganizationContext();

  return cache(
    async () => {
      const { startDate, endDate } = getDateRange(filters);

      // Base query conditions
      const baseWhere = {
        organizationId: ctx.organization.id,
        createdAt: {
          gte: startDate,
          lte: endDate
        },
        ...(filters.includeIgnored === false && { isIgnored: false }),
        ...(filters.includeTakeControl === false && { isTakeControl: false })
      };

      // Get all contacts for the period
      const contacts = await prisma.instagramContact.findMany({
        where: baseWhere
      });

      // Get total followers from Instagram Business API
      const followers = await getInstagramFollowerCountForOrganization(ctx.organization.id);

      // Calculate core metrics
      const peopleContacted = contacts.length;
      const peopleFormSent = contacts.filter(c => c.stage === 'formsent' || c.stage === 'converted').length;
      const peopleConverted = contacts.filter(c => c.stage === 'converted').length;
      const peopleEngaged = contacts.filter(c => c.stage === 'engaged' || c.stage === 'qualified' || c.stage === 'formsent' || c.stage === 'converted').length;

      // People with zero response - simplified since message relations removed
      const peopleWithZeroResponse = 0; // TODO: Calculate from MessageList if needed

      // Follow-up metrics - get from MessageList table
      const followUpMessages = await prisma.messageList.findMany({
        where: {
          organizationId: ctx.organization.id,
          contactId: { in: contacts.map(c => c.id) }
        }
      });
      
      const contactsWithFollowUps = [...new Set(followUpMessages.map(f => f.contactId))];
      const peopleFollowedUp = contactsWithFollowUps.length;
      const approxFollowUpAmount = followUpMessages.length;

      // Calculate percentages
      const percentageFormSent = peopleContacted > 0 ? (peopleFormSent / peopleContacted) * 100 : 0;
      const percentageConverted = peopleContacted > 0 ? (peopleConverted / peopleContacted) * 100 : 0;
      const percentageEngaged = peopleContacted > 0 ? (peopleEngaged / peopleContacted) * 100 : 0;

      // Response rate
      const peopleWhoResponded = peopleContacted - peopleWithZeroResponse;
      const responseRate = peopleContacted > 0 ? (peopleWhoResponded / peopleContacted) * 100 : 0;

      // Average messages per contact - simplified
      const averageMessagesPerContact = 0; // TODO: Calculate from MessageList if needed

      // Stage distribution
      const stageDistribution: Record<string, number> = {};
      Object.values(InstagramContactStage).forEach(stage => {
        stageDistribution[stage] = contacts.filter(c => c.stage === stage).length;
      });

      // Follow-up success rate (conversions after follow-ups)
      const conversionsAfterFollowUps = contacts.filter(c =>
        contactsWithFollowUps.includes(c.id) && c.stage === 'converted'
      ).length;
      const followUpSuccessRate = peopleFollowedUp > 0 ? (conversionsAfterFollowUps / peopleFollowedUp) * 100 : 0;

      // Average time to conversion - simplified
      const averageTimeToConversion = 0; // TODO: Calculate from stage change logs if needed

      // Bot performance - simplified
      const botPerformance = {
        messagesSent: 0,
        messagesReceived: 0,
        ratio: 0
      };

      // Quality metrics
      const verifiedUsers = contacts.filter(c => c.isVerifiedUser).length;
      const followerCounts = contacts.map(c => c.followerCount).filter(count => count !== null) as number[];
      const averageFollowerCount = followerCounts.length > 0
        ? followerCounts.reduce((sum, count) => sum + count, 0) / followerCounts.length
        : 0;
      const businessFollowsUser = contacts.filter(c => c.isBusinessFollowUser).length;
      const userFollowsBusiness = contacts.filter(c => c.isUserFollowBusiness).length;

      const qualityMetrics = {
        verifiedUsers,
        averageFollowerCount,
        businessFollowsUser,
        userFollowsBusiness
      };

      // Disqualification reasons - simplified
      const disqualificationReasons: Record<string, number> = {};

      // Window utilization (placeholder - needs 24-hour window logic)
      const windowUtilization = {
        within24Hours: 0, // TODO: Implement 24-hour window calculation
        outside24Hours: 0
      };

      // Media usage - simplified
      const mediaUsage = {
        textMessages: 0,
        audioMessages: 0,
        imageMessages: 0
      };

      // Conversation length distribution - simplified
      const conversationLengthDistribution: Record<string, number> = {
        '1-2': 0,
        '3-5': 0,
        '6-10': 0,
        '11-20': 0,
        '20+': 0
      };

      // Chrome extension metrics (placeholder)
      const chromeExtension = {
        followUpsSent: 0,
        followUpsPending: 0,
        followUpsFailed: 0,
        averageResponseTime: 0,
        successRate: 0,
        lastSyncTime: null
      };

      // Generate trends
      const trends = await generateTrends(ctx.organization.id, startDate, endDate);

      return {
        followers,
        peopleContacted,
        peopleFormSent,
        peopleConverted,
        peopleWithZeroResponse,
        percentageFormSent,
        percentageConverted,
        peopleEngaged,
        percentageEngaged,
        peopleFollowedUp,
        approxFollowUpAmount,
        responseRate,
        averageMessagesPerContact,
        stageDistribution,
        followUpSuccessRate,
        averageTimeToConversion,
        botPerformance,
        qualityMetrics,
        disqualificationReasons,
        windowUtilization,
        mediaUsage,
        conversationLengthDistribution,
        chromeExtension,
        trends
      };
    },
    Caching.createOrganizationKeyParts(
      OrganizationCacheKey.InstagramStatistics,
      `${ctx.organization.id}-${JSON.stringify(filters)}`
    ),
    {
      revalidate: defaultRevalidateTimeInSeconds,
      tags: [
        Caching.createOrganizationTag(
          OrganizationCacheKey.InstagramStatistics,
          ctx.organization.id
        )
      ]
    }
  )();
}

function getDateRange(filters: InstagramStatisticsFiltersDto): { startDate: Date; endDate: Date } {
  const now = new Date();
  const endDate = endOfDay(now);

  switch (filters.dateRange) {
    case 'last_day':
      return {
        startDate: startOfDay(subDays(now, 1)),
        endDate
      };
    case 'last_7_days':
      return {
        startDate: startOfDay(subDays(now, 7)),
        endDate
      };
    case 'last_30_days':
      return {
        startDate: startOfDay(subDays(now, 30)),
        endDate
      };
    case 'custom':
      return {
        startDate: filters.startDate ? startOfDay(new Date(filters.startDate)) : startOfDay(subDays(now, 30)),
        endDate: filters.endDate ? endOfDay(new Date(filters.endDate)) : endDate
      };
    default:
      return {
        startDate: startOfDay(subDays(now, 30)),
        endDate
      };
  }
}

async function generateTrends(
  organizationId: string,
  startDate: Date,
  endDate: Date
): Promise<{ daily: InstagramStatisticsTrendDto[]; weekly: InstagramStatisticsTrendDto[]; monthly: InstagramStatisticsTrendDto[] }> {
  // This is a simplified implementation - you might want to optimize this with raw SQL
  const trends = {
    daily: [] as InstagramStatisticsTrendDto[],
    weekly: [] as InstagramStatisticsTrendDto[],
    monthly: [] as InstagramStatisticsTrendDto[]
  };

  // Generate daily trends for the last 30 days
  for (let i = 29; i >= 0; i--) {
    const date = subDays(new Date(), i);
    const dayStart = startOfDay(date);
    const dayEnd = endOfDay(date);

    const dayContacts = await prisma.instagramContact.count({
      where: {
        organizationId,
        createdAt: { gte: dayStart, lte: dayEnd }
      }
    });

    const dayEngaged = await prisma.instagramContact.count({
      where: {
        organizationId,
        createdAt: { gte: dayStart, lte: dayEnd },
        stage: { in: ['engaged', 'qualified', 'formsent', 'converted'] }
      }
    });

    const dayConverted = await prisma.instagramContact.count({
      where: {
        organizationId,
        createdAt: { gte: dayStart, lte: dayEnd },
        stage: 'converted'
      }
    });

    const dayFormSent = await prisma.instagramContact.count({
      where: {
        organizationId,
        createdAt: { gte: dayStart, lte: dayEnd },
        stage: { in: ['formsent', 'converted'] }
      }
    });

    const dayFollowUps = await prisma.messageList.count({
      where: {
        organizationId,
        createdAt: { gte: dayStart, lte: dayEnd }
      }
    });

    trends.daily.push({
      date: date.toISOString().split('T')[0],
      contacted: dayContacts,
      engaged: dayEngaged,
      converted: dayConverted,
      formSent: dayFormSent,
      followUps: dayFollowUps
    });
  }

  return trends;
}

export async function getInstagramStatisticsSummary(): Promise<InstagramStatisticsSummaryDto> {
  const ctx = await getAuthOrganizationContext();

  return cache(
    async () => {
      const now = new Date();
      const last30Days = subDays(now, 30);
      const last60Days = subDays(now, 60);

      // Current period (last 30 days)
      const currentContacts = await prisma.instagramContact.count({
        where: {
          organizationId: ctx.organization.id,
          createdAt: { gte: last30Days }
        }
      });

      const currentConverted = await prisma.instagramContact.count({
        where: {
          organizationId: ctx.organization.id,
          createdAt: { gte: last30Days },
          stage: 'converted'
        }
      });

      const currentEngaged = await prisma.instagramContact.count({
        where: {
          organizationId: ctx.organization.id,
          createdAt: { gte: last30Days },
          stage: { in: ['engaged', 'qualified', 'formsent', 'converted'] }
        }
      });

      const currentFollowUps = await prisma.messageList.count({
        where: {
          organizationId: ctx.organization.id,
          createdAt: { gte: last30Days }
        }
      });

      // Previous period (30-60 days ago)
      const previousContacts = await prisma.instagramContact.count({
        where: {
          organizationId: ctx.organization.id,
          createdAt: { gte: last60Days, lt: last30Days }
        }
      });

      // Calculate rates
      const conversionRate = currentContacts > 0 ? (currentConverted / currentContacts) * 100 : 0;
      const engagementRate = currentContacts > 0 ? (currentEngaged / currentContacts) * 100 : 0;
      const followUpRate = currentContacts > 0 ? (currentFollowUps / currentContacts) * 100 : 0;

      // Calculate trend
      let trend: 'up' | 'down' | 'stable' = 'stable';
      let trendPercentage = 0;

      if (previousContacts > 0) {
        const change = ((currentContacts - previousContacts) / previousContacts) * 100;
        trendPercentage = Math.abs(change);

        if (change > 5) trend = 'up';
        else if (change < -5) trend = 'down';
        else trend = 'stable';
      } else if (currentContacts > 0) {
        trend = 'up';
        trendPercentage = 100;
      }

      return {
        totalContacts: currentContacts,
        conversionRate,
        engagementRate,
        followUpRate,
        trend,
        trendPercentage
      };
    },
    Caching.createOrganizationKeyParts(
      OrganizationCacheKey.InstagramStatistics,
      `${ctx.organization.id}-summary`
    ),
    {
      revalidate: defaultRevalidateTimeInSeconds,
      tags: [
        Caching.createOrganizationTag(
          OrganizationCacheKey.InstagramStatistics,
          ctx.organization.id
        )
      ]
    }
  )();
}
