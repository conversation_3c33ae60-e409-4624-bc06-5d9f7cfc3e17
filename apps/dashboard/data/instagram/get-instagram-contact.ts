import 'server-only';

import { unstable_noStore as noStore } from 'next/cache';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { prisma } from '@workspace/database/client';

import { InstagramContactDto } from '~/types/dtos/instagram-contact-dto';

export async function getInstagramContact(contactId: string): Promise<InstagramContactDto | null> {
  // Disable caching to always get fresh data
  noStore();

  const ctx = await getAuthOrganizationContext();

  const contact = await prisma.instagramContact.findFirst({
    where: {
      id: contactId,
      organizationId: ctx.organization.id
    },
    select: {
      id: true,
      instagramId: true,
      instagramNickname: true,
      avatar: true,
      messageCount: true,
      stage: true,
      isIgnored: true,
      isConversionLinkSent: true,
      isTakeControl: true,
      createdAt: true,
      updatedAt: true,
      MessageList: {
        where: {
          status: { in: ['PENDING', 'SENT', 'FAILED'] }
        },
        orderBy: [
          { scheduledTime: 'asc' },
          { sequenceNumber: 'asc' }
        ]
      }
    }
  });

  if (!contact) {
    return null;
  }

  // Get messages - simplified for now since message relations were removed
  const messages: any[] = [];

  // Transform MessageList follow-ups to match expected InstagramMessageDto format
  const followUps = contact.MessageList.map(message => ({
    id: message.id,
    message: message.messageContent,
    time: message.scheduledTime,
    status: message.status.toLowerCase(),
    type: 'new' as const,
    sequenceNumber: message.sequenceNumber
  }));

  return {
    id: contact.id,
    instagramId: contact.instagramId,
    instagramUsername: contact.instagramNickname,
    avatar: contact.avatar,
    messageCount: contact.messageCount,
    stage: contact.stage,
    isIgnored: contact.isIgnored,
    isLinkSent: contact.isConversionLinkSent,
    isTakeControl: contact.isTakeControl,
    messages: messages,
    followUps: followUps, // Now fetches actual follow-ups from MessageList
    createdAt: contact.createdAt,
    updatedAt: contact.updatedAt
  };
}
