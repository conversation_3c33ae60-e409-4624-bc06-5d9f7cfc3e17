'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@workspace/ui/components/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Badge } from '@workspace/ui/components/badge';
import { Loader2, Play, RefreshCw, Users, MessageSquare, Target } from 'lucide-react';
import { useToast } from '@workspace/ui/hooks/use-toast';

interface ProcessingStats {
  pendingFollowers: number;
  contactedFollowers: number;
  totalContacts: number;
  attackListCount: number;
  needsProcessing: boolean;
}

interface ProcessingResult {
  processed: number;
  newContacts: number;
  conversationsFound: number;
  errors: string[];
}

export default function ProcessFollowersPage() {
  const { toast } = useToast();
  const [isProcessing, setIsProcessing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [stats, setStats] = useState<ProcessingStats | null>(null);
  const [lastResult, setLastResult] = useState<ProcessingResult | null>(null);

  const fetchStats = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/instagram/process-new-followers');
      if (response.ok) {
        const data = await response.json();
        setStats(data.data);
      } else {
        toast({ title: 'Error', description: 'Failed to fetch processing stats', variant: 'destructive' });
      }
    } catch (error) {
      toast({ title: 'Error', description: 'Error fetching stats', variant: 'destructive' });
    } finally {
      setIsLoading(false);
    }
  };

  const processFollowers = async () => {
    setIsProcessing(true);
    try {
      const response = await fetch('/api/instagram/process-new-followers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          batchSize: 50
        })
      });

      if (response.ok) {
        const data = await response.json();
        setLastResult(data.data);
        toast({ title: 'Success', description: data.message });
        // Refresh stats after processing
        await fetchStats();
      } else {
        const error = await response.json();
        toast({ title: 'Error', description: error.error || 'Failed to process followers', variant: 'destructive' });
      }
    } catch (error) {
      toast({ title: 'Error', description: 'Error processing followers', variant: 'destructive' });
    } finally {
      setIsProcessing(false);
    }
  };

  // Load stats on component mount
  React.useEffect(() => {
    fetchStats();
  }, []);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Process Instagram Followers</h1>
          <p className="text-muted-foreground">
            Convert new Instagram followers into contacts with conversation analysis
          </p>
        </div>
        <Button
          onClick={fetchStats}
          variant="outline"
          size="sm"
          disabled={isLoading}
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4" />
          )}
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Followers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.pendingFollowers ?? '...'}
            </div>
            <p className="text-xs text-muted-foreground">
              Waiting to be processed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Contacts</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.totalContacts ?? '...'}
            </div>
            <p className="text-xs text-muted-foreground">
              Created from followers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Attack List</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.attackListCount ?? '...'}
            </div>
            <p className="text-xs text-muted-foreground">
              Ready for messaging
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Badge variant={stats?.needsProcessing ? "destructive" : "secondary"}>
                {stats?.needsProcessing ? "Needs Processing" : "Up to Date"}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Processing status
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Processing Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Process New Followers</CardTitle>
          <CardDescription>
            This will analyze new Instagram followers, check for existing conversations,
            and create contacts with appropriate priorities and messages.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-4">
            <Button
              onClick={processFollowers}
              disabled={isProcessing || !stats?.needsProcessing}
              size="lg"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Play className="mr-2 h-4 w-4" />
                  Process Followers
                </>
              )}
            </Button>

            {!stats?.needsProcessing && (
              <p className="text-sm text-muted-foreground">
                No pending followers to process
              </p>
            )}
          </div>

          {/* Processing Workflow Info */}
          <div className="bg-muted p-4 rounded-lg">
            <h4 className="font-medium mb-2">Processing Workflow:</h4>
            <ol className="text-sm space-y-1 list-decimal list-inside text-muted-foreground">
              <li>Get pending followers from InstagramFollower table</li>
              <li>Check Instagram API for existing conversations</li>
              <li>For followers WITHOUT conversations: Create contact with priority 3 + batch messages</li>
              <li>For followers WITH conversations: Analyze with AI + create messages</li>
              <li>Add all contacts to attack list for Chrome Extension</li>
            </ol>
          </div>
        </CardContent>
      </Card>

      {/* Last Processing Result */}
      {lastResult && (
        <Card>
          <CardHeader>
            <CardTitle>Last Processing Result</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm font-medium">Processed</p>
                <p className="text-2xl font-bold text-blue-600">{lastResult.processed}</p>
              </div>
              <div>
                <p className="text-sm font-medium">New Contacts</p>
                <p className="text-2xl font-bold text-green-600">{lastResult.newContacts}</p>
              </div>
              <div>
                <p className="text-sm font-medium">Conversations Found</p>
                <p className="text-2xl font-bold text-purple-600">{lastResult.conversationsFound}</p>
              </div>
            </div>

            {lastResult.errors.length > 0 && (
              <div className="mt-4">
                <p className="text-sm font-medium text-red-600 mb-2">Errors:</p>
                <ul className="text-sm text-red-600 space-y-1">
                  {lastResult.errors.map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Auto-Processing Info */}
      <Card>
        <CardHeader>
          <CardTitle>Automation</CardTitle>
          <CardDescription>
            Set up automatic processing of new followers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">Chrome Extension Integration</h4>
              <p className="text-sm text-blue-800">
                When the Chrome extension uploads new followers, they are added to the InstagramFollower table.
                Use this page to manually process them, or set up automatic processing.
              </p>
            </div>

            <div className="bg-yellow-50 p-4 rounded-lg">
              <h4 className="font-medium text-yellow-900 mb-2">Automatic Processing</h4>
              <p className="text-sm text-yellow-800">
                You can set up a cron job to call <code>/api/instagram/auto-process-followers</code>
                every 10 minutes to automatically process new followers across all organizations.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
