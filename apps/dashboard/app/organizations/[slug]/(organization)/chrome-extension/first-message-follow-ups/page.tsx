import { type Metadata } from 'next';

import { getAuthOrganizationContext } from '@workspace/auth/context';

import { Page, PageBody, PageHeader, PagePrimaryBar } from '@workspace/ui/components/page';
import { TransitionProvider } from '~/hooks/use-transition-context';

import { OrganizationPageTitle } from '~/components/organizations/slug/organization-page-title';
import { MessageBatchManager } from '~/components/instagram/message-batch-manager';
import { FollowUpManager } from '~/components/instagram/follow-up-manager';

export const metadata: Metadata = {
  title: 'First Message & Follow Up Templates'
};

export default async function FirstMessageFollowUpsPage(): Promise<React.JSX.Element> {
  const ctx = await getAuthOrganizationContext();

  return (
    <TransitionProvider>
      <Page>
        <PageHeader>
          <PagePrimaryBar>
            <OrganizationPageTitle
              title="First Message & Follow Up Templates"
              info="Configure first message batches and follow-up message templates for the Chrome Extension"
            />
          </PagePrimaryBar>
        </PageHeader>
        <PageBody className="p-6">
          <div className="space-y-8">
            <MessageBatchManager />
            <FollowUpManager />
          </div>
        </PageBody>
      </Page>
    </TransitionProvider>
  );
}
