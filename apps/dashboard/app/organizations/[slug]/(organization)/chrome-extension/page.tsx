import * as React from 'react';
import { DownloadIcon, ExternalLinkIcon, HistoryIcon } from 'lucide-react';

import { But<PERSON> } from '@workspace/ui/components/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Badge } from '@workspace/ui/components/badge';

export default function ChromeExtensionPage(): React.JSX.Element {
  const currentVersion = '1.0.1';
  const downloadUrl = 'https://app.aisetter.pl/extension/extension.zip';
  
  const changelog = [
    {
      version: '1.0.1',
      date: '2025-08-12',
      changes: [
        'First release'
      ],
      type: 'feature' as const
    }
  ];

  const getChangeTypeBadge = (type: string) => {
    switch (type) {
      case 'feature':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">New Feature</Badge>;
      case 'bugfix':
        return <Badge variant="destructive" className="bg-red-100 text-red-800">Bug Fix</Badge>;
      case 'improvement':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Improvement</Badge>;
      default:
        return <Badge variant="outline">Update</Badge>;
    }
  };

  return (
    <div className="px-6 py-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">Chrome Extension</h1>
        <p className="text-muted-foreground mt-2">
          Download the latest version of the AISetter Chrome Extension for Instagram DM automation
        </p>
      </div>

      {/* Download Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <DownloadIcon className="size-5" />
                Latest Version
              </CardTitle>
              <CardDescription>
                Current version: <strong>v{currentVersion}</strong>
              </CardDescription>
            </div>
            <Button asChild size="lg">
              <a href={downloadUrl} target="_blank" rel="noopener noreferrer" className="flex items-center">
                <DownloadIcon className="size-4 mr-2" />
                Download Extension
                <ExternalLinkIcon className="size-4 ml-2" />
              </a>
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          
          <div className="bg-muted/50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">Installation Instructions:</h4>
            <ol className="list-decimal list-inside space-y-1 text-sm text-muted-foreground">
              <li>Download the extension file from the link above</li>
              <li>Open Chrome and go to <code className="bg-muted px-1 rounded">chrome://extensions/</code></li>
              <li>Enable "Developer mode" in the top right</li>
              <li>Click "Load unpacked" and select the downloaded extension folder</li>
              <li>The AISetter extension icon should appear in your toolbar</li>
            </ol>
          </div>
        </CardContent>
      </Card>

      {/* Features Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Key Features</CardTitle>
          <CardDescription>
            What you can do with the AISetter Chrome Extension
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">📩 Auto DM for New Followers</h4>
              <p className="text-sm text-muted-foreground">
                Automatically send direct messages to new followers
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">⏰ Auto Follow Up</h4>
              <p className="text-sm text-muted-foreground">
                Auto message people outside 24h window
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">🔄 Auto Fetch New Followers</h4>
              <p className="text-sm text-muted-foreground">
                Automatically fetch and track new followers
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">🚀 Auto Fetch on Start</h4>
              <p className="text-sm text-muted-foreground">
                Auto fetch followers on initial start
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Changelog */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HistoryIcon className="size-5" />
            Changelog
          </CardTitle>
          <CardDescription>
            Recent updates and improvements to the extension
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {changelog.map((release) => (
              <div key={release.version} className="border-l-2 border-muted pl-4">
                <div className="flex items-center gap-3 mb-2">
                  <h4 className="font-medium">Version {release.version}</h4>
                  {getChangeTypeBadge(release.type)}
                  <span className="text-xs text-muted-foreground">{release.date}</span>
                </div>
                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                  {release.changes.map((change, index) => (
                    <li key={index}>{change}</li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}