import * as React from 'react';
import type { Metadata } from 'next';
import { unstable_noStore as noStore } from 'next/cache';

import { createTitle } from '~/lib/formatters';
import { MessageListDashboard } from '~/components/organizations/slug/instagram/message-list/message-list-dashboard';

export const metadata: Metadata = {
  title: createTitle('Message List')
};

export default async function InstagramMessageListPage(): Promise<React.JSX.Element> {
  // Disable caching for this page
  noStore();

  return (
    <div className="container mx-auto py-6">
      <MessageListDashboard />
    </div>
  );
}