import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@workspace/auth';
import { verifyApiKey } from '@workspace/api-keys';
import { prisma } from '@workspace/database/client';

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

// Handle preflight OPTIONS requests
export async function OPTIONS(_req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

/**
 * Check if scraping is allowed based on settings and continuous scraping rules
 */
export async function GET(req: NextRequest): Promise<Response> {
  const { searchParams } = new URL(req.url);
  const checkContinuous = searchParams.get('continuous') === 'true'; // Flag to check continuous scraping
  const hasMessages = searchParams.get('hasMessages') === 'true'; // Whether extension has messages to send
  
  try {
    // Check for API key first (Chrome extension)
    const apiKey = req.headers.get('X-API-Key');
    let organizationId: string;

    if (apiKey) {
      // API key authentication (Chrome extension)
      const result = await verifyApiKey(apiKey);
      if (!result.success) {
        return NextResponse.json(
          { success: false, error: result.errorMessage },
          { status: 401, headers: getCorsHeaders() }
        );
      }
      organizationId = result.organizationId;
    } else {
      // Session authentication (web dashboard)
      const session = await auth();
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401, headers: getCorsHeaders() }
        );
      }

      // Get user's organization
      const membership = await prisma.membership.findFirst({
        where: {
          userId: session.user.id
        }
      });

      if (!membership) {
        return NextResponse.json(
          { success: false, error: 'No organization found' },
          { status: 404, headers: getCorsHeaders() }
        );
      }

      organizationId = membership.organizationId;
    }

    // Get Chrome Extension settings
    const settings = await prisma.chromeExtensionSettings.findUnique({
      where: {
        organizationId
      }
    });

    const now = new Date();
    let isEligible = true;
    let nextAllowedAt: Date | null = null;
    let waitTimeMs = 0;
    let waitTimeHuman = '';
    let scrapingType: 'none' | 'initial' | 'continuous' | 'standard' = 'none';
    let reason = '';

    if (!settings) {
      // No settings, allow initial scraping
      isEligible = true;
      scrapingType = 'initial';
      reason = 'No settings found - initial scraping allowed';
    } else {
      // Check if attack past followers is disabled
      if (!settings.attackPastFollowers) {
        isEligible = false;
        scrapingType = 'none';
        reason = 'Attack past followers is disabled';
      }
      // Check for continuous scraping scenario
      else if (checkContinuous && settings.continuousScraping && !hasMessages) {
        // This is continuous scraping check - bot is idle with no messages
        if (settings.lastContinuousScrapingAt) {
          const hoursSinceLastScraping = (now.getTime() - settings.lastContinuousScrapingAt.getTime()) / (1000 * 60 * 60);
          
          if (hoursSinceLastScraping >= 1) {
            // 1 hour has passed, allow continuous scraping
            isEligible = true;
            scrapingType = 'continuous';
            reason = 'Continuous scraping allowed - 1 hour has passed';
          } else {
            // Must wait for 1 hour
            isEligible = false;
            scrapingType = 'none';
            const minutesRemaining = Math.ceil((1 - hoursSinceLastScraping) * 60);
            waitTimeMs = minutesRemaining * 60 * 1000;
            nextAllowedAt = new Date(settings.lastContinuousScrapingAt.getTime() + 60 * 60 * 1000);
            waitTimeHuman = `${minutesRemaining} minute${minutesRemaining !== 1 ? 's' : ''}`;
            reason = `Must wait ${waitTimeHuman} for continuous scraping`;
          }
        } else {
          // Never done continuous scraping, allow it
          isEligible = true;
          scrapingType = 'continuous';
          reason = 'First continuous scraping session allowed';
        }
      }
      // Check standard scraping rules (7-day interval)
      else if (settings.nextScrapingAllowedAt && settings.nextScrapingAllowedAt > now) {
        isEligible = false;
        scrapingType = 'none';
        nextAllowedAt = settings.nextScrapingAllowedAt;
        waitTimeMs = settings.nextScrapingAllowedAt.getTime() - now.getTime();
        
        // Convert to human readable format
        const days = Math.floor(waitTimeMs / (1000 * 60 * 60 * 24));
        const hours = Math.floor((waitTimeMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((waitTimeMs % (1000 * 60 * 60)) / (1000 * 60));
        
        if (days > 0) {
          waitTimeHuman = `${days} day${days > 1 ? 's' : ''}, ${hours} hour${hours > 1 ? 's' : ''}`;
        } else if (hours > 0) {
          waitTimeHuman = `${hours} hour${hours > 1 ? 's' : ''}, ${minutes} minute${minutes > 1 ? 's' : ''}`;
        } else {
          waitTimeHuman = `${minutes} minute${minutes > 1 ? 's' : ''}`;
        }
        reason = `Standard scraping interval - wait ${waitTimeHuman}`;
      }
      // Allow standard scraping
      else {
        isEligible = true;
        scrapingType = settings.extensionStatus === 'FRESH_START' ? 'initial' : 'standard';
        reason = scrapingType === 'initial' ? 'Initial scraping allowed' : 'Standard scraping allowed';
      }

      // Special case: if all followers are scraped, always allow (for testing/manual override)
      if (settings.allFollowersScraped) {
        isEligible = true;
        nextAllowedAt = null;
        waitTimeMs = 0;
        waitTimeHuman = '';
        reason = 'All followers scraped - manual override allowed';
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        isEligible,
        nextAllowedAt,
        waitTimeMs,
        waitTimeHuman,
        scrapingType,
        reason,
        intervalDays: settings?.scrapingIntervalDays || 5,
        lastScrapingSession: settings?.lastScrapingSession,
        lastContinuousScrapingAt: settings?.lastContinuousScrapingAt,
        totalFollowersScraped: settings?.totalFollowersScraped || 0,
        extensionStatus: settings?.extensionStatus || 'FRESH_START',
        allFollowersScraped: settings?.allFollowersScraped || false,
        attackPastFollowers: settings?.attackPastFollowers ?? true,
        continuousScraping: settings?.continuousScraping ?? true
      }
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error checking scraping eligibility:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}
