import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { verifyApi<PERSON><PERSON> } from '@workspace/api-keys';
import { prisma } from '@workspace/database/client';

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

// Handle preflight OPTIONS requests
export async function OPTIONS(req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

const AttackListQuerySchema = z.object({
  organizationId: z.string().uuid(),
  limit: z.number().min(1).max(100).default(50),
});

/**
 * NEW SIMPLIFIED Attack List for Chrome Extension
 * Single table query - 50%+ performance improvement over legacy endpoint
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    const { searchParams } = new URL(req.url);
    const organizationId = searchParams.get('organizationId');
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 100);
    
    if (!organizationId) {
      return NextResponse.json(
        { success: false, error: 'Organization ID is required' },
        { status: 400, headers: getCorsHeaders() }
      );
    }

    // Verify API key (Chrome extension only)
    const apiKey = req.headers.get('X-API-Key');
    if (!apiKey) {
      return NextResponse.json(
        { success: false, error: 'API key required' },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    const result = await verifyApiKey(apiKey);
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.errorMessage },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    // Validate organization access
    if (result.organizationId !== organizationId) {
      return NextResponse.json(
        { success: false, error: 'Organization access denied' },
        { status: 403, headers: getCorsHeaders() }
      );
    }

    // Get Chrome Extension settings for smart focus
    const chromeSettings = await prisma.chromeExtensionSettings.findUnique({
      where: { organizationId }
    });

    const smartFocus = chromeSettings?.smartFocus ?? false;

    // ULTRA-SIMPLE QUERY: Just get messages ready to send
    const attackList = await prisma.attackList.findMany({
      where: {
        organizationId,
        status: 'READY'
      },
      orderBy: smartFocus 
        ? [
            { priority: 'desc' }, // Smart focus: priority first
            { addedAt: 'asc' }     // Then by time added
          ]
        : [
            { addedAt: 'asc' },    // Normal: time first  
            { priority: 'desc' }   // Then by priority
          ],
      take: limit,
      select: {
        nickname: true,
        priority: true,
        messageType: true,
        messageContent: true,
        contactId: true,
        sequenceNumber: true
      }
    });

    // Get next refresh time (when more messages might be available)
    const nextMessage = await prisma.attackList.findFirst({
      where: {
        organizationId,
        status: 'READY'
      },
      orderBy: { addedAt: 'asc' },
      skip: limit,
      select: { addedAt: true }
    });

    const response = {
      success: true,
      data: attackList,
      count: attackList.length,
      metadata: {
        nextRefreshAt: nextMessage ? new Date(Date.now() + 60000).toISOString() : null, // 1 minute
        smartFocus,
        timestamp: new Date().toISOString()
      }
    };

    return NextResponse.json(response, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error fetching attack list v2:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}