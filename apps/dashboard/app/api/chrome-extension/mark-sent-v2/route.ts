import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { verifyApiKey } from '@workspace/api-keys';
import { prisma } from '@workspace/database/client';

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

// Handle preflight OPTIONS requests
export async function OPTIONS(req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

const MarkSentSchema = z.object({
  contactId: z.string().uuid(),
  messageType: z.enum(['FIRST_MESSAGE', 'FOLLOW_UP']),
  timestamp: z.string().datetime(),
});

/**
 * NEW SIMPLIFIED Mark Message Sent
 * Clean up AttackList and update contact status
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    // Verify API key (Chrome extension only)
    const apiKey = req.headers.get('X-API-Key');
    if (!apiKey) {
      return NextResponse.json(
        { success: false, error: 'API key required' },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    const result = await verifyApiKey(apiKey);
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.errorMessage },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    const organizationId = result.organizationId;
    const body = await req.json();
    const validatedData = MarkSentSchema.parse(body);

    // Use transaction to ensure data consistency
    const response = await prisma.$transaction(async (tx) => {
      // 1. Find and remove message from AttackList
      const attackListItem = await tx.attackList.findFirst({
        where: {
          organizationId,
          contactId: validatedData.contactId,
          messageType: validatedData.messageType,
          status: 'READY'
        }
      });

      if (!attackListItem) {
        throw new Error('Message not found in attack list');
      }

      // Remove from AttackList
      await tx.attackList.update({
        where: { id: attackListItem.id },
        data: { status: 'SENT' }
      });

      // 2. Update corresponding MessageList entry if exists
      if (attackListItem.messageListId) {
        await tx.messageList.update({
          where: { id: attackListItem.messageListId },
          data: { 
            status: 'SENT',
            updatedAt: new Date()
          }
        });
      }

      // 3. Update InstagramContact
      const contact = await tx.instagramContact.findUnique({
        where: { id: validatedData.contactId }
      });

      if (!contact) {
        throw new Error('Contact not found');
      }

      // Update contact fields
      const contactUpdate: any = {
        lastMessageSentAt: new Date(validatedData.timestamp),
        messagesSentCount: contact.messagesSentCount + 1,
        updatedAt: new Date()
      };

      // Update stage based on message type
      if (validatedData.messageType === 'FIRST_MESSAGE' && contact.stage === 'new') {
        contactUpdate.stage = 'initial';
      } else if (validatedData.messageType === 'FOLLOW_UP' && contact.stage === 'initial') {
        contactUpdate.stage = 'engaged';
      }

      await tx.instagramContact.update({
        where: { id: validatedData.contactId },
        data: contactUpdate
      });

      // 4. Create stage change log if stage changed
      if (contactUpdate.stage && contactUpdate.stage !== contact.stage) {
        await tx.stageChangeLog.create({
          data: {
            contactId: validatedData.contactId,
            fromStage: contact.stage,
            toStage: contactUpdate.stage,
            reason: `${validatedData.messageType.toLowerCase()} message sent`,
            changedBy: 'chrome_extension',
            metadata: {
              messageType: validatedData.messageType,
              timestamp: validatedData.timestamp
            }
          }
        });
      }

      // 4.5. Create follow-up templates after FIRST_MESSAGE is sent
      if (validatedData.messageType === 'FIRST_MESSAGE') {
        // Get organization's follow-up templates
        const followUpTemplates = await tx.followUpTemplate.findMany({
          where: {
            organizationId,
            isActive: true
          },
          orderBy: [
            { sequenceNumber: 'asc' },
            { variationNumber: 'asc' }
          ]
        });

        if (followUpTemplates.length > 0) {
          // Group templates by sequence number
          const templatesBySequence = followUpTemplates.reduce((acc, template) => {
            if (!acc[template.sequenceNumber]) {
              acc[template.sequenceNumber] = [];
            }
            acc[template.sequenceNumber].push(template);
            return acc;
          }, {} as Record<number, typeof followUpTemplates>);

          const sentTime = new Date(validatedData.timestamp);
          const sequenceNumbers = Object.keys(templatesBySequence).map(Number).sort();

          // Create follow-up MessageList entries for each sequence
          for (const sequenceNumber of sequenceNumbers) {
            const sequenceTemplates = templatesBySequence[sequenceNumber];
            if (sequenceTemplates && sequenceTemplates.length > 0) {
              // Pick a random variation for this sequence
              const randomTemplate = sequenceTemplates[Math.floor(Math.random() * sequenceTemplates.length)];

              // Calculate scheduled time based on template delay
              const scheduledTime = new Date(sentTime.getTime() + (randomTemplate.delayHours * 60 * 60 * 1000));

              // Determine handler type based on 24-hour window logic
              const now = new Date();
              const hoursDiff = (scheduledTime.getTime() - now.getTime()) / (1000 * 60 * 60);
              const isWithin24HourWindow = hoursDiff <= 24;
              const handlerType = isWithin24HourWindow ? 'SYSTEM' : 'EXTENSION';

              // Create MessageList entry
              await tx.messageList.create({
                data: {
                  organizationId,
                  contactId: validatedData.contactId,
                  nickname: attackListItem.nickname,
                  priority: contact.priority || 3,
                  messageType: 'EXTENSION',
                  handlerType,
                  scheduledTime,
                  messageContent: randomTemplate.messageText,
                  sequenceNumber,
                  status: 'PENDING'
                }
              });
            }
          }
        }
      }

      // 5. Update Chrome Extension daily counter
      const settings = await tx.chromeExtensionSettings.findUnique({
        where: { organizationId }
      });

      if (settings) {
        // Check if we need to reset the daily counter
        const now = new Date();
        const lastReset = new Date(settings.lastMessageResetDate);
        const needsReset = now.toDateString() !== lastReset.toDateString();

        await tx.chromeExtensionSettings.update({
          where: { organizationId },
          data: {
            messagesSentToday: needsReset ? 1 : settings.messagesSentToday + 1,
            lastMessageResetDate: needsReset ? now : settings.lastMessageResetDate,
            lastActivityAt: now,
            currentActivity: `Sent ${validatedData.messageType.toLowerCase().replace('_', ' ')} to ${attackListItem.nickname}`,
          }
        });
      }

      // 6. Check for next available message
      const nextMessage = await tx.attackList.findFirst({
        where: {
          organizationId,
          status: 'READY'
        },
        orderBy: { addedAt: 'asc' },
        select: { contactId: true, nickname: true }
      });

      return {
        success: true,
        action: 'message_sent_and_removed',
        data: {
          contactId: validatedData.contactId,
          messageType: validatedData.messageType,
          newStage: contactUpdate.stage || contact.stage,
          messagesSentCount: contact.messagesSentCount + 1
        },
        nextMessage: nextMessage ? {
          available: true,
          contactId: nextMessage.contactId,
          nickname: nextMessage.nickname
        } : {
          available: false
        }
      };
    });

    return NextResponse.json(response, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error marking message as sent v2:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400, headers: getCorsHeaders() }
      );
    }
    
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}