import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@workspace/auth';
import { verifyApiKey } from '@workspace/api-keys';
import { prisma } from '@workspace/database/client';
import { triggerFollowerProcessing } from '~/lib/instagram-follower-trigger';

/**
 * Enhanced status determination logic
 * Generates dynamic milestone statuses based on total scraped count
 */
function determineExtensionStatus(
  totalScraped: number,
  newFollowersCount: number,
  isComplete: boolean,
  reachedBottom: boolean,
): string {
  if (reachedBottom || isComplete) {
    return "ALL_SCRAPED";
  }

  const newTotal = totalScraped + newFollowersCount;

  // Special case for initial 50 followers
  if (newTotal >= 50 && newTotal < 250) {
    return "SCRAPED_50";
  }

  // Generate milestone status (every 250 followers after initial 50)
  const milestone = Math.floor(newTotal / 250) * 250;
  if (milestone > 0 && newTotal >= milestone) {
    return `SCRAPED_${milestone}`;
  }

  return "ACTIVE";
}

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

// Handle preflight OPTIONS requests
export async function OPTIONS(_req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

const ProcessFollowersSchema = z.object({
  followers: z.array(z.object({
    instagramNickname: z.string().min(1),
    instagramId: z.string().optional(),
    avatar: z.string().url().optional(),
    followerCount: z.number().int().min(0).optional(),
    isVerified: z.boolean().default(false)
  })).min(1).max(500),
  startPosition: z.number().int().min(0),
  totalFollowers: z.number().int().min(0).optional(),
  isComplete: z.boolean().optional(),
  // Enhanced tracking fields
  lastScrapedUsernames: z.array(z.string()).max(10).optional(),
});

/**
 * Process followers batch from Chrome Extension
 * Simply adds followers to InstagramFollower table
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    // Check for API key first (Chrome extension)
    const apiKey = req.headers.get('X-API-Key');
    let organizationId: string;
    let userId: string;

    if (apiKey) {
      // API key authentication (Chrome extension)
      const result = await verifyApiKey(apiKey);
      if (!result.success) {
        return NextResponse.json(
          { success: false, error: result.errorMessage },
          { status: 401, headers: getCorsHeaders() }
        );
      }
      organizationId = result.organizationId;

      // For API key auth, we need to get a user ID from the organization
      const membership = await prisma.membership.findFirst({
        where: {
          organizationId: result.organizationId,
          isOwner: true // Use the owner as the default user for API operations
        }
      });

      if (!membership) {
        return NextResponse.json(
          { success: false, error: 'No organization owner found' },
          { status: 404, headers: getCorsHeaders() }
        );
      }

      userId = membership.userId;
    } else {
      // Session authentication (web dashboard)
      const session = await auth();
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401, headers: getCorsHeaders() }
        );
      }

      // Get user's organization
      const membership = await prisma.membership.findFirst({
        where: {
          userId: session.user.id
        }
      });

      if (!membership) {
        return NextResponse.json(
          { success: false, error: 'No organization found' },
          { status: 404, headers: getCorsHeaders() }
        );
      }

      organizationId = membership.organizationId;
      userId = session.user.id;
    }



    const body = await req.json();
    console.log('🚨 DEBUG: Received request body:', JSON.stringify(body, null, 2));
    
    try {
      ProcessFollowersSchema.parse(body);
      console.log('🚨 DEBUG: Schema validation successful');
    } catch (validationError: unknown) {
      console.error('🚨 DEBUG: Schema validation failed:', validationError);
      const errorMessage = validationError instanceof Error ? validationError.message : 'Unknown validation error';
      return NextResponse.json(
        { success: false, error: `Validation error: ${errorMessage}` },
        { status: 400, headers: getCorsHeaders() }
      );
    }
    
    const validatedData = ProcessFollowersSchema.parse(body);

    // Process followers - simply add them to InstagramFollower table
    const results = {
      processed: 0,
      newFollowers: 0,
      existingFollowers: 0,
      errors: [] as string[]
    };

    for (const follower of validatedData.followers) {
      try {
        results.processed++;

        // Check if follower already exists in InstagramFollower table
        const existingFollower = await prisma.instagramFollower.findFirst({
          where: {
            organizationId,
            instagramNickname: follower.instagramNickname
          }
        });

        if (existingFollower) {
          results.existingFollowers++;
          continue;
        }

        // Add follower to InstagramFollower table
        const newFollower = await prisma.instagramFollower.create({
          data: {
            organizationId,
            userId: userId,
            instagramId: follower.instagramId,
            instagramNickname: follower.instagramNickname,
            avatar: follower.avatar,
            followerCount: follower.followerCount,
            isVerified: follower.isVerified,
            batchNumber: Math.floor(validatedData.startPosition / 500) + 1,
            priority: 'normal',
            status: 'pending',
            isTargeted: false,
            automationEnabled: true
          }
        });

        results.newFollowers++;

        console.log(`✅ Added ${follower.instagramNickname} to followers table`);

      } catch (error) {
        console.error(`Error processing follower ${follower.instagramNickname}:`, error);
        results.errors.push(`Failed to process ${follower.instagramNickname}: ${error}`);
      }
    }

    // Step 2: Update processing position
    await prisma.instagramFollowerProcessingState.upsert({
      where: {
        organizationId
      },
      update: {
        lastProcessedPosition: validatedData.startPosition + validatedData.followers.length,
        totalFollowersCount: validatedData.totalFollowers,
        lastProcessedAt: new Date(),
        updatedAt: new Date()
      },
      create: {
        organizationId,
        lastProcessedPosition: validatedData.startPosition + validatedData.followers.length,
        totalFollowersCount: validatedData.totalFollowers,
        lastProcessedAt: new Date()
      }
    });

    // Step 3: Enhanced status tracking - update ChromeExtensionSettings with new status
    const currentSettings = await prisma.chromeExtensionSettings.findUnique({
      where: { organizationId }
    });

    const currentTotalScraped = currentSettings?.totalFollowersScraped || 0;
    const newTotalScraped = currentTotalScraped + results.newFollowers;
    const isComplete = validatedData.isComplete || false;

    // Determine new status based on enhanced logic
    const newStatus = determineExtensionStatus(
      currentTotalScraped,
      results.newFollowers,
      isComplete,
      isComplete
    );

    // Prepare last scraped usernames (keep last 10)
    const newUsernames = validatedData.followers.map(f => f.instagramNickname);
    const existingUsernames = validatedData.lastScrapedUsernames || currentSettings?.lastScrapedUsernames || [];
    const combinedUsernames = [...existingUsernames, ...newUsernames];
    const lastScrapedUsernames = combinedUsernames.slice(-10); // Keep only last 10

    // CONTINUOUS SCRAPING: Allow scraping for catching up on new followers
    const scrapingIntervalDays = currentSettings?.scrapingIntervalDays || 5;
    let nextScrapingAllowedAt: Date | null = null;

    // For continuous scraping, we allow scraping to continue until duplicates are found
    // Only set scraping cooldown if this is marked as complete (end of session)
    if (isComplete && newTotalScraped >= 50) {
      // Set next allowed scraping time after normal interval (for scheduled scraping)
      nextScrapingAllowedAt = new Date();
      nextScrapingAllowedAt.setDate(nextScrapingAllowedAt.getDate() + scrapingIntervalDays);
      console.log(`🚀 Scraping session complete. Next scheduled scraping: ${nextScrapingAllowedAt.toISOString()}`);
    } else {
      // Keep nextScrapingAllowedAt as null to allow continuous scraping within the session
      console.log(`🚀 Continuous scraping in progress. ${results.newFollowers} new followers added.`);
    }

    // Update ChromeExtensionSettings with enhanced tracking
    await prisma.chromeExtensionSettings.upsert({
      where: { organizationId },
      update: {
        extensionStatus: newStatus,
        totalFollowersScraped: newTotalScraped,
        lastScrapedPosition: validatedData.startPosition + validatedData.followers.length,
        lastScrapedUsernames: lastScrapedUsernames,
        scrapingTargetReached: newTotalScraped >= 50, // Target reached at 50 followers
        allFollowersScraped: isComplete,
        lastScrapingSession: new Date(),
        nextScrapingAllowedAt: nextScrapingAllowedAt, // Set 5-day wait when batch completed
        currentActivity: isComplete ?
          `All ${newTotalScraped} followers processed` :
          `Processed ${results.newFollowers} followers (total: ${newTotalScraped})`,
        lastActivityAt: new Date(),
        updatedAt: new Date(),
      },
      create: {
        organizationId,
        extensionStatus: newStatus === 'SCRAPED_50' ? 'SCRAPED_250' : newStatus, // Map SCRAPED_50 to SCRAPED_250 for conversation gathering compatibility
        totalFollowersScraped: newTotalScraped,
        lastScrapedPosition: validatedData.startPosition + validatedData.followers.length,
        lastScrapedUsernames: lastScrapedUsernames,
        scrapingTargetReached: newTotalScraped >= 50, // Target reached at 50 followers
        allFollowersScraped: isComplete,
        lastScrapingSession: new Date(),
        nextScrapingAllowedAt: nextScrapingAllowedAt, // Set 5-day wait when batch completed
        currentActivity: isComplete ?
          `All ${newTotalScraped} followers processed` :
          `Processed ${results.newFollowers} followers (total: ${newTotalScraped})`,
        lastActivityAt: new Date(),
        // Default settings
        timeBetweenDMsMin: 3,
        timeBetweenDMsMax: 8,
        messagesBeforeBreakMin: 8,
        messagesBeforeBreakMax: 15,
        breakDurationMin: 10,
        breakDurationMax: 20,
        pauseStart: "00:30",
        pauseStop: "07:00",
        smartFocus: true,
        isConnected: true,
        lastConnectionAt: new Date(),
      }
    });

    console.log(`✅ Enhanced status tracking: ${newStatus} (${newTotalScraped} total followers)`);
    console.log(`✅ Added ${results.newFollowers} new followers to database`);

    // 🚀 INTELLIGENT PROCESSING: Check conversations and use AI analysis when available
    if (results.newFollowers > 0) {
      console.log(`🚀 Processing ${results.newFollowers} new followers with conversation checking...`);
      
      try {
        let processedCount = 0;
        let conversationCount = 0;
        let batchCount = 0;
        
        for (const follower of validatedData.followers) {
          try {
            const username = follower.instagramNickname;
            
            // Find the created follower record
            const followerRecord = await prisma.instagramFollower.findFirst({
              where: {
                organizationId,
                instagramNickname: username
              }
            });
            
            if (!followerRecord) {
              console.log(`⚠️ Follower record not found for ${username}, skipping`);
              continue;
            }

            // Use the existing sophisticated processing logic
            console.log(`👤 Processing ${username}...`);
            const processingResult = await triggerFollowerProcessing(followerRecord.id);
            
            if (processingResult.success) {
              processedCount++;
              
              // Log what type of processing was used
              const action = processingResult.data?.action;
              if (action === 'created_with_ai_analysis' || action === 'updated_existing_contact') {
                console.log(`👤 ${username}: HAS CONVERSATION → AI (priority: ${processingResult.data?.priority || 'unknown'})`);
                conversationCount++;
              } else if (action === 'created_with_batch_messages') {
                console.log(`👤 ${username}: NO CONVERSATION → BATCH (priority: ${processingResult.data?.priority || 3})`);
                batchCount++;
              } else {
                console.log(`👤 ${username}: ${action || 'PROCESSED'}`);
              }
            } else {
              console.error(`❌ Failed to process ${username}: ${processingResult.error}`);
            }

            // Add small delay between followers to prevent overwhelming the system
            await new Promise(resolve => setTimeout(resolve, 100));

          } catch (error) {
            console.error(`❌ Error processing ${follower.instagramNickname}:`, error);
          }
        }
        
        console.log(`✅ Processing completed: ${processedCount}/${results.newFollowers} followers processed`);
        console.log(`📊 Breakdown: ${conversationCount} with conversations (AI), ${batchCount} without (batch messages)`);
        
        // Update extension status to ACTIVE
        await prisma.chromeExtensionSettings.update({
          where: { organizationId },
          data: {
            extensionStatus: 'ACTIVE',
            currentActivity: `Ready for messaging - processed ${processedCount} followers (${conversationCount} AI, ${batchCount} batch)`,
            lastActivityAt: new Date()
          }
        });

        console.log(`✅ Extension status updated to ACTIVE - ready for messaging`);
        
      } catch (error) {
        console.error('❌ Error in follower processing:', error);
        // Continue - don't fail the upload
      }
    }

    // Determine if scraping should continue based on duplicate rate
    const duplicateRate = results.processed > 0 ? results.existingFollowers / results.processed : 0;
    const shouldContinueScraping = duplicateRate < 0.8 && results.newFollowers > 0; // Stop if 80%+ are duplicates or no new followers

    return NextResponse.json({
      success: true,
      message: `Successfully uploaded ${results.processed} followers. Added ${results.newFollowers} new followers to processing queue, ${results.existingFollowers} already existed.`,
      data: {
        ...results,
        shouldContinueScraping,
        duplicateRate: Math.round(duplicateRate * 100),
        recommendation: shouldContinueScraping ?
          'Continue scraping - found new followers' :
          'Stop scraping - reached existing followers or high duplicate rate'
      }
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error processing followers:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}


export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check for API key first (Chrome extension)
    const apiKey = req.headers.get('X-API-Key');
    let organizationId: string;

    if (apiKey) {
      // API key authentication (Chrome extension)
      const result = await verifyApiKey(apiKey);
      if (!result.success) {
        return NextResponse.json(
          { success: false, error: result.errorMessage },
          { status: 401, headers: getCorsHeaders() }
        );
      }
      organizationId = result.organizationId;
    } else {
      // Session authentication (web dashboard)
      const session = await auth();
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401, headers: getCorsHeaders() }
        );
      }

      // Get user's organization
      const membership = await prisma.membership.findFirst({
        where: {
          userId: session.user.id
        }
      });

      if (!membership) {
        return NextResponse.json(
          { success: false, error: 'No organization found' },
          { status: 404, headers: getCorsHeaders() }
        );
      }

      organizationId = membership.organizationId;
    }

    // Get processing state
    const processingState = await prisma.instagramFollowerProcessingState.findUnique({
      where: {
        organizationId
      }
    });

    // Get counts
    const totalFollowers = await prisma.instagramFollower.count({
      where: { organizationId }
    });

    const pendingFollowers = await prisma.instagramFollower.count({
      where: {
        organizationId,
        status: 'pending'
      }
    });

    const contactedFollowers = await prisma.instagramFollower.count({
      where: {
        organizationId,
        status: 'contacted'
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        processingState: processingState || {
          position: 0,
          totalFollowers: null,
          isCompleted: false,
          lastProcessedAt: null
        },
        stats: {
          totalFollowers,
          pendingFollowers,
          contactedFollowers
        }
      }
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error getting processing status:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}
