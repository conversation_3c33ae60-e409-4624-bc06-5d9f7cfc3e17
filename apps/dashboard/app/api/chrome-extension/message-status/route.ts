import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

// Handle preflight OPTIONS requests
export async function OPTIONS(req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

const UpdateMessageStatusSchema = z.object({
  contactId: z.string().uuid(),
  status: z.enum(['sent', 'failed']),
  messageType: z.enum(['initial', 'follow_up', 'batch_sequence']).optional(),
  followUpId: z.string().uuid().optional(),
  messageSequence: z.number().int().min(1).optional(), // Which message in sequence was sent
  error: z.string().optional()
});

/**
 * Update message status for Chrome Extension
 * Used when Chrome extension sends messages and needs to update status
 */
export async function PUT(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    const body = await req.json();
    const validatedData = UpdateMessageStatusSchema.parse(body);

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404, headers: getCorsHeaders() }
      );
    }

    const organizationId = membership.organizationId;

    // Find the contact
    const contact = await prisma.instagramContact.findFirst({
      where: {
        id: validatedData.contactId,
        organizationId
      }
    });

    if (!contact) {
      return NextResponse.json(
        { success: false, error: 'Contact not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {
      updatedAt: new Date()
    };

    // If message was sent successfully
    if (validatedData.status === 'sent') {
      // Update message sequence tracking and set lastInteractionAt when message is actually sent
      const currentSequence = validatedData.messageSequence || contact.currentMessageSequence || 1;
      updateData.currentMessageSequence = currentSequence;
      updateData.lastMessageSentAt = new Date();
      updateData.lastInteractionAt = new Date(); // Set interaction time when message is actually sent

      // Handle different message types
      if (validatedData.messageType === 'follow_up' && validatedData.followUpId) {
        // Mark specific follow-up as sent
        await prisma.messageList.update({
          where: { id: validatedData.followUpId },
          data: {
            status: 'SENT',
            sentAt: new Date()
          }
        });
      } else if (validatedData.messageType === 'initial' || validatedData.messageType === 'batch_sequence') {
        // For initial messages, mark batch message status as sent
        if (currentSequence === 1) {
          updateData.batchMessageStatus = 'sent';
        }

        // For batch sequence contacts, create follow-ups if this is the first message
        if (contact.priority === 3 && contact.stage === 'new' && currentSequence === 1) {
          // Get the batch messages for this contact
          const messageBatch = await prisma.messageBatch.findFirst({
            where: { organizationId },
            include: {
              MessageBatchItem: {
                where: { sequenceNumber: { gt: 1 } }, // Get follow-up messages (sequence 2, 3)
                orderBy: { sequenceNumber: 'asc' }
              }
            }
          });

          // Create follow-ups from batch messages with configurable delays
          if (messageBatch && messageBatch.MessageBatchItem.length > 0) {
            const sentTime = new Date(); // Time when message was actually sent

            for (const batchItem of messageBatch.MessageBatchItem) {
              // Calculate scheduled time based on delayMinutes from the batch item
              const delayMs = (batchItem.delayMinutes || 1440) * 60 * 1000; // Default to 24h if no delay specified
              const scheduledTime = new Date(sentTime.getTime() + delayMs);

              await prisma.messageList.create({
                data: {
                  organizationId,
                  contactId: contact.id,
                  nickname: contact.instagramNickname,
                  priority: contact.priority || 3,
                  messageType: 'EXTENSION',
                  scheduledTime: scheduledTime,
                  messageContent: batchItem.messageText,
                  status: 'EXTERNAL', // External so it appears in attack list
                  handlerType: 'EXTENSION',
                  sequenceNumber: batchItem.sequenceNumber
                }
              });
            }
          }

          // Change priority to 1 for follow-ups
          updateData.priority = 1;
        }
      }

      // Update next message time based on pending follow-ups
      const nextFollowUp = await prisma.messageList.findFirst({
        where: {
          contactId: contact.id,
          status: { in: ['PENDING', 'EXTERNAL'] }
        },
        orderBy: { scheduledTime: 'asc' }
      });

      if (nextFollowUp) {
        updateData.nextMessageAt = nextFollowUp.scheduledTime;
        updateData.attackListStatus = 'pending';
      } else {
        updateData.attackListStatus = 'completed';
        updateData.nextMessageAt = null;
      }
    } else if (validatedData.status === 'failed') {
      // Handle failed messages
      if (validatedData.followUpId) {
        await prisma.messageList.update({
          where: { id: validatedData.followUpId },
          data: { status: 'FAILED' }
        });
      }
      updateData.attackListStatus = 'failed';
    }

    // Update the contact
    const updatedContact = await prisma.instagramContact.update({
      where: {
        id: validatedData.contactId
      },
      data: updateData
    });

    return NextResponse.json({
      success: true,
      data: {
        contactId: updatedContact.id,
        attackListStatus: updatedContact.attackListStatus,
        nextMessageAt: updatedContact.nextMessageAt,
        priority: updatedContact.priority,
        lastInteractionAt: updatedContact.lastInteractionAt
      }
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error updating message status:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}

/**
 * Get message status for contacts
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const attackListStatus = searchParams.get('status');
    const priority = searchParams.get('priority');

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;

    // Build where clause
    const where: any = {
      organizationId
    };

    if (attackListStatus) where.attackListStatus = attackListStatus;
    if (priority) where.priority = parseInt(priority);

    // Get contacts with their follow-ups
    const contacts = await prisma.instagramContact.findMany({
      where,
      select: {
        id: true,
        instagramId: true,
        instagramNickname: true,
        avatar: true,
        priority: true,
        nextMessageAt: true,
        attackListStatus: true,
        stage: true,
        lastInteractionAt: true,
        MessageList: {
          where: {
            status: { in: ['PENDING', 'EXTERNAL'] }
          },
          orderBy: { scheduledTime: 'asc' }
        }
      },
      orderBy: [
        { priority: 'desc' },
        { nextMessageAt: 'asc' }
      ]
    });

    return NextResponse.json({
      success: true,
      data: contacts,
      count: contacts.length
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error getting message status:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}
