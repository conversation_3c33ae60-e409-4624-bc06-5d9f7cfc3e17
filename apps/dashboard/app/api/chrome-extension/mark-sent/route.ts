import { NextRequest, NextResponse } from 'next/server';

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

export async function OPTIONS(req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

/**
 * DEPRECATED - Use /api/chrome-extension/mark-sent-v2 instead
 * This endpoint has been replaced with simplified AttackList architecture
 */
export async function POST(req: NextRequest): Promise<Response> {
  return NextResponse.json({
    success: false,
    error: 'DEPRECATED_ENDPOINT',
    message: 'This endpoint has been deprecated. Use /api/chrome-extension/mark-sent-v2 instead.',
    newEndpoint: '/api/chrome-extension/mark-sent-v2',
    migration: {
      reason: 'Simplified architecture with AttackList and MessageList tables',
      benefits: ['Cleaner data flow', 'Better performance', 'Simplified logic'],
      changes: {
        requestFormat: 'Updated to use messageType enum (FIRST_MESSAGE, FOLLOW_UP)',
        responseFormat: 'Streamlined response with next message info',
        processing: 'Single transaction for all updates'
      }
    }
  }, {
    status: 410, // Gone
    headers: getCorsHeaders()
  });
}
