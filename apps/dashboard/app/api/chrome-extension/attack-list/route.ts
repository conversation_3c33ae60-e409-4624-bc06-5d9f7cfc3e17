import { NextRequest, NextResponse } from 'next/server';

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

export async function OPTIONS(req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

/**
 * DEPRECATED - Use /api/chrome-extension/attack-list-v2 instead
 * This endpoint has been replaced with simplified AttackList architecture
 */
export async function GET(req: NextRequest): Promise<Response> {
  return NextResponse.json({
    success: false,
    error: 'DEPRECATED_ENDPOINT',
    message: 'This endpoint has been deprecated. Use /api/chrome-extension/attack-list-v2 instead.',
    newEndpoint: '/api/chrome-extension/attack-list-v2',
    migration: {
      reason: 'Performance optimization and simplified architecture',
      benefits: ['50%+ faster response times', 'Simplified single-table queries', 'Better reliability']
    }
  }, {
    status: 410, // Gone
    headers: getCorsHeaders()
  });
}

export async function PUT(req: NextRequest): Promise<Response> {
  return NextResponse.json({
    success: false,
    error: 'DEPRECATED_ENDPOINT',
    message: 'This endpoint has been deprecated. Use /api/chrome-extension/attack-list-v2 for queries and /api/chrome-extension/mark-sent-v2 for updates.',
    newEndpoints: {
      query: '/api/chrome-extension/attack-list-v2',
      markSent: '/api/chrome-extension/mark-sent-v2'
    }
  }, {
    status: 410, // Gone
    headers: getCorsHeaders()
  });
}
