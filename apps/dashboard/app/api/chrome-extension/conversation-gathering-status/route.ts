import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@workspace/database/client';
import { verifyApiKey } from '@workspace/api-keys';

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

// Handle preflight OPTIONS requests
export async function OPTIONS(req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

/**
 * Check conversation gathering status for Chrome extension
 * Returns status of conversation gathering process and progress
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check for API key (Chrome extension)
    const apiKey = req.headers.get('X-API-Key');
    if (!apiKey) {
      return NextResponse.json(
        { success: false, message: 'API key required' },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    // Verify API key and get organization
    const result = await verifyApiKey(apiKey);
    if (!result.success) {
      return NextResponse.json(
        { success: false, message: result.errorMessage },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    const organizationId = result.organizationId;

    // Get Chrome extension settings
    const settings = await prisma.chromeExtensionSettings.findUnique({
      where: { organizationId }
    });

    if (!settings) {
      return NextResponse.json(
        { success: false, message: 'Chrome extension settings not found' },
        { status: 404, headers: getCorsHeaders() }
      );
    }

    // OPTIMIZED SYSTEM: Check conversation gathering status from InstagramConversationsNotGathered table
    const totalConversations = await prisma.instagramConversationsNotGathered.count({
      where: { organizationId }
    });

    const gatheredConversations = await prisma.instagramConversationsNotGathered.count({
      where: {
        organizationId,
        isGathered: true
      }
    });

    const pendingConversations = await prisma.instagramConversationsNotGathered.count({
      where: {
        organizationId,
        isGathered: false
      }
    });

    // Determine gathering status for optimized system
    const isGathering = settings.extensionStatus === 'CONVERSATIONS_GATHERING';
    const hasConversations = totalConversations > 0;
    const allGathered = hasConversations && gatheredConversations === totalConversations;
    const isComplete = hasConversations && allGathered && pendingConversations === 0;

    // Auto-transition status when conversation gathering is complete
    if (isComplete && settings.extensionStatus === 'CONVERSATIONS_GATHERING') {
      console.log('🔄 SIMPLE_SYSTEM: Auto-transitioning status to CONVERSATIONS_GATHERED_READY');

      try {
        await prisma.chromeExtensionSettings.update({
          where: { organizationId },
          data: {
            extensionStatus: 'CONVERSATIONS_GATHERED_READY',
            currentActivity: 'All conversations gathered - ready for follower processing',
            lastActivityAt: new Date()
          }
        });

        console.log('✅ OPTIMIZED_SYSTEM: Status auto-updated to CONVERSATIONS_GATHERED_READY');
      } catch (error) {
        console.error('❌ Error auto-updating extension status:', error);
      }
    }

    // Calculate progress for optimized system
    const percentComplete = totalConversations > 0
      ? Math.round((totalConversations / totalConversations) * 100)  // Always 100% since gathering happens all at once now
      : 0;

    // Estimate time remaining (optimized system is blazing fast - happens during OAuth)
    const estimatedTimeRemaining = isGathering && !isComplete ? "Less than 30 seconds" : null;

    // Get timing information for conversation gathering
    const firstConversation = await prisma.instagramConversationsNotGathered.findFirst({
      where: { organizationId },
      orderBy: { createdAt: 'asc' }
    });

    const lastGatheredConversation = await prisma.instagramConversationsNotGathered.findFirst({
      where: {
        organizationId,
        isGathered: true
      },
      orderBy: { updatedAt: 'desc' }
    });

    const response = {
      success: true,
      data: {
        isGathering,
        isComplete,
        progress: {
          totalContacts: totalConversations,
          processedContacts: totalConversations,  // All gathered at once in optimized system
          percentComplete
        },
        estimatedTimeRemaining,
        startedAt: firstConversation?.createdAt || null,
        completedAt: isComplete ? (lastGatheredConversation?.updatedAt || new Date()) : null,
        // Additional status information
        extensionStatus: settings.extensionStatus,
        pendingConversationItems: pendingConversations,
        hasConversations
      }
    };

    console.log('🔄 OPTIMIZED_SYSTEM: Status check:', {
      isGathering,
      isComplete,
      totalConversations,
      gatheredConversations,
      percentComplete,
      pendingConversations,
      extensionStatus: settings.extensionStatus
    });

    return NextResponse.json(response, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error checking conversation gathering status:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}
