import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { verifyApi<PERSON><PERSON> } from '@workspace/api-keys';
import { prisma } from '@workspace/database/client';
import { InstagramContactStage } from '@workspace/database';

import { 
  handleContactDisqualification, 
  shouldTriggerDisqualificationCleanup 
} from '~/lib/contact-disqualification-cleanup';

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

// Handle preflight OPTIONS requests
export async function OPTIONS(req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

const disqualifyContactSchema = z.object({
  contactId: z.string().uuid().optional(),
  instagramUsername: z.string().optional(),
  reason: z.string().optional(),
  // Support bulk operations
  contactIds: z.array(z.string().uuid()).optional(),
  instagramUsernames: z.array(z.string()).optional()
}).refine(
  (data) => 
    (data.contactId || data.instagramUsername || 
     (data.contactIds && data.contactIds.length > 0) || 
     (data.instagramUsernames && data.instagramUsernames.length > 0)),
  {
    message: "Must provide either contactId, instagramUsername, contactIds, or instagramUsernames"
  }
);

/**
 * Disqualify Instagram contact(s) and perform complete cleanup
 * Used by Chrome extension or other API clients
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    // Check for API key (Chrome extension)
    const apiKey = req.headers.get('X-API-Key');
    if (!apiKey) {
      return NextResponse.json(
        { success: false, error: 'API key required' },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    // Verify API key
    const result = await verifyApiKey(apiKey);
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.errorMessage },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    const organizationId = result.organizationId;
    const body = await req.json();
    const validatedData = disqualifyContactSchema.parse(body);

    // Collect contact IDs to process
    const contactIdsToProcess: string[] = [];

    // Handle single contact ID
    if (validatedData.contactId) {
      contactIdsToProcess.push(validatedData.contactId);
    }

    // Handle bulk contact IDs
    if (validatedData.contactIds) {
      contactIdsToProcess.push(...validatedData.contactIds);
    }

    // Handle single Instagram username
    if (validatedData.instagramUsername) {
      const contact = await prisma.instagramContact.findFirst({
        where: {
          organizationId,
          instagramNickname: validatedData.instagramUsername
        },
        select: { id: true }
      });
      
      if (contact) {
        contactIdsToProcess.push(contact.id);
      }
    }

    // Handle bulk Instagram usernames
    if (validatedData.instagramUsernames) {
      const contacts = await prisma.instagramContact.findMany({
        where: {
          organizationId,
          instagramNickname: { in: validatedData.instagramUsernames }
        },
        select: { id: true, instagramNickname: true }
      });
      
      contactIdsToProcess.push(...contacts.map(c => c.id));
    }

    if (contactIdsToProcess.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No valid contacts found to disqualify' },
        { status: 404, headers: getCorsHeaders() }
      );
    }

    // Remove duplicates
    const uniqueContactIds = [...new Set(contactIdsToProcess)];

    console.log(`Chrome Extension: Disqualifying ${uniqueContactIds.length} contact(s)...`);

    // Process each contact
    const results = [];
    for (const contactId of uniqueContactIds) {
      // First, update the contact stage to disqualified
      const contact = await prisma.instagramContact.findFirst({
        where: {
          id: contactId,
          organizationId
        },
        select: { id: true, stage: true, instagramNickname: true }
      });

      if (!contact) {
        results.push({
          contactId,
          success: false,
          error: 'Contact not found'
        });
        continue;
      }

      // Check if we need to trigger cleanup
      if (!shouldTriggerDisqualificationCleanup(contact.stage, InstagramContactStage.disqualified)) {
        results.push({
          contactId,
          success: true,
          message: 'Contact already disqualified',
          actions: {
            pendingFollowUpsRemoved: 0,
            externalFollowUpsRemoved: 0,
            legacyFollowUpsCleared: 0,
            attackListCleared: false,
            automationStopped: false,
            scheduledMessagesCleared: false,
          }
        });
        continue;
      }

      try {
        // Update stage to disqualified first
        await prisma.$transaction(async (tx) => {
          await tx.instagramContact.update({
            where: { id: contactId },
            data: {
              stage: InstagramContactStage.disqualified,
              updatedAt: new Date()
            }
          });

          // Log the stage change
          await tx.stageChangeLog.create({
            data: {
              contactId,
              fromStage: contact.stage,
              toStage: InstagramContactStage.disqualified,
              reason: validatedData.reason || 'Disqualified via Chrome Extension API',
              changedBy: 'chrome_extension',
              metadata: {
                action: 'chrome_extension_disqualify',
                apiKey: apiKey.substring(0, 8) + '...',
                timestamp: new Date().toISOString()
              }
            }
          });
        });

        // Perform cleanup
        const cleanupResult = await handleContactDisqualification(
          contactId,
          organizationId,
          validatedData.reason || 'Disqualified via Chrome Extension'
        );

        results.push({
          contactId,
          instagramUsername: contact.instagramNickname,
          success: cleanupResult.success,
          message: cleanupResult.success ? 'Contact disqualified and cleaned up successfully' : 'Disqualification completed but cleanup failed',
          actions: cleanupResult.actions,
          error: cleanupResult.error
        });

      } catch (error) {
        console.error(`Error disqualifying contact ${contactId}:`, error);
        results.push({
          contactId,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // Calculate summary statistics
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    const totalFollowUpsRemoved = results
      .filter(r => r.success && r.actions)
      .reduce((sum, r) => sum + (r.actions?.pendingFollowUpsRemoved || 0) + (r.actions?.externalFollowUpsRemoved || 0), 0);

    console.log(`Chrome Extension: Disqualification completed - ${successful} successful, ${failed} failed, ${totalFollowUpsRemoved} follow-ups removed`);

    return NextResponse.json({
      success: successful > 0,
      summary: {
        total: uniqueContactIds.length,
        successful,
        failed,
        totalFollowUpsRemoved
      },
      results
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error in Chrome Extension disqualify contact endpoint:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid request data', 
          details: error.errors 
        },
        { status: 400, headers: getCorsHeaders() }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}

/**
 * Get disqualification cleanup statistics
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check for API key
    const apiKey = req.headers.get('X-API-Key');
    if (!apiKey) {
      return NextResponse.json(
        { success: false, error: 'API key required' },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    // Verify API key
    const result = await verifyApiKey(apiKey);
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.errorMessage },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    const organizationId = result.organizationId;

    // Get cleanup statistics
    const stats = await prisma.instagramContact.groupBy({
      by: ['stage'],
      where: { organizationId },
      _count: { stage: true }
    });

    const stageDistribution: Record<string, number> = {};
    stats.forEach(stat => {
      stageDistribution[stat.stage] = stat._count.stage;
    });

    // Get contacts that might need cleanup
    const contactsNeedingCleanup = await prisma.instagramContact.count({
      where: {
        organizationId,
        stage: InstagramContactStage.disqualified,
        OR: [
          {
            MessageList: {
              some: {
                status: { in: ['PENDING', 'EXTERNAL'] }
              }
            }
          },
          { attackListStatus: { not: 'disqualified' } },
          { nextMessageAt: { not: null } }
        ]
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        stageDistribution,
        disqualifiedCount: stageDistribution.disqualified || 0,
        contactsNeedingCleanup
      }
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error getting disqualification stats:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}