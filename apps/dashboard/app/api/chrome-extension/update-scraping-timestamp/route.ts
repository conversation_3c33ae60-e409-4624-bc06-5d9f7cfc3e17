import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { verifyApiKey } from '@workspace/api-keys';
import { prisma } from '@workspace/database/client';

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

// Handle preflight OPTIONS requests
export async function OPTIONS(req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

const UpdateScrapingSchema = z.object({
  scrapingType: z.enum(['continuous', 'standard', 'initial']),
  followersScraped: z.number().optional(),
  isComplete: z.boolean().optional(),
});

/**
 * Update scraping timestamp after performing scraping
 * This is called after continuous scraping to enforce the 1-hour break
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    // Check for API key (Chrome extension)
    const apiKey = req.headers.get('X-API-Key');
    if (!apiKey) {
      return NextResponse.json(
        { success: false, error: 'API key required' },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    // Verify API key
    const result = await verifyApiKey(apiKey);
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.errorMessage },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    const organizationId = result.organizationId;
    const body = await req.json();
    const validatedData = UpdateScrapingSchema.parse(body);

    // Get current settings
    const settings = await prisma.chromeExtensionSettings.findUnique({
      where: { organizationId },
    });

    if (!settings) {
      return NextResponse.json(
        { success: false, error: 'Settings not found' },
        { status: 404, headers: getCorsHeaders() }
      );
    }

    const now = new Date();
    const updateData: any = {
      lastActivityAt: now,
      currentActivity: `${validatedData.scrapingType} scraping completed`,
    };

    // Update based on scraping type
    if (validatedData.scrapingType === 'continuous') {
      // Update continuous scraping timestamp for 1-hour break enforcement
      updateData.lastContinuousScrapingAt = now;
      
      // Update follower count if provided
      if (validatedData.followersScraped !== undefined) {
        updateData.totalFollowersScraped = settings.totalFollowersScraped + validatedData.followersScraped;
        updateData.lastScrapedPosition = settings.lastScrapedPosition + validatedData.followersScraped;
      }
    } else if (validatedData.scrapingType === 'standard' || validatedData.scrapingType === 'initial') {
      // Update standard scraping timestamp and set 7-day wait
      updateData.lastScrapingSession = now;
      
      if (validatedData.followersScraped !== undefined) {
        updateData.totalFollowersScraped = settings.totalFollowersScraped + validatedData.followersScraped;
        updateData.lastScrapedPosition = settings.lastScrapedPosition + validatedData.followersScraped;
      }
      
      // Set next allowed scraping time (7 days for standard scraping)
      if (validatedData.isComplete) {
        const scrapingIntervalDays = settings.scrapingIntervalDays || 7;
        updateData.nextScrapingAllowedAt = new Date(now.getTime() + scrapingIntervalDays * 24 * 60 * 60 * 1000);
      }
      
      // Update status based on followers scraped
      if (validatedData.followersScraped && validatedData.followersScraped > 0) {
        const totalScraped = settings.totalFollowersScraped + validatedData.followersScraped;
        if (totalScraped >= 250 && totalScraped < 500) {
          updateData.extensionStatus = 'SCRAPED_250';
        } else if (totalScraped >= 500 && totalScraped < 1000) {
          updateData.extensionStatus = 'SCRAPED_500';
        } else if (totalScraped >= 1000) {
          updateData.extensionStatus = `SCRAPED_${totalScraped}`;
        }
      }
    }

    // Update settings
    const updatedSettings = await prisma.chromeExtensionSettings.update({
      where: { organizationId },
      data: updateData,
    });

    return NextResponse.json({
      success: true,
      data: {
        scrapingType: validatedData.scrapingType,
        totalFollowersScraped: updatedSettings.totalFollowersScraped,
        lastContinuousScrapingAt: updatedSettings.lastContinuousScrapingAt?.toISOString(),
        lastScrapingSession: updatedSettings.lastScrapingSession?.toISOString(),
        nextScrapingAllowedAt: updatedSettings.nextScrapingAllowedAt?.toISOString(),
        extensionStatus: updatedSettings.extensionStatus,
        nextContinuousScrapingAt: updatedSettings.lastContinuousScrapingAt 
          ? new Date(updatedSettings.lastContinuousScrapingAt.getTime() + 60 * 60 * 1000).toISOString()
          : null,
      },
    }, {
      headers: getCorsHeaders(),
    });

  } catch (error) {
    console.error('Error updating scraping timestamp:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400, headers: getCorsHeaders() }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}