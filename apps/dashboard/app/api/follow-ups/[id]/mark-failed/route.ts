import { NextRequest, NextResponse } from 'next/server';
import { verifyApi<PERSON><PERSON> } from '@workspace/api-keys';
import { prisma } from '@workspace/database/client';

/**
 * Mark a follow-up as failed
 * This endpoint is used by the Chrome extension to mark follow-up messages as failed
 */
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
): Promise<Response> {
  try {
    const { id } = params;

    // Get API key from header
    const apiKey = req.headers.get('X-API-Key');

    if (!apiKey) {
      return NextResponse.json(
        { success: false, message: 'API key is missing' },
        { status: 401 }
      );
    }

    // Verify the API key
    const result = await verifyApiKey(apiKey);

    if (!result.success) {
      return NextResponse.json(
        { success: false, message: result.errorMessage },
        { status: 401 }
      );
    }

    const organizationId = result.organizationId;

    // Get error message from request body if provided
    const body = await req.json().catch(() => ({}));
    const errorMessage = body.error || 'Unknown error';

    // Check if it's a new follow-up (UUID) or legacy follow-up (contactId_fuN)
    if (id.includes('_fu')) {
      // Legacy follow-up format: contactId_fu1, contactId_fu2, etc.
      const [contactId, followUpType] = id.split('_');
      const followUpNumber = parseInt(followUpType.replace('fu', ''));

      if (isNaN(followUpNumber) || followUpNumber < 1 || followUpNumber > 4) {
        return NextResponse.json(
          { success: false, message: 'Invalid follow-up ID' },
          { status: 400 }
        );
      }

      // Check if the contact exists and belongs to the organization
      const contact = await prisma.instagramContact.findFirst({
        where: {
          id: contactId,
          organizationId
        }
      });

      if (!contact) {
        return NextResponse.json(
          { success: false, message: 'Contact not found' },
          { status: 404 }
        );
      }

      // Update the follow-up status
      const updateData: any = {};
      updateData[`followUpStatus${followUpNumber}`] = 'failed';

      await prisma.instagramContact.update({
        where: { id: contactId },
        data: updateData
      });

      return NextResponse.json({
        success: true,
        message: `Follow-up ${followUpNumber} marked as failed`
      });
    } else {
      // MessageList format: UUID
      const followUp = await prisma.messageList.findFirst({
        where: {
          id,
          organizationId
        }
      });

      if (!followUp) {
        return NextResponse.json(
          { success: false, message: 'Follow-up not found' },
          { status: 404 }
        );
      }

      // Update the follow-up status to failed
      await prisma.messageList.update({
        where: { id },
        data: {
          status: 'FAILED'
        }
      });

      return NextResponse.json({
        success: true,
        message: `Follow-up marked as failed`
      });
    }
  } catch (error) {
    console.error('Error marking follow-up as failed:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
