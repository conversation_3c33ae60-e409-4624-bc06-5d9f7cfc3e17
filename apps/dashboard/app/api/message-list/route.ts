import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@workspace/auth';
import { verifyApi<PERSON>ey } from '@workspace/api-keys';
import { prisma } from '@workspace/database/client';

// CORS headers
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

export async function OPTIONS(req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

const MessageListQuerySchema = z.object({
  organizationId: z.string().uuid().optional(),
  status: z.enum(['PENDING', 'PROCESSING', 'SENT', 'FAILED']).optional(),
  handlerType: z.enum(['SYSTEM', 'EXTENSION']).optional(),
  messageType: z.enum(['SYSTEM', 'EXTENSION']).optional(),
  isInAttackList: z.boolean().optional(),
  limit: z.number().min(1).max(500).default(100),
  offset: z.number().min(0).default(0),
});

const MessageListUpdateSchema = z.object({
  priority: z.number().min(1).max(5).optional(),
  scheduledTime: z.string().datetime().optional(),
  messageContent: z.string().min(1).max(2000).optional(),
  status: z.enum(['PENDING', 'PROCESSING', 'SENT', 'FAILED']).optional(),
});

async function getOrganizationId(req: NextRequest): Promise<{ organizationId: string; error?: NextResponse }> {
  // Check for API key first (external access)
  const apiKey = req.headers.get('X-API-Key');
  
  if (apiKey) {
    const result = await verifyApiKey(apiKey);
    if (!result.success) {
      return {
        organizationId: '',
        error: NextResponse.json(
          { success: false, error: result.errorMessage },
          { status: 401, headers: getCorsHeaders() }
        )
      };
    }
    return { organizationId: result.organizationId };
  }

  // Session authentication (dashboard)
  const session = await auth();
  if (!session?.user?.id) {
    return {
      organizationId: '',
      error: NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401, headers: getCorsHeaders() }
      )
    };
  }

  // Get user's organization
  const membership = await prisma.membership.findFirst({
    where: { userId: session.user.id }
  });

  if (!membership) {
    return {
      organizationId: '',
      error: NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404, headers: getCorsHeaders() }
      )
    };
  }

  return { organizationId: membership.organizationId };
}

/**
 * GET /api/message-list - Get all planned messages with filtering
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    const { organizationId, error } = await getOrganizationId(req);
    if (error) return error;

    const { searchParams } = new URL(req.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    // Parse query parameters
    const parsedParams = MessageListQuerySchema.parse({
      ...queryParams,
      limit: queryParams.limit ? parseInt(queryParams.limit) : 100,
      offset: queryParams.offset ? parseInt(queryParams.offset) : 0,
      isInAttackList: queryParams.isInAttackList === 'true' ? true : queryParams.isInAttackList === 'false' ? false : undefined,
    });

    // Build where clause
    const where: any = {
      organizationId,
    };

    if (parsedParams.status) where.status = parsedParams.status;
    if (parsedParams.handlerType) where.handlerType = parsedParams.handlerType;
    if (parsedParams.messageType) where.messageType = parsedParams.messageType;
    if (parsedParams.isInAttackList !== undefined) {
      // Check if message exists in AttackList
      if (parsedParams.isInAttackList) {
        where.AttackList = { some: {} };
      } else {
        where.AttackList = { none: {} };
      }
    }

    // Get total count for pagination
    const totalCount = await prisma.messageList.count({ where });

    // Get messages with pagination
    const messages = await prisma.messageList.findMany({
      where,
      include: {
        InstagramContact: {
          select: {
            instagramNickname: true,
            avatar: true,
            stage: true,
          }
        },
        AttackList: {
          select: {
            id: true,
            status: true,
            addedAt: true,
          }
        }
      },
      orderBy: [
        { scheduledTime: 'asc' },
        { priority: 'desc' },
        { createdAt: 'asc' }
      ],
      skip: parsedParams.offset,
      take: parsedParams.limit,
    });

    // Get metadata
    const statusCounts = await prisma.messageList.groupBy({
      by: ['status'],
      where: { organizationId },
      _count: { _all: true },
    });

    const handlerCounts = await prisma.messageList.groupBy({
      by: ['handlerType'],
      where: { organizationId },
      _count: { _all: true },
    });

    // Count messages in AttackList
    const inAttackListCount = await prisma.messageList.count({
      where: {
        organizationId,
        AttackList: { some: {} }
      }
    });

    const response = {
      success: true,
      data: {
        messages: messages.map(message => ({
          id: message.id,
          contactId: message.contactId,
          contactUsername: message.InstagramContact.instagramNickname,
          contactAvatar: message.InstagramContact.avatar,
          contactStage: message.InstagramContact.stage,
          priority: message.priority,
          messageType: message.messageType,
          message: message.messageContent, // Map messageContent to message for dashboard compatibility
          messageContent: message.messageContent,
          scheduledTime: message.scheduledTime,
          status: message.status,
          handlerType: message.handlerType,
          sequenceNumber: message.sequenceNumber,
          isInAttackList: message.AttackList.length > 0,
          attackListStatus: message.AttackList[0]?.status || null,
          attackListAddedAt: message.AttackList[0]?.addedAt || null,
          createdAt: message.createdAt,
          updatedAt: message.updatedAt,
        })),
      },
      pagination: {
        total: totalCount,
        limit: parsedParams.limit,
        offset: parsedParams.offset,
        hasMore: parsedParams.offset + parsedParams.limit < totalCount,
      },
      metadata: {
        totalMessages: totalCount,
        statusBreakdown: statusCounts.reduce((acc, item) => {
          acc[item.status.toLowerCase()] = item._count._all;
          return acc;
        }, {} as Record<string, number>),
        handlerBreakdown: handlerCounts.reduce((acc, item) => {
          acc[item.handlerType.toLowerCase()] = item._count._all;
          return acc;
        }, {} as Record<string, number>),
        inAttackList: inAttackListCount,
        pendingToday: await prisma.messageList.count({
          where: {
            organizationId,
            status: 'PENDING',
            scheduledTime: {
              gte: new Date(new Date().setHours(0, 0, 0, 0)),
              lt: new Date(new Date().setHours(23, 59, 59, 999))
            }
          }
        })
      }
    };

    return NextResponse.json(response, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error fetching message list:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid query parameters', details: error.errors },
        { status: 400, headers: getCorsHeaders() }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}

/**
 * PATCH /api/message-list - Update single message (for dashboard)
 */
export async function PATCH(req: NextRequest): Promise<Response> {
  try {
    const { organizationId, error } = await getOrganizationId(req);
    if (error) return error;

    const body = await req.json();
    const { messageId, message, scheduledTime } = body;

    if (!messageId) {
      return NextResponse.json(
        { success: false, error: 'messageId is required' },
        { status: 400, headers: getCorsHeaders() }
      );
    }

    const updateData: any = {
      updatedAt: new Date()
    };

    if (message) updateData.messageContent = message;
    if (scheduledTime) updateData.scheduledTime = new Date(scheduledTime);

    // Update message
    const result = await prisma.messageList.update({
      where: {
        id: messageId,
        organizationId
      },
      data: updateData
    });

    return NextResponse.json({
      success: true,
      data: result
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error updating message:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}

/**
 * PUT /api/message-list - Batch update messages
 */
export async function PUT(req: NextRequest): Promise<Response> {
  try {
    const { organizationId, error } = await getOrganizationId(req);
    if (error) return error;

    const body = await req.json();
    const { messageIds, updates } = body;

    if (!messageIds || !Array.isArray(messageIds) || messageIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'messageIds array is required' },
        { status: 400, headers: getCorsHeaders() }
      );
    }

    if (!updates || Object.keys(updates).length === 0) {
      return NextResponse.json(
        { success: false, error: 'updates object is required' },
        { status: 400, headers: getCorsHeaders() }
      );
    }

    const validatedUpdates = MessageListUpdateSchema.parse(updates);

    // Prepare update data
    const updateData: any = {
      updatedAt: new Date()
    };

    if (validatedUpdates.priority) updateData.priority = validatedUpdates.priority;
    if (validatedUpdates.scheduledTime) updateData.scheduledTime = new Date(validatedUpdates.scheduledTime);
    if (validatedUpdates.messageContent) updateData.messageContent = validatedUpdates.messageContent;
    if (validatedUpdates.status) updateData.status = validatedUpdates.status;

    // Update messages
    const result = await prisma.messageList.updateMany({
      where: {
        id: { in: messageIds },
        organizationId
      },
      data: updateData
    });

    // Check if any messages need AttackList sync
    const attackListSyncRequired = Boolean(
      validatedUpdates.priority || 
      validatedUpdates.scheduledTime || 
      validatedUpdates.messageContent
    );

    return NextResponse.json({
      success: true,
      data: {
        updatedCount: result.count,
        updated: Object.keys(validatedUpdates),
        attackListSyncRequired
      }
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error updating message list:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid update data', details: error.errors },
        { status: 400, headers: getCorsHeaders() }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}

/**
 * DELETE /api/message-list - Cancel/remove messages
 */
export async function DELETE(req: NextRequest): Promise<Response> {
  try {
    const { organizationId, error } = await getOrganizationId(req);
    if (error) return error;

    const body = await req.json();
    const { messageIds, messageId } = body;

    // Support both single messageId and messageIds array for compatibility
    let idsToDelete: string[];
    if (messageIds && Array.isArray(messageIds)) {
      idsToDelete = messageIds;
    } else if (messageId) {
      idsToDelete = [messageId];
    } else {
      return NextResponse.json(
        { success: false, error: 'messageId or messageIds array is required' },
        { status: 400, headers: getCorsHeaders() }
      );
    }

    const result = await prisma.$transaction(async (tx) => {
      // Remove from AttackList first
      const attackListDeleted = await tx.attackList.deleteMany({
        where: {
          messageListId: { in: idsToDelete },
          organizationId
        }
      });

      // Delete from MessageList
      const messageListDeleted = await tx.messageList.deleteMany({
        where: {
          id: { in: idsToDelete },
          organizationId
        }
      });

      return {
        messagesDeleted: messageListDeleted.count,
        removedFromAttackList: attackListDeleted.count
      };
    });

    return NextResponse.json({
      success: true,
      data: result
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error deleting messages:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}