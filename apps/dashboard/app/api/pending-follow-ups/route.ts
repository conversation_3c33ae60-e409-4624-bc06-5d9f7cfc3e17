import { NextRequest, NextResponse } from 'next/server';
import { verifyApiKey } from '@workspace/api-keys';
import { prisma } from '@workspace/database/client';
import { isWithin24HourWindow } from '@workspace/instagram-bot';

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

// Handle preflight OPTIONS requests
export async function OPTIONS(req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

/**
 * Get pending follow-ups for external handling
 * This endpoint is used by the Chrome extension to fetch follow-up messages
 * that are outside the 24-hour window and need external handling
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Get API key from header
    const apiKey = req.headers.get('X-API-Key');

    if (!apiKey) {
      return NextResponse.json(
        { success: false, message: 'API key is missing' },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    // Verify the API key
    const result = await verifyApiKey(apiKey);

    if (!result.success) {
      return NextResponse.json(
        { success: false, message: result.errorMessage },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    const organizationId = result.organizationId;

    // Get current time
    const now = new Date();

    // Get follow-ups that should be handled externally (only pending status)
    const newFollowUps = await prisma.messageList.findMany({
      where: {
        status: 'PENDING',
        organizationId: organizationId,
        InstagramContact: {
          isIgnored: false
        }
      },
      include: {
        InstagramContact: {
          select: {
            instagramId: true,
            instagramNickname: true,
            lastInteractionAt: true
          }
        }
      },
      orderBy: [
        { scheduledTime: 'asc' },
        { sequenceNumber: 'asc' }
      ]
    });

    // Legacy follow-up fields were migrated to MessageList table
    const legacyContactsWithFollowUps: any[] = [];

    // Format the follow-ups
    const pendingFollowUps = [];

    // Add MessageList follow-ups (only pending status that are outside 24h window)
    for (const followUp of newFollowUps) {
      // Only include pending follow-ups that are outside 24h window
      const isWithinWindow = isWithin24HourWindow(
        followUp.InstagramContact.lastInteractionAt,
        followUp.scheduledTime
      );

      if (!isWithinWindow) { // Include if outside 24h window
        pendingFollowUps.push({
          id: followUp.id,
          contactId: followUp.contactId,
          recipientId: followUp.InstagramContact.instagramId,
          username: followUp.InstagramContact.instagramNickname,
          message: followUp.messageContent,
          scheduledTime: followUp.scheduledTime,
          followUpNumber: followUp.sequenceNumber || 1
        });
      }
    }

    // Legacy follow-up fields were migrated to MessageList table
    // No legacy processing needed

    // Sort all follow-ups by scheduled time
    pendingFollowUps.sort((a, b) => new Date(a.scheduledTime).getTime() - new Date(b.scheduledTime).getTime());

    return NextResponse.json({
      success: true,
      data: pendingFollowUps
    }, {
      headers: getCorsHeaders()
    });
  } catch (error) {
    console.error('Error fetching pending follow-ups:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}
