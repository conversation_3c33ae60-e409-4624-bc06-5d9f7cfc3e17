import { NextRequest, NextResponse } from 'next/server';
import { auth } from '~/auth';
import { prisma } from '@workspace/database/client';

/**
 * Helper function to get organization from referer
 */
async function getOrganizationFromRequest(req: NextRequest) {
  const session = await auth();
  if (!session?.user?.id) {
    throw new Error('Unauthorized');
  }

  // Get the organization slug from the referer URL
  const referer = req.headers.get('referer') || '';
  const match = referer.match(/\/organizations\/([^/]+)\//i);
  const orgSlug = match ? match[1] : null;

  if (!orgSlug) {
    throw new Error('Organization not found');
  }

  // Get the organization ID from the slug
  const organization = await prisma.organization.findFirst({
    where: { slug: orgSlug }
  });

  if (!organization) {
    throw new Error('Organization not found');
  }

  // Check if the user has access to the organization
  const userMembership = await prisma.membership.findFirst({
    where: {
      organizationId: organization.id,
      userId: session.user.id,
    },
  });

  if (!userMembership) {
    throw new Error('Unauthorized');
  }

  return { organization, session };
}

/**
 * Manual cleanup of old follow-ups for a specific organization
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    const { organization } = await getOrganizationFromRequest(req);
    const { cleanupDays } = await req.json();

    // Validate cleanup days
    if (!cleanupDays || typeof cleanupDays !== 'number' || cleanupDays < 1 || cleanupDays > 365) {
      return NextResponse.json(
        { success: false, error: 'Cleanup days must be between 1 and 365' },
        { status: 400 }
      );
    }

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - cleanupDays);

    console.log(`Manual cleanup for ${organization.name}: ${cleanupDays} days, cutoff: ${cutoffDate.toISOString()}`);

    // Clean up old messages (updated to use MessageList)
    // First get contacts for this organization
    const orgContacts = await prisma.instagramContact.findMany({
      where: { organizationId: organization.id },
      select: { id: true }
    });
    const contactIds = orgContacts.map(c => c.id);

    const deletedMessages = await prisma.messageList.deleteMany({
      where: {
        createdAt: {
          lt: cutoffDate
        },
        status: {
          in: ['SENT', 'FAILED']
        },
        contactId: {
          in: contactIds
        }
      }
    });

    // Legacy follow-up fields no longer exist - skip cleanup
    const clearedLegacyFollowUps = 0;

    const totalCleaned = deletedMessages.count + clearedLegacyFollowUps;

    console.log(`Manual cleanup completed for ${organization.name}:
    - Deleted ${deletedMessages.count} messages
    - Cleared ${clearedLegacyFollowUps} legacy follow-ups (none - deprecated)
    - Total cleaned: ${totalCleaned}
    - Cutoff date: ${cutoffDate.toISOString()}
    - Cleanup days: ${cleanupDays}`);

    return NextResponse.json({
      success: true,
      cleaned: {
        messages: deletedMessages.count,
        legacyFollowUps: clearedLegacyFollowUps,
        total: totalCleaned
      },
      cutoffDate: cutoffDate.toISOString(),
      cleanupDays,
      organizationName: organization.name
    });

  } catch (error) {
    console.error('Error in manual cleanup:', error);
    if (error instanceof Error) {
      if (error.message === 'Unauthorized') {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401 }
        );
      }
      if (error.message === 'Organization not found') {
        return NextResponse.json(
          { success: false, error: 'Organization not found' },
          { status: 404 }
        );
      }
    }
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
