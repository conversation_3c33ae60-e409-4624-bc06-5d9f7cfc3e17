import { NextRequest, NextResponse } from 'next/server';

import { prisma } from '@workspace/database/client';

export async function GET(req: NextRequest) {
  try {
    console.log('API: Fetching Instagram contacts');

    // Get the organization slug from the URL query or referer
    const url = new URL(req.url);
    let orgSlug = url.searchParams.get('organizationId');

    if (!orgSlug) {
      const referer = req.headers.get('referer') || '';
      const match = referer.match(/\/organizations\/([^/]+)\//i);
      orgSlug = match ? match[1] : null;
    }

    if (!orgSlug) {
      console.error('API: No organization slug found');
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    console.log('API: Looking for organization with slug:', orgSlug);

    // Get the organization ID from the slug
    const organization = await prisma.organization.findFirst({
      where: { slug: orgSlug }
    });

    if (!organization) {
      console.error('API: Organization not found for slug:', orgSlug);
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    console.log('API: Found organization:', organization.id);

    // Get all Instagram contacts for the organization
    console.log('API: Querying contacts for organization ID:', organization.id);
    const contacts = await prisma.instagramContact.findMany({
      where: {
        organizationId: organization.id
      },
      orderBy: {
        updatedAt: 'desc'
      }
    });

    console.log(`API: Found ${contacts.length} raw contacts`);

    // Transform contacts - removed legacy follow-up processing since fields no longer exist
    const transformedContacts = contacts.map(contact => {
      return {
        ...contact,
        instagramUsername: contact.instagramNickname || 'Unknown User',
        followUps: [] // Empty for now - follow-ups are now in MessageList table
      };
    });

    console.log(`API: Returning ${transformedContacts.length} contacts`);
    return NextResponse.json(transformedContacts);
  } catch (error) {
    console.error('API: Error fetching Instagram contacts:', error);

    // Provide more detailed error information
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : undefined;

    console.error('API: Error details:', {
      message: errorMessage,
      stack: errorStack
    });

    return NextResponse.json({
      error: 'Failed to fetch Instagram contacts',
      details: errorMessage
    }, { status: 500 });
  }
}
