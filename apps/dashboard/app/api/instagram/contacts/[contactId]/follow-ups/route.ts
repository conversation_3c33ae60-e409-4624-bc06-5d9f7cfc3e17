import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';

export async function GET(
  req: NextRequest,
  { params }: { params: { contactId: string } }
) {
  try {
    // Get session
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { contactId } = await Promise.resolve(params);

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: { userId: session.user.id }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    // Verify contact belongs to organization
    const contact = await prisma.instagramContact.findFirst({
      where: {
        id: contactId,
        organizationId: membership.organizationId
      }
    });

    if (!contact) {
      return NextResponse.json(
        { success: false, error: 'Contact not found' },
        { status: 404 }
      );
    }

    // Get follow-ups from MessageList table
    const followUps = await prisma.messageList.findMany({
      where: {
        contactId: contactId,
        organizationId: membership.organizationId,
        status: { in: ['PENDING', 'SENT', 'FAILED'] }
      },
      orderBy: [
        { scheduledTime: 'asc' },
        { sequenceNumber: 'asc' }
      ]
    });

    // Transform to expected format
    const transformedFollowUps = followUps.map(followUp => ({
      id: followUp.id,
      contactId: followUp.contactId,
      contactUsername: contact.instagramNickname,
      contactAvatar: contact.avatar,
      sequenceNumber: followUp.sequenceNumber,
      message: followUp.messageContent,
      scheduledTime: followUp.scheduledTime,
      status: followUp.status.toLowerCase(),
      sentAt: followUp.sentAt,
      createdAt: followUp.createdAt,
      updatedAt: followUp.updatedAt,
      type: 'new' as const
    }));

    return NextResponse.json({
      success: true,
      data: transformedFollowUps
    });

  } catch (error) {
    console.error('Error fetching contact follow-ups:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}