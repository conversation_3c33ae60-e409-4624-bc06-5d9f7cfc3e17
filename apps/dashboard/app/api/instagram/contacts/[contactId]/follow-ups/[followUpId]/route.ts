import { NextRequest, NextResponse } from 'next/server';

import { prisma } from '@workspace/database/client';

export async function PATCH(
  req: NextRequest,
  { params }: { params: { contactId: string; followUpId: string } }
) {
  try {
    const { contactId, followUpId } = await Promise.resolve(params);

    // Get the organization slug from the referer URL
    const referer = req.headers.get('referer') || '';
    const match = referer.match(/\/organizations\/([^/]+)\//i);
    const orgSlug = match ? match[1] : null;

    if (!orgSlug) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    // Get the organization ID from the slug
    const organization = await prisma.organization.findFirst({
      where: { slug: orgSlug }
    });

    if (!organization) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    // Check if contact exists and belongs to the organization
    const contact = await prisma.instagramContact.findFirst({
      where: {
        id: contactId,
        organizationId: organization.id
      }
    });

    if (!contact) {
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 });
    }

    // Find the MessageList entry
    const messageListEntry = await prisma.messageList.findFirst({
      where: {
        id: followUpId,
        contactId: contactId,
        organizationId: organization.id
      }
    });

    if (!messageListEntry) {
      return NextResponse.json({ error: 'Follow-up not found' }, { status: 404 });
    }

    const { message, delayHours } = await req.json();

    if (!message || typeof message !== 'string') {
      return NextResponse.json({ error: 'Message is required' }, { status: 400 });
    }

    if (!delayHours || typeof delayHours !== 'number' || delayHours < 1) {
      return NextResponse.json({ error: 'Valid delay hours are required' }, { status: 400 });
    }

    // Calculate the new time based on the current time and delay
    const newTime = new Date();
    newTime.setHours(newTime.getHours() + delayHours);

    // Update the MessageList entry
    await prisma.messageList.update({
      where: { id: followUpId },
      data: {
        messageContent: message,
        scheduledTime: newTime,
        status: messageListEntry.status === 'SENT' ? 'SENT' : 'PENDING', // Keep SENT status
        updatedAt: new Date()
      }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error updating follow-up:', error);
    return NextResponse.json({ error: 'Failed to update follow-up' }, { status: 500 });
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: { contactId: string; followUpId: string } }
) {
  try {
    const { contactId, followUpId } = await Promise.resolve(params);

    // Get the organization slug from the referer URL
    const referer = req.headers.get('referer') || '';
    const match = referer.match(/\/organizations\/([^/]+)\//i);
    const orgSlug = match ? match[1] : null;

    if (!orgSlug) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    // Get the organization ID from the slug
    const organization = await prisma.organization.findFirst({
      where: { slug: orgSlug }
    });

    if (!organization) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    // Check if contact exists and belongs to the organization
    const contact = await prisma.instagramContact.findFirst({
      where: {
        id: contactId,
        organizationId: organization.id
      }
    });

    if (!contact) {
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 });
    }

    // Find and delete the MessageList entry
    const messageListEntry = await prisma.messageList.findFirst({
      where: {
        id: followUpId,
        contactId: contactId,
        organizationId: organization.id
      }
    });

    if (!messageListEntry) {
      return NextResponse.json({ error: 'Follow-up not found' }, { status: 404 });
    }

    // Delete from both AttackList and MessageList using transaction
    await prisma.$transaction(async (tx) => {
      // Remove from AttackList first (if exists)
      await tx.attackList.deleteMany({
        where: {
          messageListId: followUpId,
          organizationId: organization.id
        }
      });

      // Delete from MessageList
      await tx.messageList.delete({
        where: { id: followUpId }
      });
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting follow-up:', error);
    return NextResponse.json({ error: 'Failed to delete follow-up' }, { status: 500 });
  }
}
