import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';
import { manualGatherConversations } from '~/lib/instagram-conversation-gathering';

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get organization ID from request
    const body = await req.json();
    const { organizationId } = body;

    if (!organizationId) {
      return NextResponse.json(
        { success: false, error: 'Organization ID required' },
        { status: 400 }
      );
    }

    // Verify user has access to this organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id,
        organizationId,
        role: 'ADMIN'
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'Access denied to this organization' },
        { status: 403 }
      );
    }

    // Check if Instagram is connected
    const settings = await prisma.instagramSettings.findUnique({
      where: { organizationId },
      select: { 
        instagramToken: true,
        instagramUsername: true,
        isConnected: true
      }
    });

    if (!settings?.isConnected || !settings?.instagramToken) {
      return NextResponse.json(
        { success: false, error: 'Instagram account not connected' },
        { status: 400 }
      );
    }

    console.log(`📥 Manual conversation gathering triggered for ${settings.instagramUsername}`);

    // Trigger manual gathering
    const result = await manualGatherConversations(organizationId);

    return NextResponse.json(result);

  } catch (error) {
    console.error('Error in manual conversation gathering:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

// GET endpoint to check gathering status
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const organizationId = searchParams.get('organizationId');

    if (!organizationId) {
      return NextResponse.json(
        { success: false, error: 'Organization ID required' },
        { status: 400 }
      );
    }

    // Verify user has access
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id,
        organizationId
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    // Get gathering status
    const [conversationCount, lastGathered] = await Promise.all([
      prisma.instagramConversationsNotGathered.count({
        where: { organizationId }
      }),
      prisma.instagramConversationsNotGathered.findFirst({
        where: { organizationId },
        orderBy: { createdAt: 'desc' },
        select: { createdAt: true }
      })
    ]);

    // Get some stats
    const [gatheredCount, pendingCount] = await Promise.all([
      prisma.instagramConversationsNotGathered.count({
        where: { organizationId, isGathered: true }
      }),
      prisma.instagramConversationsNotGathered.count({
        where: { organizationId, isGathered: false }
      })
    ]);

    return NextResponse.json({
      success: true,
      status: {
        hasGathered: conversationCount > 0,
        totalConversations: conversationCount,
        gatheredCount,
        pendingCount,
        lastGatheredAt: lastGathered?.createdAt,
        percentProcessed: conversationCount > 0 
          ? Math.round((gatheredCount / conversationCount) * 100)
          : 0
      }
    });

  } catch (error) {
    console.error('Error checking gathering status:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error' 
      },
      { status: 500 }
    );
  }
}