import { NextRequest, NextResponse } from 'next/server';

import { auth } from '~/auth';
import { prisma } from '@workspace/database/client';
import { isWithin24HourWindow, getLatest24HourTime } from '@workspace/instagram-bot';

/**
 * Helper function to get organization from referer
 */
async function getOrganizationFromRequest(req: NextRequest) {
  const session = await auth();
  if (!session?.user?.id) {
    throw new Error('Unauthorized');
  }

  // Get the organization slug from the referer URL
  const referer = req.headers.get('referer') || '';
  const match = referer.match(/\/organizations\/([^/]+)\//i);
  const orgSlug = match ? match[1] : null;

  if (!orgSlug) {
    throw new Error('Organization not found');
  }

  // Get the organization ID from the slug
  const organization = await prisma.organization.findFirst({
    where: { slug: orgSlug }
  });

  if (!organization) {
    throw new Error('Organization not found');
  }

  // Check if the user has access to the organization
  const userMembership = await prisma.membership.findFirst({
    where: {
      organizationId: organization.id,
      userId: session.user.id,
    },
  });

  if (!userMembership) {
    throw new Error('Unauthorized');
  }

  return { organization, session };
}

/**
 * Get all follow-ups for the organization
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    const { organization } = await getOrganizationFromRequest(req);

    // Get follow-ups from MessageList table
    const newFollowUps = await prisma.messageList.findMany({
      where: {
        status: { in: ['PENDING', 'SENT', 'FAILED'] },
        organizationId: organization.id
      },
      include: {
        InstagramContact: {
          select: {
            instagramNickname: true,
            avatar: true,
            lastInteractionAt: true
          }
        }
      },
      orderBy: [
        { scheduledTime: 'asc' },
        { sequenceNumber: 'asc' }
      ]
    });

    // Transform MessageList follow-ups with 24-hour window information
    const transformedNewFollowUps = newFollowUps.map(followUp => {
      const contact = followUp.InstagramContact;
      const isWithinWindow = isWithin24HourWindow(contact.lastInteractionAt, followUp.scheduledTime);
      const latest24HourTime = getLatest24HourTime(contact.lastInteractionAt);

      return {
        id: followUp.id,
        contactId: followUp.contactId,
        contactUsername: contact.instagramNickname,
        contactAvatar: contact.avatar,
        sequenceNumber: followUp.sequenceNumber || 1,
        message: followUp.messageContent,
        scheduledTime: followUp.scheduledTime,
        status: followUp.status.toLowerCase(),
        sentAt: followUp.sentAt,
        createdAt: followUp.createdAt,
        updatedAt: followUp.updatedAt,
        type: 'new',
        isWithin24HourWindow: isWithinWindow,
        latest24HourTime: latest24HourTime?.toISOString() || null
      };
    });

    // Legacy follow-ups were migrated to MessageList table
    const transformedLegacyFollowUps: any[] = [];

    const response = NextResponse.json({
      success: true,
      data: {
        newFollowUps: transformedNewFollowUps,
        legacyFollowUps: transformedLegacyFollowUps
      }
    });

    // Add cache headers to prevent browser caching
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');

    return response;
  } catch (error) {
    console.error('Error fetching follow-ups:', error);
    if (error instanceof Error) {
      if (error.message === 'Unauthorized') {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401 }
        );
      }
      if (error.message === 'Organization not found') {
        return NextResponse.json(
          { success: false, error: 'Organization not found' },
          { status: 404 }
        );
      }
    }
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Update a follow-up
 */
export async function PATCH(req: NextRequest): Promise<Response> {
  try {
    const { followUpId, message, scheduledTime, delayHours } = await req.json();

    if (!followUpId || !message || (!scheduledTime && !delayHours)) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const { organization } = await getOrganizationFromRequest(req);

    // Check if it's a new follow-up (UUID) or legacy follow-up (contactId_fuN)
    if (followUpId.includes('_fu')) {
      // Legacy follow-up
      const [contactId, followUpType] = followUpId.split('_');
      const followUpNumber = parseInt(followUpType.replace('fu', ''));

      if (isNaN(followUpNumber) || followUpNumber < 1 || followUpNumber > 4) {
        return NextResponse.json(
          { success: false, error: 'Invalid follow-up ID' },
          { status: 400 }
        );
      }

      // Check if contact exists and belongs to the organization
      const contact = await prisma.instagramContact.findFirst({
        where: {
          id: contactId,
          User: {
            memberships: {
              some: {
                organizationId: organization.id
              }
            }
          }
        },
        select: {
          id: true,
          lastInteractionAt: true
        }
      });

      if (!contact) {
        return NextResponse.json(
          { success: false, error: 'Contact not found' },
          { status: 404 }
        );
      }

      // Calculate the new time based on scheduledTime or delayHours
      let newTime: Date;
      if (scheduledTime) {
        newTime = new Date(scheduledTime);
      } else {
        newTime = new Date();
        newTime.setHours(newTime.getHours() + delayHours);
      }

      // Check if the follow-up is within the 24-hour window
      const isWithinWindow = isWithin24HourWindow(contact.lastInteractionAt, newTime);
      const latest24HourTime = getLatest24HourTime(contact.lastInteractionAt);

      if (!isWithinWindow && latest24HourTime) {
        return NextResponse.json({
          success: false,
          error: `Follow-up scheduled outside 24-hour window. Latest allowed time: ${latest24HourTime.toISOString()}`,
          latest24HourTime: latest24HourTime.toISOString()
        }, { status: 400 });
      }

      // Update the follow-up
      const updateData: Record<string, unknown> = {};
      updateData[`followUpMessage${followUpNumber}`] = message;
      updateData[`followUpTime${followUpNumber}`] = newTime;

      // Only update status if it's not already sent
      const statusField = `followUpStatus${followUpNumber}`;
      if (contact[statusField as keyof typeof contact] !== 'sent') {
        updateData[statusField] = 'pending';
      }

      await prisma.instagramContact.update({
        where: { id: contactId },
        data: updateData
      });
    } else {
      // MessageList follow-up
      const followUp = await prisma.messageList.findFirst({
        where: {
          id: followUpId,
          organizationId: organization.id
        },
        include: {
          InstagramContact: {
            select: {
              id: true,
              lastInteractionAt: true
            }
          }
        }
      });

      if (!followUp) {
        return NextResponse.json(
          { success: false, error: 'Follow-up not found' },
          { status: 404 }
        );
      }

      // Calculate the new time based on scheduledTime or delayHours
      let newTime: Date;
      if (scheduledTime) {
        newTime = new Date(scheduledTime);
      } else {
        newTime = new Date();
        newTime.setHours(newTime.getHours() + delayHours);
      }

      // Check if the follow-up is within the 24-hour window
      const isWithinWindow = isWithin24HourWindow(followUp.InstagramContact.lastInteractionAt, newTime);
      const latest24HourTime = getLatest24HourTime(followUp.InstagramContact.lastInteractionAt);

      if (!isWithinWindow && latest24HourTime) {
        return NextResponse.json({
          success: false,
          error: `Follow-up scheduled outside 24-hour window. Latest allowed time: ${latest24HourTime.toISOString()}`,
          latest24HourTime: latest24HourTime.toISOString()
        }, { status: 400 });
      }

      await prisma.messageList.update({
        where: { id: followUpId },
        data: {
          messageContent: message,
          scheduledTime: newTime,
          status: followUp.status === 'SENT' ? 'SENT' : 'PENDING'
        }
      });
    }

    // Cache invalidation removed - Instagram contacts no longer cached

    return NextResponse.json({
      success: true,
      message: 'Follow-up updated successfully'
    });
  } catch (error) {
    console.error('Error updating follow-up:', error);
    if (error instanceof Error) {
      if (error.message === 'Unauthorized') {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401 }
        );
      }
      if (error.message === 'Organization not found') {
        return NextResponse.json(
          { success: false, error: 'Organization not found' },
          { status: 404 }
        );
      }
    }
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Delete a follow-up
 */
export async function DELETE(req: NextRequest): Promise<Response> {
  try {
    const { followUpId } = await req.json();

    if (!followUpId) {
      return NextResponse.json(
        { success: false, error: 'Follow-up ID is required' },
        { status: 400 }
      );
    }

    const { organization } = await getOrganizationFromRequest(req);

    // Check if it's a new follow-up (UUID) or legacy follow-up (contactId_fuN)
    if (followUpId.includes('_fu')) {
      // Legacy follow-up
      const [contactId, followUpType] = followUpId.split('_');
      const followUpNumber = parseInt(followUpType.replace('fu', ''));

      if (isNaN(followUpNumber) || followUpNumber < 1 || followUpNumber > 4) {
        return NextResponse.json(
          { success: false, error: 'Invalid follow-up ID' },
          { status: 400 }
        );
      }

      // Check if contact exists and belongs to the organization
      const contact = await prisma.instagramContact.findFirst({
        where: {
          id: contactId,
          User: {
            memberships: {
              some: {
                organizationId: organization.id
              }
            }
          }
        }
      });

      if (!contact) {
        return NextResponse.json(
          { success: false, error: 'Contact not found' },
          { status: 404 }
        );
      }

      // Clear the follow-up fields
      const updateData: Record<string, unknown> = {};
      updateData[`followUpMessage${followUpNumber}`] = null;
      updateData[`followUpTime${followUpNumber}`] = null;
      updateData[`followUpStatus${followUpNumber}`] = null;

      await prisma.instagramContact.update({
        where: { id: contactId },
        data: updateData
      });
    } else {
      // MessageList follow-up
      const followUp = await prisma.messageList.findFirst({
        where: {
          id: followUpId,
          organizationId: organization.id
        }
      });

      if (!followUp) {
        return NextResponse.json(
          { success: false, error: 'Follow-up not found' },
          { status: 404 }
        );
      }

      await prisma.messageList.delete({
        where: { id: followUpId }
      });
    }

    // Cache invalidation removed - Instagram contacts no longer cached

    return NextResponse.json({
      success: true,
      message: 'Follow-up deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting follow-up:', error);
    if (error instanceof Error) {
      if (error.message === 'Unauthorized') {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401 }
        );
      }
      if (error.message === 'Organization not found') {
        return NextResponse.json(
          { success: false, error: 'Organization not found' },
          { status: 404 }
        );
      }
    }
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
