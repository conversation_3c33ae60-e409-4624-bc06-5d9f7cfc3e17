import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@workspace/database/client';
import { auth } from '@workspace/auth';
import { triggerConversationGathering } from '~/lib/instagram-conversation-gathering';

// Instagram OAuth configuration
const INSTAGRAM_CLIENT_ID = process.env.INSTAGRAM_CLIENT_ID;
const INSTAGRAM_CLIENT_SECRET = process.env.INSTAGRAM_CLIENT_SECRET;
const INSTAGRAM_REDIRECT_URI = 'https://app.aisetter.pl/api/instagram/oauth/callback';

export async function GET(req: NextRequest) {
  try {
    // Get the authorization code from the URL
    const url = new URL(req.url);
    const code = url.searchParams.get('code');
    const error = url.searchParams.get('error');
    const state = url.searchParams.get('state'); // Organization slug should be in state

    // Helper function to create error HTML
    const createErrorHtml = (title: string, message: string) => `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${title}</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            background-color: #f8fafc;
          }
          .container {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            max-width: 400px;
          }
          .error-icon {
            font-size: 3rem;
            color: #ef4444;
            margin-bottom: 1rem;
          }
          h1 {
            color: #1f2937;
            margin-bottom: 0.5rem;
            font-size: 1.5rem;
          }
          p {
            color: #6b7280;
            margin-bottom: 2rem;
            line-height: 1.5;
          }
          .close-button {
            background-color: #6b7280;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-size: 0.875rem;
            cursor: pointer;
            transition: background-color 0.2s;
          }
          .close-button:hover {
            background-color: #4b5563;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="error-icon">❌</div>
          <h1>${title}</h1>
          <p>${message}</p>
          <button class="close-button" onclick="window.close()">Close Window</button>
        </div>
        <script>
          // Send message to parent window that connection failed
          try {
            if (window.opener) {
              window.opener.postMessage({ type: 'instagram-oauth-error' }, '*');
            }
          } catch (e) {
            console.log('Could not communicate with parent window');
          }
        </script>
      </body>
      </html>
    `;

    // Check for OAuth errors
    if (error) {
      console.error('Instagram OAuth error:', error);
      return new NextResponse(createErrorHtml('OAuth Error', 'Instagram authorization was cancelled or failed. Please try again.'), {
        status: 200,
        headers: { 'Content-Type': 'text/html' },
      });
    }

    if (!code) {
      console.error('No authorization code received');
      return new NextResponse(createErrorHtml('Authorization Failed', 'No authorization code was received from Instagram. Please try again.'), {
        status: 200,
        headers: { 'Content-Type': 'text/html' },
      });
    }

    if (!INSTAGRAM_CLIENT_SECRET) {
      console.error('Instagram client secret not configured');
      return new NextResponse(createErrorHtml('Configuration Error', 'Instagram client secret is not configured. Please contact support.'), {
        status: 200,
        headers: { 'Content-Type': 'text/html' },
      });
    }

    if (!INSTAGRAM_CLIENT_ID) {
      console.error('Instagram client ID not configured');
      return new NextResponse(createErrorHtml('Configuration Error', 'Instagram client ID is not configured. Please contact support.'), {
        status: 200,
        headers: { 'Content-Type': 'text/html' },
      });
    }

    // Get the current user session
    const session = await auth();
    if (!session?.user?.id) {
      console.error('No user session found');
      return new NextResponse(createErrorHtml('Authentication Required', 'Please sign in to connect your Instagram account.'), {
        status: 200,
        headers: { 'Content-Type': 'text/html' },
      });
    }

    // Exchange authorization code for access token
    const tokenResponse = await fetch('https://api.instagram.com/oauth/access_token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: INSTAGRAM_CLIENT_ID,
        client_secret: INSTAGRAM_CLIENT_SECRET,
        grant_type: 'authorization_code',
        redirect_uri: INSTAGRAM_REDIRECT_URI,
        code,
      }),
    });

    const tokenData = await tokenResponse.json();

    if (!tokenResponse.ok) {
      console.error('Failed to exchange code for token:', tokenData);
      return new NextResponse(createErrorHtml('Token Exchange Failed', 'Failed to exchange authorization code for access token. Please try again.'), {
        status: 200,
        headers: { 'Content-Type': 'text/html' },
      });
    }

    // Get long-lived access token
    const longLivedTokenResponse = await fetch(
      `https://graph.instagram.com/access_token?grant_type=ig_exchange_token&client_secret=${INSTAGRAM_CLIENT_SECRET}&access_token=${tokenData.access_token}`,
      { method: 'GET' }
    );

    const longLivedTokenData = await longLivedTokenResponse.json();

    if (!longLivedTokenResponse.ok) {
      console.error('Failed to get long-lived token:', longLivedTokenData);
      // Use short-lived token if long-lived fails
      console.log('Using short-lived token as fallback');
    }

    const finalAccessToken = longLivedTokenData.access_token || tokenData.access_token;
    const expiresIn = longLivedTokenData.expires_in || tokenData.expires_in;

    // Get Instagram user account info first
    const userInfoResponse = await fetch(
      `https://graph.instagram.com/me?fields=id,username,name,profile_picture_url,followers_count,media_count,account_type&access_token=${finalAccessToken}`,
      { method: 'GET' }
    );

    const userInfo = await userInfoResponse.json();

    if (!userInfoResponse.ok) {
      console.error('Failed to get user account info:', userInfo);
      return new NextResponse(createErrorHtml('Account Info Failed', 'Failed to retrieve Instagram account information. Please try again.'), {
        status: 200,
        headers: { 'Content-Type': 'text/html' },
      });
    }

    // CORRECT SOLUTION: Get the Instagram Professional Account ID that webhooks will use
    // According to Instagram API docs, the user_id field from /me endpoint is the exact ID webhooks send
    console.log(`🔍 GETTING WEBHOOK-COMPATIBLE ID for @${userInfo.username} (${userInfo.account_type})`);
    console.log(`   Profile User ID: ${userInfo.id}`);

    // Get the user_id field which is the Instagram professional account ID used in webhooks
    const webhookIdResponse = await fetch(
      `https://graph.instagram.com/v23.0/me?fields=user_id,username&access_token=${finalAccessToken}`,
      { method: 'GET' }
    );

    if (!webhookIdResponse.ok) {
      console.error('Failed to get webhook-compatible user_id:', await webhookIdResponse.text());
      return new NextResponse(createErrorHtml('Webhook ID Failed', 'Failed to retrieve Instagram webhook-compatible ID. Please try again.'), {
        status: 200,
        headers: { 'Content-Type': 'text/html' },
      });
    }

    const webhookIdData = await webhookIdResponse.json();
    const webhookCompatibleId = webhookIdData.user_id;

    console.log(`📋 WEBHOOK ID MAPPING for @${userInfo.username}:`);
    console.log(`   Profile ID (/me): ${userInfo.id}`);
    console.log(`   Webhook ID (/me?fields=user_id): ${webhookCompatibleId}`);
    console.log(`   Account Type: ${userInfo.account_type}`);
    console.log(`   🎯 Storing webhook ID for routing: ${webhookCompatibleId}`);
    
    if (webhookCompatibleId !== userInfo.id) {
      console.log(`   ✅ SUCCESS: Different webhook ID found - this will fix webhook routing!`);
    } else {
      console.log(`   ℹ️ Webhook ID same as profile ID - both will work for routing`);
    }

    // Set final IDs
    const businessAccountId = userInfo.id; // For general API calls

    // Find or get the organization
    if (!state) {
      console.error('No organization state provided');
      return new NextResponse(createErrorHtml('Invalid State', 'No organization information provided. Please try again.'), {
        status: 200,
        headers: { 'Content-Type': 'text/html' },
      });
    }
    
    const organization = await prisma.organization.findFirst({
      where: {
        slug: state,
        memberships: {
          some: {
            userId: session.user.id,
            role: { in: ['ADMIN'] }
          }
        }
      }
    });

    if (!organization) {
      console.error('Organization not found or user not authorized');
      return new NextResponse(createErrorHtml('Unauthorized', 'Organization not found or you do not have permission to connect Instagram for this organization.'), {
        status: 200,
        headers: { 'Content-Type': 'text/html' },
      });
    }

    // Calculate token expiration
    const tokenExpiresAt = expiresIn 
      ? new Date(Date.now() + (expiresIn * 1000))
      : new Date(Date.now() + (60 * 24 * 60 * 60 * 1000)); // 60 days default

    // Update or create Instagram settings with both OAuth ID and Webhook ID
    await prisma.instagramSettings.upsert({
      where: {
        organizationId: organization.id
      },
      update: {
        instagramToken: finalAccessToken,
        instagramAccountId: businessAccountId, // OAuth ID for API calls
        instagramWebhookId: webhookCompatibleId, // Webhook ID for routing
        instagramUsername: userInfo.username,
        instagramName: userInfo.name,
        instagramProfilePicture: userInfo.profile_picture_url,
        tokenExpiresAt,
        isConnected: true,
        updatedAt: new Date()
      },
      create: {
        organizationId: organization.id,
        instagramToken: finalAccessToken,
        instagramAccountId: businessAccountId, // OAuth ID for API calls
        instagramWebhookId: webhookCompatibleId, // Webhook ID for routing
        instagramUsername: userInfo.username,
        instagramName: userInfo.name,
        instagramProfilePicture: userInfo.profile_picture_url,
        tokenExpiresAt,
        isConnected: true,
        isBotEnabled: false, // Default to disabled until user enables
        minResponseTime: 30,
        maxResponseTime: 50,
        messageDelayMin: 3,
        messageDelayMax: 5,
        autoCleanupEnabled: true,
        followUpCleanupDays: 30
      }
    });

    console.log(`✅ Successfully connected Instagram account ${userInfo.username} for organization ${organization.slug}`);
    console.log(`   OAuth ID (for API calls): ${businessAccountId}`);
    console.log(`   Webhook ID (for routing): ${webhookCompatibleId}`);
    console.log(`   🎯 This will fix webhook routing issues!`);

    // Trigger blazing-fast conversation gathering in background
    console.log(`🚀 Triggering conversation gathering for ${userInfo.username}...`);
    triggerConversationGathering(finalAccessToken, organization.id);

    // Return success HTML page instead of redirecting
    const successHtml = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Instagram Connected Successfully</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            background-color: #f8fafc;
          }
          .container {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            max-width: 400px;
          }
          .success-icon {
            font-size: 3rem;
            color: #10b981;
            margin-bottom: 1rem;
          }
          h1 {
            color: #1f2937;
            margin-bottom: 0.5rem;
            font-size: 1.5rem;
          }
          p {
            color: #6b7280;
            margin-bottom: 2rem;
            line-height: 1.5;
          }
          .close-button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-size: 0.875rem;
            cursor: pointer;
            transition: background-color 0.2s;
          }
          .close-button:hover {
            background-color: #2563eb;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="success-icon">✅</div>
          <h1>Success!</h1>
          <p>Your Instagram account <strong>@${userInfo.username}</strong> has been connected successfully.</p>
          <p>You can now close this popup!</p>
          <button class="close-button" onclick="window.close()">Close Window</button>
        </div>
        <script>
          // Try to close the window automatically after 3 seconds
          setTimeout(() => {
            window.close();
          }, 3000);
          
          // Send message to parent window that connection was successful
          try {
            if (window.opener) {
              window.opener.postMessage({ type: 'instagram-oauth-success' }, '*');
            }
          } catch (e) {
            console.log('Could not communicate with parent window');
          }
        </script>
      </body>
      </html>
    `;

    return new NextResponse(successHtml, {
      status: 200,
      headers: {
        'Content-Type': 'text/html',
      },
    });

  } catch (error) {
    console.error('Instagram OAuth callback error:', error);
    
    // Return error HTML page instead of redirecting
    const errorHtml = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Instagram Connection Failed</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            background-color: #f8fafc;
          }
          .container {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            max-width: 400px;
          }
          .error-icon {
            font-size: 3rem;
            color: #ef4444;
            margin-bottom: 1rem;
          }
          h1 {
            color: #1f2937;
            margin-bottom: 0.5rem;
            font-size: 1.5rem;
          }
          p {
            color: #6b7280;
            margin-bottom: 2rem;
            line-height: 1.5;
          }
          .close-button {
            background-color: #6b7280;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-size: 0.875rem;
            cursor: pointer;
            transition: background-color 0.2s;
          }
          .close-button:hover {
            background-color: #4b5563;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="error-icon">❌</div>
          <h1>Connection Failed</h1>
          <p>There was an error connecting your Instagram account. Please try again.</p>
          <button class="close-button" onclick="window.close()">Close Window</button>
        </div>
        <script>
          // Send message to parent window that connection failed
          try {
            if (window.opener) {
              window.opener.postMessage({ type: 'instagram-oauth-error' }, '*');
            }
          } catch (e) {
            console.log('Could not communicate with parent window');
          }
        </script>
      </body>
      </html>
    `;

    return new NextResponse(errorHtml, {
      status: 200,
      headers: {
        'Content-Type': 'text/html',
      },
    });
  }
}