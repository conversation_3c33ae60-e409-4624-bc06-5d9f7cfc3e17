import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@workspace/database/client';

/**
 * Cleanup old Instagram follow-ups
 * This endpoint should be called by a cron job daily
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check for authorization header
    const authHeader = req.headers.get('authorization');
    if (!authHeader || authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('Starting cleanup of old follow-ups...');

    // Get all organizations with auto cleanup enabled
    const organizationsWithCleanup = await prisma.organization.findMany({
      where: {
        InstagramSettings: {
          autoCleanupEnabled: true
        }
      },
      include: {
        InstagramSettings: {
          select: {
            followUpCleanupDays: true,
            autoCleanupEnabled: true
          }
        }
      }
    });

    let totalDeletedNewFollowUps = 0;
    let totalClearedLegacyFollowUps = 0;
    const cleanupResults: Array<{
      organizationId: string;
      organizationName: string;
      cleanupDays: number;
      deletedNewFollowUps: number;
      clearedLegacyFollowUps: number;
    }> = [];

    for (const organization of organizationsWithCleanup) {
      if (!organization.InstagramSettings) {
        continue; // Skip organizations without Instagram settings
      }

      const settings = organization.InstagramSettings;
      const cleanupDays = settings.followUpCleanupDays || 30;

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - cleanupDays);

      console.log(`Cleaning up ${organization.name} (${organization.id}): ${cleanupDays} days, cutoff: ${cutoffDate.toISOString()}`);

      // Clean up MessageList records for this organization
      const deletedNewFollowUps = await prisma.messageList.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate
          },
          status: {
            in: ['SENT', 'FAILED']
          },
          organizationId: organization.id
        }
      });

      // Legacy follow-up fields were migrated to MessageList table
      // No additional cleanup needed for legacy fields
      const clearedLegacyFollowUps = 0;

      const totalCleaned = deletedNewFollowUps.count + clearedLegacyFollowUps;
      totalDeletedNewFollowUps += deletedNewFollowUps.count;
      totalClearedLegacyFollowUps += clearedLegacyFollowUps;

      cleanupResults.push({
        organizationId: organization.id,
        organizationName: organization.name,
        cleanupDays,
        deletedNewFollowUps: deletedNewFollowUps.count,
        clearedLegacyFollowUps
      });

      console.log(`Cleanup completed for ${organization.name}:
      - Deleted ${deletedNewFollowUps.count} new follow-ups
      - Cleared ${clearedLegacyFollowUps} legacy follow-ups
      - Total cleaned: ${totalCleaned}
      - Cutoff date: ${cutoffDate.toISOString()}
      - Cleanup days: ${cleanupDays}`);
    }

    const grandTotal = totalDeletedNewFollowUps + totalClearedLegacyFollowUps;

    console.log(`Global cleanup summary:
    - Organizations processed: ${cleanupResults.length}
    - Total new follow-ups deleted: ${totalDeletedNewFollowUps}
    - Total legacy follow-ups cleared: ${totalClearedLegacyFollowUps}
    - Grand total cleaned: ${grandTotal}`);

    return NextResponse.json({
      success: true,
      summary: {
        organizationsProcessed: cleanupResults.length,
        totalNewFollowUpsDeleted: totalDeletedNewFollowUps,
        totalLegacyFollowUpsCleared: totalClearedLegacyFollowUps,
        grandTotal
      },
      results: cleanupResults
    });

  } catch (error) {
    console.error('Error cleaning up old follow-ups:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
