import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@workspace/database/client';

/**
 * Background sync process: MessageList → AttackList
 * This runs every minute to populate the AttackList with messages ready for chrome extension
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check for authorization header (simple API key)
    const authHeader = req.headers.get('authorization');
    const apiKey = process.env.CRON_API_KEY;
    
    if (!apiKey || authHeader !== `Bearer ${apiKey}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const now = new Date();
    
    // Import 24-hour window function
    const { isWithin24HourWindow } = await import('../../../../../../packages/instagram-bot/src/24-hour-window');

    console.log('🔄 Starting AttackList sync process...');

    // Get all active organizations
    const organizations = await prisma.organization.findMany({
      where: {
        InstagramSettings: {
          isConnected: true
        }
      },
      select: { id: true, name: true }
    });

    let totalProcessed = 0;
    let totalAddedToAttackList = 0;
    let totalSystemTimeouts = 0;

    for (const org of organizations) {
      try {
        const result = await prisma.$transaction(async (tx) => {
          // Find messages ready for chrome extension handling
          const readyMessages = await tx.messageList.findMany({
            where: {
              organizationId: org.id,
              status: 'PENDING',
              scheduledTime: { lte: now },
              // Not already in AttackList
              AttackList: { none: {} }
            },
            include: {
              InstagramContact: {
                select: {
                  instagramNickname: true,
                  lastInteractionAt: true
                }
              }
            },
            orderBy: [
              { priority: 'desc' },
              { scheduledTime: 'asc' }
            ],
            take: 100 // Process in batches to avoid overwhelming the system
          });

          // Filter messages that should be handled by extension
          const extensionReadyMessages = readyMessages.filter(message => {
            // Always include EXTENSION messages
            if (message.handlerType === 'EXTENSION') {
              return true;
            }
            
            // For SYSTEM messages, check if they're outside 24-hour window
            if (message.handlerType === 'SYSTEM') {
              const isWithinWindow = isWithin24HourWindow(
                message.InstagramContact.lastInteractionAt,
                message.scheduledTime
              );
              return !isWithinWindow; // Include if outside window (system timeout)
            }
            
            return false;
          });

          if (extensionReadyMessages.length === 0) {
            return { processed: 0, addedToAttackList: 0, systemTimeouts: 0 };
          }

          let orgAddedToAttackList = 0;
          let orgSystemTimeouts = 0;

          // Process each message
          for (const message of extensionReadyMessages) {
            // Check if this is a system timeout (move from SYSTEM to EXTENSION)
            const isSystemTimeout = message.handlerType === 'SYSTEM' &&
                                  !isWithin24HourWindow(
                                    message.InstagramContact.lastInteractionAt,
                                    message.scheduledTime
                                  );

            if (isSystemTimeout) {
              // Update MessageList to change handler type
              await tx.messageList.update({
                where: { id: message.id },
                data: { 
                  handlerType: 'EXTENSION',
                  updatedAt: now
                }
              });
              orgSystemTimeouts++;
            }

            // Determine message type for AttackList
            let attackListMessageType: 'FIRST_MESSAGE' | 'FOLLOW_UP' = 'FOLLOW_UP';
            
            // Check if this is a first message (contact has no previous messages sent)
            const contact = await tx.instagramContact.findUnique({
              where: { id: message.contactId },
              select: { messagesSentCount: true, lastInteractionAt: true }
            });

            if (contact && contact.messagesSentCount === 0) {
              attackListMessageType = 'FIRST_MESSAGE';
            }

            // Add to AttackList
            await tx.attackList.create({
              data: {
                organizationId: org.id,
                contactId: message.contactId,
                messageListId: message.id,
                nickname: message.nickname,
                priority: message.priority,
                messageContent: message.messageContent,
                messageType: attackListMessageType,
                sequenceNumber: message.sequenceNumber,
                addedAt: now,
                status: 'READY'
              }
            });

            orgAddedToAttackList++;
          }

          return {
            processed: extensionReadyMessages.length,
            addedToAttackList: orgAddedToAttackList,
            systemTimeouts: orgSystemTimeouts
          };
        });

        totalProcessed += result.processed;
        totalAddedToAttackList += result.addedToAttackList;
        totalSystemTimeouts += result.systemTimeouts;

        if (result.processed > 0) {
          console.log(`✅ ${org.name}: processed ${result.processed}, added ${result.addedToAttackList} to attack list, ${result.systemTimeouts} system timeouts`);
        }

      } catch (orgError) {
        console.error(`❌ Error processing organization ${org.name}:`, orgError);
        continue; // Continue with other organizations
      }
    }

    // Clean up old sent/failed AttackList entries (older than 1 hour)
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const cleanupResult = await prisma.attackList.deleteMany({
      where: {
        status: { in: ['SENT', 'FAILED'] },
        addedAt: { lt: oneHourAgo }
      }
    });

    console.log(`🧹 Cleaned up ${cleanupResult.count} old AttackList entries`);

    const summary = {
      success: true,
      timestamp: now.toISOString(),
      organizations: organizations.length,
      processed: {
        totalMessages: totalProcessed,
        addedToAttackList: totalAddedToAttackList,
        systemTimeouts: totalSystemTimeouts,
        cleanedUp: cleanupResult.count
      }
    };

    console.log('✅ AttackList sync completed:', summary);

    return NextResponse.json(summary);

  } catch (error) {
    console.error('❌ Error in AttackList sync process:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * Manual trigger for attack list sync (for testing/debugging)
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    // Check for API key authentication
    const apiKey = req.headers.get('X-API-Key');
    if (!apiKey) {
      return NextResponse.json({ error: 'API key required' }, { status: 401 });
    }

    // For manual triggers, allow any valid API key
    const { verifyApiKey } = await import('@workspace/api-keys');
    const result = await verifyApiKey(apiKey);
    if (!result.success) {
      return NextResponse.json({ error: result.errorMessage }, { status: 401 });
    }

    const body = await req.json();
    const { organizationId, dryRun = false } = body;

    const now = new Date();
    
    // Import 24-hour window function
    const { isWithin24HourWindow } = await import('../../../../../../packages/instagram-bot/src/24-hour-window');

    // Process specific organization or all
    const whereClause = organizationId ? { id: organizationId } : {};
    
    const organizations = await prisma.organization.findMany({
      where: {
        ...whereClause,
        InstagramSettings: { isConnected: true }
      },
      select: { id: true, name: true }
    });

    const results = [];

    for (const org of organizations) {
      const allMessages = await prisma.messageList.findMany({
        where: {
          organizationId: org.id,
          status: 'PENDING',
          scheduledTime: { lte: now },
          AttackList: { none: {} }
        },
        include: {
          InstagramContact: {
            select: {
              instagramNickname: true,
              messagesSentCount: true,
              lastInteractionAt: true
            }
          }
        }
      });

      // Filter messages ready for extension
      const readyMessages = allMessages.filter(message => {
        if (message.handlerType === 'EXTENSION') {
          return true;
        }
        
        if (message.handlerType === 'SYSTEM') {
          return !isWithin24HourWindow(
            message.InstagramContact.lastInteractionAt,
            message.scheduledTime
          );
        }
        
        return false;
      });

      const orgResult = {
        organizationId: org.id,
        organizationName: org.name,
        readyMessages: readyMessages.length,
        systemTimeouts: readyMessages.filter(m =>
          m.handlerType === 'SYSTEM' && !isWithin24HourWindow(
            m.InstagramContact.lastInteractionAt,
            m.scheduledTime
          )
        ).length,
        firstMessages: readyMessages.filter(m => 
          m.InstagramContact.messagesSentCount === 0
        ).length,
        followUps: readyMessages.filter(m => 
          m.InstagramContact.messagesSentCount > 0
        ).length
      };

      results.push(orgResult);

      // If not dry run, actually process
      if (!dryRun && readyMessages.length > 0) {
        // This would trigger the actual sync - for now just log
        console.log(`Would process ${readyMessages.length} messages for ${org.name}`);
      }
    }

    return NextResponse.json({
      success: true,
      dryRun,
      timestamp: now.toISOString(),
      results
    });

  } catch (error) {
    console.error('Error in manual AttackList sync:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}