import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@workspace/database/client';

const INSTAGRAM_CLIENT_SECRET = process.env.INSTAGRAM_CLIENT_SECRET;

export async function GET(req: NextRequest) {
  try {
    // Verify this is a cron request (you might want to add authentication)
    const authHeader = req.headers.get('authorization');
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!INSTAGRAM_CLIENT_SECRET) {
      return NextResponse.json({ error: 'Instagram client secret not configured' }, { status: 500 });
    }

    // Find Instagram accounts with tokens expiring within 7 days
    const sevenDaysFromNow = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    const accountsToRefresh = await prisma.instagramSettings.findMany({
      where: {
        isConnected: true,
        instagramToken: { not: null },
        tokenExpiresAt: {
          lt: sevenDaysFromNow, // Token expires within 7 days
          gt: new Date() // Token not yet expired
        },
        updatedAt: {
          lt: twentyFourHoursAgo // Token is at least 24 hours old
        }
      },
      include: {
        Organization: {
          select: {
            slug: true,
            name: true
          }
        }
      }
    });

    const results = [];

    for (const account of accountsToRefresh) {
      try {
        console.log(`Refreshing token for organization: ${account.Organization.slug}`);

        // Refresh the token
        const refreshResponse = await fetch(
          `https://graph.instagram.com/refresh_access_token?grant_type=ig_refresh_token&access_token=${account.instagramToken}`,
          { method: 'GET' }
        );

        const refreshData = await refreshResponse.json();

        if (!refreshResponse.ok) {
          console.error(`Failed to refresh token for ${account.Organization.slug}:`, refreshData);
          results.push({
            organizationSlug: account.Organization.slug,
            success: false,
            error: refreshData.error?.message || 'Unknown error'
          });
          continue;
        }

        // Calculate new expiration (60 days from now)
        const newExpiresAt = new Date(Date.now() + (refreshData.expires_in * 1000));

        // Update the token in database
        await prisma.instagramSettings.update({
          where: {
            id: account.id
          },
          data: {
            instagramToken: refreshData.access_token,
            tokenExpiresAt: newExpiresAt,
            updatedAt: new Date()
          }
        });

        results.push({
          organizationSlug: account.Organization.slug,
          success: true,
          newExpiresAt: newExpiresAt
        });

        console.log(`Successfully refreshed token for ${account.Organization.slug}, new expiry: ${newExpiresAt}`);

      } catch (error) {
        console.error(`Error refreshing token for ${account.Organization.slug}:`, error);
        results.push({
          organizationSlug: account.Organization.slug,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return NextResponse.json({
      success: true,
      processed: accountsToRefresh.length,
      results: results
    });

  } catch (error) {
    console.error('Cron job error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}