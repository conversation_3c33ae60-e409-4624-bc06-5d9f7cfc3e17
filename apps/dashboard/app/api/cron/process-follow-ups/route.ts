import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@workspace/database/client';

/**
 * Updated Follow-up Processor for MessageList/AttackList Architecture
 * Processes SYSTEM messages within 24-hour window
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check for authorization header (simple API key)
    const authHeader = req.headers.get('authorization');
    const apiKey = process.env.CRON_API_KEY;
    
    if (!apiKey || authHeader !== `Bearer ${apiKey}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const now = new Date();
    const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    console.log('🔄 Starting system follow-up processing...');

    // Get all organizations with Instagram connected
    const organizations = await prisma.organization.findMany({
      where: {
        InstagramSettings: {
          isConnected: true,
          isBotEnabled: true
        }
      },
      include: {
        InstagramSettings: {
          select: {
            instagramToken: true,
            instagramAccountId: true
          }
        }
      }
    });

    let totalProcessed = 0;
    let totalSent = 0;
    let totalFailed = 0;
    let totalMovedToExtension = 0;

    for (const org of organizations) {
      if (!org.InstagramSettings?.instagramToken) {
        console.log(`⚠️ ${org.name}: No Instagram token, skipping`);
        continue;
      }

      try {
        const result = await prisma.$transaction(async (tx) => {
          // Find SYSTEM messages ready to send (within 24h window, with recent contact interaction)
          const systemMessages = await tx.messageList.findMany({
            where: {
              organizationId: org.id,
              status: 'PENDING',
              handlerType: 'SYSTEM',
              scheduledTime: { 
                lte: now,
                gte: twentyFourHoursAgo // Only process if still within 24h window
              }
            },
            include: {
              InstagramContact: {
                select: {
                  id: true,
                  instagramId: true,
                  instagramNickname: true,
                  lastInteractionAt: true,
                  messagesSentCount: true,
                  stage: true
                }
              }
            },
            orderBy: [
              { priority: 'desc' },
              { scheduledTime: 'asc' }
            ],
            take: 50 // Process in batches
          });

          if (systemMessages.length === 0) {
            return { processed: 0, sent: 0, failed: 0, movedToExtension: 0 };
          }

          let orgSent = 0;
          let orgFailed = 0;
          let orgMovedToExtension = 0;

          for (const message of systemMessages) {
            const contact = message.InstagramContact;

            // Check if contact has recent interaction (within 24h for system handling)
            const canSendViaSystem = contact.lastInteractionAt && 
              contact.lastInteractionAt >= twentyFourHoursAgo;

            if (!canSendViaSystem) {
              // Move to extension handling
              await tx.messageList.update({
                where: { id: message.id },
                data: { 
                  handlerType: 'EXTENSION',
                  updatedAt: now
                }
              });
              orgMovedToExtension++;
              continue;
            }

            if (!contact.instagramId) {
              // Can't send without Instagram ID - mark as failed
              await tx.messageList.update({
                where: { id: message.id },
                data: { 
                  status: 'FAILED',
                  updatedAt: now
                }
              });
              orgFailed++;
              continue;
            }

            try {
              // Mark as processing first
              await tx.messageList.update({
                where: { id: message.id },
                data: { 
                  status: 'PROCESSING',
                  updatedAt: now
                }
              });

              // Send message via Instagram API
              // Note: In a real implementation, you'd import the Instagram client here
              // For now, we'll simulate the API call
              console.log(`📤 Sending system message to ${contact.instagramNickname}: ${message.messageContent.substring(0, 50)}...`);
              
              // Simulate API call delay
              await new Promise(resolve => setTimeout(resolve, 100));

              // Mark as sent
              await tx.messageList.update({
                where: { id: message.id },
                data: { 
                  status: 'SENT',
                  updatedAt: now
                }
              });

              // Update contact
              await tx.instagramContact.update({
                where: { id: contact.id },
                data: {
                  lastMessageSentAt: now,
                  messagesSentCount: contact.messagesSentCount + 1,
                  stage: contact.stage === 'initial' ? 'engaged' : contact.stage,
                  updatedAt: now
                }
              });

              // Create stage change log if stage changed
              if (contact.stage === 'initial') {
                await tx.stageChangeLog.create({
                  data: {
                    contactId: contact.id,
                    fromStage: 'initial',
                    toStage: 'engaged',
                    reason: 'System follow-up sent',
                    changedBy: 'system_bot',
                    metadata: {
                      messageListId: message.id,
                      messageType: message.messageType
                    }
                  }
                });
              }

              orgSent++;

              // TODO: Generate next follow-up message if needed
              // This would involve AI analysis or template-based follow-up creation

            } catch (sendError) {
              console.error(`❌ Failed to send message to ${contact.instagramNickname}:`, sendError);
              
              // Mark as failed
              await tx.messageList.update({
                where: { id: message.id },
                data: { 
                  status: 'FAILED',
                  updatedAt: now
                }
              });
              orgFailed++;
            }
          }

          return { 
            processed: systemMessages.length, 
            sent: orgSent, 
            failed: orgFailed,
            movedToExtension: orgMovedToExtension
          };
        });

        totalProcessed += result.processed;
        totalSent += result.sent;
        totalFailed += result.failed;
        totalMovedToExtension += result.movedToExtension;

        if (result.processed > 0) {
          console.log(`✅ ${org.name}: processed ${result.processed}, sent ${result.sent}, failed ${result.failed}, moved to extension ${result.movedToExtension}`);
        }

      } catch (orgError) {
        console.error(`❌ Error processing organization ${org.name}:`, orgError);
        continue;
      }
    }

    const summary = {
      success: true,
      timestamp: now.toISOString(),
      organizations: organizations.length,
      processed: {
        totalMessages: totalProcessed,
        sent: totalSent,
        failed: totalFailed,
        movedToExtension: totalMovedToExtension
      }
    };

    console.log('✅ System follow-up processing completed:', summary);

    return NextResponse.json(summary);

  } catch (error) {
    console.error('❌ Error processing system follow-ups:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
