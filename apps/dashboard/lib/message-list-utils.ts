import { prisma } from '@workspace/database/client';

export type MessageListStatus = 'PENDING' | 'PROCESSING' | 'SENT' | 'FAILED' | 'EXTERNAL';
export type MessageType = 'SYSTEM' | 'EXTENSION';
export type HandlerType = 'SYSTEM' | 'EXTENSION';

interface CreateMessageListEntryParams {
  organizationId: string;
  contactId: string;
  nickname: string;
  priority: number;
  messageType: MessageType;
  handlerType: HandlerType;
  scheduledTime: Date;
  messageContent: string;
  sequenceNumber: number;
  status?: MessageListStatus;
}

/**
 * Helper function to create MessageList entries with all required fields
 * Ensures consistency across the application and prevents missing fields
 */
export async function createMessageListEntry(params: CreateMessageListEntryParams) {
  const {
    organizationId,
    contactId,
    nickname,
    priority,
    messageType,
    handlerType,
    scheduledTime,
    messageContent,
    sequenceNumber,
    status = 'PENDING'
  } = params;

  return await prisma.messageList.create({
    data: {
      organizationId,
      contactId,
      nickname,
      priority,
      messageType,
      handlerType,
      scheduledTime,
      messageContent,
      sequenceNumber,
      status
    }
  });
}

/**
 * Determine handler type based on 24-hour window logic
 * Messages within 24 hours use SYSTEM handler, outside use EXTENSION handler
 */
export function determineHandlerType(scheduledTime: Date, referenceTime: Date = new Date()): HandlerType {
  const hoursDiff = (scheduledTime.getTime() - referenceTime.getTime()) / (1000 * 60 * 60);
  const isWithin24HourWindow = hoursDiff <= 24;
  return isWithin24HourWindow ? 'SYSTEM' : 'EXTENSION';
}

/**
 * Create follow-up messages from templates
 */
export async function createFollowUpsFromTemplates(
  organizationId: string,
  contactId: string,
  nickname: string,
  priority: number,
  sentTime: Date
) {
  // Get organization's follow-up templates
  const followUpTemplates = await prisma.followUpTemplate.findMany({
    where: {
      organizationId,
      isActive: true
    },
    orderBy: [
      { sequenceNumber: 'asc' },
      { variationNumber: 'asc' }
    ]
  });

  if (followUpTemplates.length === 0) {
    return [];
  }

  // Group templates by sequence number
  const templatesBySequence = followUpTemplates.reduce((acc, template) => {
    if (!acc[template.sequenceNumber]) {
      acc[template.sequenceNumber] = [];
    }
    acc[template.sequenceNumber].push(template);
    return acc;
  }, {} as Record<number, typeof followUpTemplates>);

  const sequenceNumbers = Object.keys(templatesBySequence).map(Number).sort();
  const createdFollowUps = [];

  // Create follow-up MessageList entries for each sequence
  for (const sequenceNumber of sequenceNumbers) {
    const sequenceTemplates = templatesBySequence[sequenceNumber];
    if (sequenceTemplates && sequenceTemplates.length > 0) {
      // Pick a random variation for this sequence
      const randomTemplate = sequenceTemplates[Math.floor(Math.random() * sequenceTemplates.length)];

      // Calculate scheduled time based on template delay
      const scheduledTime = new Date(sentTime.getTime() + (randomTemplate.delayHours * 60 * 60 * 1000));
      // Template-based follow-ups are always EXTENSION (user didn't send message first)
      const handlerType: HandlerType = 'EXTENSION';

      // Create MessageList entry
      const followUp = await createMessageListEntry({
        organizationId,
        contactId,
        nickname,
        priority,
        messageType: 'EXTENSION',
        handlerType,
        scheduledTime,
        messageContent: randomTemplate.messageText,
        sequenceNumber,
        status: 'PENDING'
      });

      createdFollowUps.push(followUp);
    }
  }

  return createdFollowUps;
}