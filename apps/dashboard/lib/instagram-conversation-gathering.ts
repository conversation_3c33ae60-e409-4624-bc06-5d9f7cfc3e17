/**
 * Instagram conversation lookup system
 * Phase 1: Gather ALL conversation IDs + usernames as lookup table
 * Phase 2: Match scraped followers against this table  
 * Phase 3: Only fetch conversation content for matches
 */

import axios from 'axios';
import { prisma } from '@workspace/database/client';

// Simple logger
const logger = {
  info: (message: string, ...args: any[]) => console.log(`[INFO] ${message}`, ...args),
  warn: (message: string, ...args: any[]) => console.warn(`[WARN] ${message}`, ...args),
  error: (message: string, ...args: any[]) => console.error(`[ERROR] ${message}`, ...args)
};

const INSTAGRAM_API_VERSION = 'v23.0';
const INSTAGRAM_GRAPH_URL = 'https://graph.instagram.com';
const API_LIMIT = 50;
const MAX_PARALLEL_PAGES = 4;

interface ConversationLookupEntry {
  conversationId: string;
  username: string;
  participantId?: string;
  updatedTime?: Date;
}

interface GatheringProgress {
  organizationId: string;
  status: 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
  totalConversations: number;
  processedPages: number;
  startTime: Date;
  endTime?: Date;
  error?: string;
}

interface InstagramConversation {
  id: string;
  updated_time?: string;
  participants?: {
    data: Array<{
      id: string;
      username?: string;
    }>;
  };
}

interface InstagramResponse {
  data: InstagramConversation[];
  paging?: {
    next?: string;
    previous?: string;
  };
}

/**
 * PHASE 1: Gather ALL conversation list (just IDs + usernames)
 * This creates a lookup table for instant matching later
 */
export async function gatherAllConversationList(
  accessToken: string,
  organizationId: string,
  onProgress?: (progress: GatheringProgress) => void
): Promise<{ success: boolean; lookupEntries: ConversationLookupEntry[]; metrics: any }> {
  const startTime = Date.now();
  const progress: GatheringProgress = {
    organizationId,
    status: 'IN_PROGRESS',
    totalConversations: 0,
    processedPages: 0,
    startTime: new Date()
  };

  try {
    logger.info(`📋 Gathering ALL conversation list for lookup table (org: ${organizationId})`);
    
    // DEBUG: Check existing conversations count BEFORE we start
    const existingCount = await prisma.instagramConversationsNotGathered.count({
      where: { organizationId }
    });
    logger.info(`🔍 DEBUG: Found ${existingCount} existing conversations before gathering starts`);
    
    onProgress?.(progress);

    // Get business account ID to filter it out
    const businessAccountResponse = await axios.get(`${INSTAGRAM_GRAPH_URL}/${INSTAGRAM_API_VERSION}/me`, {
      params: {
        fields: 'id,username',
        access_token: accessToken
      },
      timeout: 10000
    });
    
    const businessAccountId = businessAccountResponse.data.id;
    const businessAccountUsername = businessAccountResponse.data.username;
    logger.info(`📱 Business account: @${businessAccountUsername} (ID: ${businessAccountId})`);

    // Phase 1: Initial fetch
    const initialUrl = `${INSTAGRAM_GRAPH_URL}/${INSTAGRAM_API_VERSION}/me/conversations`;
    const initialResponse = await axios.get<InstagramResponse>(initialUrl, {
      params: {
        fields: 'id,participants.limit(2){username,id},updated_time',
        access_token: accessToken,
        limit: API_LIMIT
      },
      timeout: 30000
    });

    const allConversations: InstagramConversation[] = [...(initialResponse.data.data || [])];
    progress.processedPages = 1;
    progress.totalConversations = allConversations.length;
    
    logger.info(`✅ Initial page: ${allConversations.length} conversations`);
    onProgress?.(progress);

    // Phase 2: Parallel pagination for ALL conversations
    if (initialResponse.data.paging?.next) {
      const { conversations, pagesProcessed } = await fetchAllPagesParallel(
        initialResponse.data.paging.next,
        accessToken,
        (pageCount, newConversations) => {
          progress.processedPages += pageCount;
          progress.totalConversations += newConversations;
          onProgress?.(progress);
        }
      );
      
      allConversations.push(...conversations);
      progress.totalConversations = allConversations.length;
    }

    // Phase 3: Create lookup entries (username → conversation_id mapping)
    const lookupEntries: ConversationLookupEntry[] = [];
    
    for (const conversation of allConversations) {
      if (!conversation.participants?.data || conversation.participants.data.length < 2) continue;
      
      // Filter out business account to get customer
      const customerParticipant = conversation.participants.data.find((p: any) =>
        p.id !== businessAccountId && p.username && p.username !== businessAccountUsername
      );
      
      if (customerParticipant?.username) {
        lookupEntries.push({
          conversationId: conversation.id,
          username: customerParticipant.username,
          participantId: customerParticipant.id,
          updatedTime: conversation.updated_time ? new Date(conversation.updated_time) : undefined
        });
      }
    }

    logger.info(`📊 Created lookup table with ${lookupEntries.length} entries`);

    // Phase 4: Store lookup table in database
    await storeLookupTable(organizationId, lookupEntries);

    progress.status = 'COMPLETED';
    progress.endTime = new Date();
    onProgress?.(progress);

    const totalTime = Date.now() - startTime;
    const metrics = {
      totalTime,
      conversationCount: lookupEntries.length,
      pagesProcessed: progress.processedPages,
      averageTimePerPage: totalTime / progress.processedPages,
      conversationsPerSecond: (lookupEntries.length / totalTime) * 1000
    };

    logger.info(`🎉 Lookup table created in ${totalTime}ms`, metrics);

    return {
      success: true,
      lookupEntries,
      metrics
    };

  } catch (error) {
    progress.status = 'FAILED';
    progress.error = error instanceof Error ? error.message : 'Unknown error';
    progress.endTime = new Date();
    onProgress?.(progress);

    logger.error('❌ Conversation list gathering failed:', error);
    
    return {
      success: false,
      lookupEntries: [],
      metrics: {
        error: progress.error,
        totalTime: Date.now() - startTime
      }
    };
  }
}

/**
 * Fetch all pages in parallel for maximum speed
 */
async function fetchAllPagesParallel(
  firstNextUrl: string,
  accessToken: string,
  onPageComplete?: (pageCount: number, newConversations: number) => void
): Promise<{ conversations: InstagramConversation[]; pagesProcessed: number }> {
  const allConversations: InstagramConversation[] = [];
  let currentBatchUrls = [firstNextUrl];
  let totalPagesProcessed = 0;

  while (currentBatchUrls.length > 0) {
    const batchToProcess = currentBatchUrls.slice(0, MAX_PARALLEL_PAGES);
    currentBatchUrls = currentBatchUrls.slice(MAX_PARALLEL_PAGES);

    logger.info(`📡 Fetching ${batchToProcess.length} pages in parallel...`);

    const batchPromises = batchToProcess.map(url => 
      axios.get<InstagramResponse>(url, { 
        timeout: 30000,
        validateStatus: (status) => status < 500
      }).catch(error => {
        logger.warn(`Page fetch failed: ${error.message}`);
        return null;
      })
    );

    const batchResponses = await Promise.all(batchPromises);
    
    const nextUrls: string[] = [];
    let batchConversationCount = 0;
    
    for (const response of batchResponses) {
      if (!response || !response.data?.data) continue;
      
      const conversations = response.data.data;
      allConversations.push(...conversations);
      batchConversationCount += conversations.length;
      totalPagesProcessed++;
      
      if (response.data.paging?.next) {
        nextUrls.push(response.data.paging.next);
      }
    }

    onPageComplete?.(batchToProcess.length, batchConversationCount);
    currentBatchUrls.push(...nextUrls);
    
    logger.info(`✅ Batch complete: ${batchConversationCount} new conversations (${allConversations.length} total)`);
  }

  return {
    conversations: allConversations,
    pagesProcessed: totalPagesProcessed
  };
}

/**
 * Store the lookup table in database
 */
async function storeLookupTable(
  organizationId: string,
  lookupEntries: ConversationLookupEntry[]
): Promise<void> {
  if (lookupEntries.length === 0) return;

  logger.info(`🚨 DEBUG: storeLookupTable called with ${lookupEntries.length} entries for org ${organizationId}`);

  // Don't clear existing conversations - just add new ones we don't have
  logger.info(`🚨 DEBUG: Checking for existing conversations to avoid duplicates`);

  // Get existing conversations to avoid duplicates
  const existingConversations = await prisma.instagramConversationsNotGathered.findMany({
    where: { organizationId },
    select: { instagramConversationId: true }
  });
  
  const existingIds = new Set(existingConversations.map(c => c.instagramConversationId));
  const newEntries = lookupEntries.filter(entry => !existingIds.has(entry.conversationId));
  
  logger.info(`📊 Found ${existingConversations.length} existing conversations, ${newEntries.length} new ones to add`);
  
  if (newEntries.length === 0) {
    logger.info(`✅ No new conversations to add - all ${lookupEntries.length} conversations already exist`);
    return;
  }

  // Store only new conversations in batches
  const batchSize = 1000;
  
  for (let i = 0; i < newEntries.length; i += batchSize) {
    const batch = newEntries.slice(i, i + batchSize);
    
    const createData = batch.map(entry => ({
      organizationId,
      instagramConversationId: entry.conversationId,
      participantUsername: entry.username,
      participantId: entry.participantId || '',
      updatedTime: entry.updatedTime || new Date(),
      isGathered: false // Not gathered yet - just in lookup table
    }));

    await prisma.instagramConversationsNotGathered.createMany({
      data: createData,
      skipDuplicates: true
    });
    
    logger.info(`✅ Stored batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(newEntries.length / batchSize)}`);
  }

  logger.info(`✅ Added ${newEntries.length} new conversations to lookup table (${existingConversations.length} already existed)`);
}

/**
 * PHASE 2: Check if followers have conversations (instant lookup)
 * This is called when Chrome extension processes followers
 */
export async function checkFollowerMatches(
  organizationId: string,
  followerUsernames: string[]
): Promise<Map<string, string | null>> {
  if (followerUsernames.length === 0) return new Map();

  logger.info(`🔍 Checking ${followerUsernames.length} followers against conversation lookup table`);

  // Instant database lookup - no API calls needed!
  const conversations = await prisma.instagramConversationsNotGathered.findMany({
    where: {
      organizationId,
      participantUsername: { in: followerUsernames }
    },
    select: {
      participantUsername: true,
      instagramConversationId: true
    }
  });

  const result = new Map<string, string | null>();
  
  // Initialize all with null
  for (const username of followerUsernames) {
    result.set(username, null);
  }
  
  // Set conversation IDs for matches
  for (const conv of conversations) {
    result.set(conv.participantUsername, conv.instagramConversationId);
  }

  const matchCount = conversations.length;
  logger.info(`✅ Found ${matchCount}/${followerUsernames.length} conversation matches`);

  return result;
}

/**
 * PHASE 3: Fetch actual conversation content for matched followers only
 * This is called only for followers that have conversations
 */
export async function fetchConversationContent(
  accessToken: string,
  conversationId: string
): Promise<{ success: boolean; messages?: any[]; error?: string }> {
  try {
    logger.info(`📩 Fetching conversation content for ID: ${conversationId}`);

    const response = await axios.get(
      `${INSTAGRAM_GRAPH_URL}/${INSTAGRAM_API_VERSION}/${conversationId}`,
      {
        params: {
          fields: 'messages{message,from,created_time,attachments{image_url,video_url}}',
          access_token: accessToken
        },
        timeout: 15000
      }
    );

    const messages = response.data?.messages?.data || [];
    logger.info(`✅ Fetched ${messages.length} messages from conversation ${conversationId}`);

    return {
      success: true,
      messages
    };

  } catch (error) {
    logger.error(`❌ Failed to fetch conversation ${conversationId}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Trigger initial conversation list gathering (Phase 1)
 */
export async function triggerConversationGathering(
  accessToken: string,
  organizationId: string
): Promise<void> {
  setImmediate(() => {
    gatherAllConversationList(accessToken, organizationId, (progress) => {
      if (progress.processedPages % 10 === 0 || progress.status !== 'IN_PROGRESS') {
        logger.info('Conversation list gathering progress:', {
          organizationId,
          status: progress.status,
          conversations: progress.totalConversations,
          pages: progress.processedPages
        });
      }
    }).catch(error => {
      logger.error('Background conversation list gathering failed:', error);
    });
  });
}

/**
 * Get gathering status
 */
export async function getGatheringStatus(organizationId: string): Promise<{
  hasGathered: boolean;
  conversationCount: number;
  lastGatheredAt?: Date;
}> {
  const count = await prisma.instagramConversationsNotGathered.count({
    where: { organizationId }
  });

  const lastConversation = await prisma.instagramConversationsNotGathered.findFirst({
    where: { organizationId },
    orderBy: { createdAt: 'desc' },
    select: { createdAt: true }
  });

  return {
    hasGathered: count > 0,
    conversationCount: count,
    lastGatheredAt: lastConversation?.createdAt
  };
}

/**
 * Quick check if username has conversation (for single lookups)
 */
export async function hasConversationWithUser(
  organizationId: string,
  username: string
): Promise<{ hasConversation: boolean; conversationId?: string }> {
  const conversation = await prisma.instagramConversationsNotGathered.findFirst({
    where: {
      organizationId,
      participantUsername: username
    },
    select: {
      instagramConversationId: true
    }
  });

  return {
    hasConversation: !!conversation,
    conversationId: conversation?.instagramConversationId
  };
}

/**
 * Bulk check for multiple usernames (Phase 2 implementation)
 */
export async function bulkCheckConversations(
  organizationId: string,
  usernames: string[]
): Promise<Map<string, string | null>> {
  return checkFollowerMatches(organizationId, usernames);
}

/**
 * Manual trigger for gathering conversation list
 */
export async function manualGatherConversations(organizationId: string): Promise<{
  success: boolean;
  message: string;
  metrics?: any;
}> {
  try {
    const settings = await prisma.instagramSettings.findUnique({
      where: { organizationId },
      select: { instagramToken: true }
    });

    if (!settings?.instagramToken) {
      return {
        success: false,
        message: 'No Instagram token found for this organization'
      };
    }

    const result = await gatherAllConversationList(
      settings.instagramToken,
      organizationId
    );

    return {
      success: result.success,
      message: result.success 
        ? `Successfully gathered ${result.lookupEntries.length} conversation entries in ${result.metrics.totalTime}ms (${result.metrics.conversationsPerSecond.toFixed(0)} conv/sec)`
        : 'Conversation list gathering failed',
      metrics: result.metrics
    };

  } catch (error) {
    logger.error('Manual conversation list gathering failed:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Backwards compatibility
export const blazingFastConversationGathering = gatherAllConversationList;