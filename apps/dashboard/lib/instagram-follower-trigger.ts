import { prisma } from '@workspace/database/client';
import { generateInstagramResponse } from '@workspace/instagram-bot';
import { getConversationMessages } from '~/lib/instagram-client';
import { hasConversationWithUser } from '~/lib/instagram-conversation-gathering';

/**
 * Helper function to determine handler type based on 24-hour window
 */
function determineHandlerType(lastInteractionAt: Date | null): 'SYSTEM' | 'EXTENSION' {
  if (!lastInteractionAt) return 'EXTENSION'; // No interaction, use extension
  
  const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
  return lastInteractionAt >= twentyFourHoursAgo ? 'SYSTEM' : 'EXTENSION';
}

/**
 * Helper function to create messages in MessageList
 */
async function createMessagesInMessageList(
  organizationId: string,
  contactId: string,
  nickname: string,
  priority: number,
  messages: Array<{
    content: string;
    delayHours?: number;
    sequenceNumber: number;
    messageType: 'SYSTEM' | 'EXTENSION';
  }>,
  handlerType: 'SYSTEM' | 'EXTENSION'
) {
  const now = new Date();
  
  for (const message of messages) {
    const scheduledTime = message.delayHours 
      ? new Date(now.getTime() + (message.delayHours * 60 * 60 * 1000))
      : now;

    await prisma.messageList.create({
      data: {
        organizationId,
        contactId,
        nickname,
        priority,
        messageType: message.messageType,
        scheduledTime,
        messageContent: message.content,
        status: 'PENDING',
        handlerType,
        sequenceNumber: message.sequenceNumber
      }
    });
  }
}

/**
 * Trigger processing for a new Instagram follower
 * Updated to use MessageList architecture instead of InstagramFollowUp
 */
export async function triggerFollowerProcessing(followerId: string) {
  try {
    console.log(`🚀 Triggering processing for follower: ${followerId}`);

    // Get the follower details
    const follower = await prisma.instagramFollower.findUnique({
      where: { id: followerId }
    });

    if (!follower) {
      console.error(`❌ Follower not found: ${followerId}`);
      return { success: false, error: 'Follower not found' };
    }

    if (!follower.automationEnabled) {
      console.log(`⏸️ Automation disabled for follower: ${follower.instagramNickname}`);
      return { success: false, error: 'Automation disabled' };
    }

    if (follower.status !== 'pending') {
      console.log(`⏭️ Follower already processed: ${follower.instagramNickname} (${follower.status})`);
      return { success: false, error: 'Already processed' };
    }

    // Get Instagram settings for this organization
    const instagramSettings = await prisma.instagramSettings.findFirst({
      where: {
        organizationId: follower.organizationId,
        instagramToken: { not: null }
      }
    });

    if (!instagramSettings?.instagramToken) {
      console.error(`❌ No Instagram token for organization: ${follower.organizationId}`);
      return { success: false, error: 'No Instagram token' };
    }

    // Check if InstagramContact already exists
    const existingContact = await prisma.instagramContact.findFirst({
      where: {
        organizationId: follower.organizationId,
        instagramNickname: follower.instagramNickname
      }
    });

    if (existingContact) {
      // Mark follower as contacted
      await prisma.instagramFollower.update({
        where: { id: follower.id },
        data: {
          status: 'contacted',
          isContacted: true,
          updatedAt: new Date()
        }
      });

      console.log(`✅ Follower already exists as contact: ${follower.instagramNickname}`);
      return {
        success: true,
        data: {
          action: 'marked_as_contacted',
          existingContactId: existingContact.id
        }
      };
    }

    // Check if conversation data has been gathered for this organization
    console.log(`Checking conversation data for organization: ${follower.organizationId}`);
    const conversationCount = await prisma.instagramConversationsNotGathered.count({
      where: { organizationId: follower.organizationId }
    });

    if (conversationCount === 0) {
      console.log(`⚠️ No conversation data found for organization. Please reconnect Instagram account to gather conversations.`);
    } else {
      console.log(`✅ Found ${conversationCount} conversations in database`);
    }

    // Check if this specific follower has a conversation
    console.log(`Checking for conversation with follower: ${follower.instagramNickname}`);
    const { hasConversation, conversationId } = await hasConversationWithUser(
      follower.organizationId,
      follower.instagramNickname
    );

    const conversationData = hasConversation ? {
      instagramConversationId: conversationId!
    } : null;

    if (!hasConversation) {
      // No conversation - create contact with batch messages in MessageList
      console.log(`📝 No conversation found for ${follower.instagramNickname} - creating first message in MessageList`);

      // Get available message batches
      const messageBatches = await prisma.messageBatch.findMany({
        where: {
          organizationId: follower.organizationId,
          isActive: true
        },
        include: {
          MessageBatchItem: {
            orderBy: { sequenceNumber: 'asc' }
          }
        }
      });

      const batchesWithMessages = messageBatches.filter(
        batch => batch.MessageBatchItem.length > 0
      );

      const randomBatch = batchesWithMessages.length > 0
        ? batchesWithMessages[Math.floor(Math.random() * batchesWithMessages.length)]
        : null;

      const newContact = await prisma.instagramContact.create({
        data: {
          organizationId: follower.organizationId,
          userId: follower.userId,
          instagramId: follower.instagramId,
          instagramNickname: follower.instagramNickname,
          avatar: follower.avatar,
          followerCount: follower.followerCount,
          isVerifiedUser: follower.isVerified,
          stage: 'new',
          priority: 3, // New followers get priority 3
          status: 'pending',
          messageCount: 0,
          isIgnored: false,
          isTakeControl: false,
          isConversionLinkSent: false,
          conversationSource: 'extension',
          batchId: randomBatch?.id
        }
      });

      // Create batch messages in MessageList (first messages - use EXTENSION handler)
      if (randomBatch && randomBatch.MessageBatchItem.length > 0) {
        const batchMessages = randomBatch.MessageBatchItem.map((item, index) => ({
          content: item.messageText,
          delayHours: index === 0 ? 0 : (item.delayMinutes / 60), // First message immediate, others with delay
          sequenceNumber: item.sequenceNumber,
          messageType: 'EXTENSION' as const
        }));

        await createMessagesInMessageList(
          follower.organizationId,
          newContact.id,
          follower.instagramNickname,
          3,
          batchMessages,
          'EXTENSION' // First messages always go to extension
        );

        console.log(`✅ Created ${batchMessages.length} batch messages in MessageList for ${follower.instagramNickname}`);
      }

      // Mark follower as contacted
      await prisma.instagramFollower.update({
        where: { id: follower.id },
        data: {
          status: 'contacted',
          isContacted: true,
          updatedAt: new Date()
        }
      });

      console.log(`✅ Follower processed with batch messages: ${follower.instagramNickname}`);
      return {
        success: true,
        data: {
          action: 'created_with_batch_messages',
          contactId: newContact.id,
          priority: 3,
          batchId: randomBatch?.id
        }
      };

    } else if (conversationData) {
      // Has conversation - get full conversation and analyze with AI
      console.log(`💬 Conversation found for ${follower.instagramNickname} - analyzing with AI`);

      const conversationResponse = await getConversationMessages(conversationData.instagramConversationId, instagramSettings.instagramToken);

      // Format conversation history for AI
      let conversationHistory = '';
      let lastUserInteractionAt = null;
      
      if (conversationResponse && conversationResponse.data && conversationResponse.data.length > 0) {
        const conversation = conversationResponse.data[0];
        const messages = conversation.messages?.data || [];

        if (messages.length > 0) {
          const sortedMessages = messages.sort((a: any, b: any) =>
            new Date(a.created_time).getTime() - new Date(b.created_time).getTime()
          );

          conversationHistory = sortedMessages.map((msg: any) => {
            const sender = msg.from?.username || msg.from?.id || 'Unknown';
            const messageText = msg.message || '[Media/Attachment]';
            return `${sender}: ${messageText}`;
          }).join('\n');

          // Get last user interaction timestamp
          const lastUserMessage = sortedMessages
            .filter((msg: any) => msg.from?.username === follower.instagramNickname)
            .pop();

          if (lastUserMessage) {
            lastUserInteractionAt = new Date(lastUserMessage.created_time);
            conversationHistory += `\n\nLAST USER INTERACTION: ${lastUserInteractionAt.toISOString()}`;
          }
        } else {
          conversationHistory = 'No messages found in conversation';
        }
      } else {
        conversationHistory = 'No messages found in conversation';
      }

      // Pass to AI for analysis
      console.log(`🤖 Analyzing conversation with AI for ${follower.instagramNickname}...`);

      const aiResponse = await generateInstagramResponse({
        prompt: "CONVERSATION GATHERING",
        conversationHistory: conversationHistory,
        organizationId: follower.organizationId
      });

      console.log(`AI analysis for ${follower.instagramNickname}:`, {
        stage: aiResponse.stage,
        priority: aiResponse.priority,
        followUpsCount: aiResponse.followUps?.length || 0
      });

      // Check if contact already exists
      const existingContact = await prisma.instagramContact.findFirst({
        where: {
          organizationId: follower.organizationId,
          instagramNickname: follower.instagramNickname
        }
      });

      if (existingContact) {
        console.log(`📝 Contact already exists: ${follower.instagramNickname} - Creating AI follow-ups in MessageList`);

        // Clear existing messages for this contact from MessageList
        await prisma.messageList.deleteMany({
          where: { contactId: existingContact.id }
        });

        // Create AI-generated follow-ups in MessageList
        if (aiResponse.followUps && aiResponse.followUps.length > 0) {
          const handlerType = determineHandlerType(lastUserInteractionAt);
          
          const followUpMessages = aiResponse.followUps.map((followUp, index) => ({
            content: followUp.message,
            delayHours: followUp.delayHours || 24,
            sequenceNumber: index + 1,
            messageType: 'SYSTEM' as const
          }));

          await createMessagesInMessageList(
            follower.organizationId,
            existingContact.id,
            follower.instagramNickname,
            aiResponse.priority || existingContact.priority,
            followUpMessages,
            handlerType
          );

          console.log(`✅ Created ${followUpMessages.length} follow-up messages in MessageList for existing contact ${follower.instagramNickname} (handler: ${handlerType})`);
        }

        // Update existing contact with new AI data
        await prisma.instagramContact.update({
          where: { id: existingContact.id },
          data: {
            stage: (aiResponse.stage as any) || existingContact.stage,
            priority: aiResponse.priority || existingContact.priority,
            messageCount: conversationResponse?.data?.[0]?.messages?.data?.length || existingContact.messageCount,
            lastInteractionAt: lastUserInteractionAt,
            updatedAt: new Date()
          }
        });

        // Save conversation messages to database if not already saved
        const messages = conversationResponse?.data?.[0]?.messages?.data || [];
        if (messages.length > 0) {
          console.log(`Saving ${messages.length} messages to database for existing contact ${follower.instagramNickname}...`);

          for (const msg of messages) {
            try {
              const existingMessage = await prisma.instagramMessage.findFirst({
                where: {
                  contactId: existingContact.id,
                  messageId: msg.id
                }
              });

              if (!existingMessage) {
                await prisma.instagramMessage.create({
                  data: {
                    contactId: existingContact.id,
                    messageId: msg.id,
                    content: msg.message || '[Media/Attachment]',
                    isFromUser: msg.from?.username === follower.instagramNickname,
                    timestamp: new Date(msg.created_time),
                    mediaType: msg.attachments?.[0]?.mime_type || null,
                    mediaUrl: msg.attachments?.[0]?.file_url || null
                  }
                });
              }
            } catch (error) {
              console.error(`Error saving message ${msg.id}:`, error);
            }
          }
        }

        // Mark follower as contacted
        await prisma.instagramFollower.update({
          where: { id: follower.id },
          data: {
            status: 'contacted',
            isContacted: true,
            updatedAt: new Date()
          }
        });

        // Mark conversation as gathered
        await prisma.instagramConversationsNotGathered.updateMany({
          where: {
            organizationId: follower.organizationId,
            instagramConversationId: conversationData.instagramConversationId
          },
          data: {
            isGathered: true
          }
        });

        console.log(`✅ Updated existing contact ${follower.instagramNickname} with AI priority ${aiResponse.priority} and ${aiResponse.followUps?.length || 0} follow-ups in MessageList`);
        return {
          success: true,
          data: {
            action: 'updated_existing_contact',
            contactId: existingContact.id,
            priority: aiResponse.priority || existingContact.priority,
            stage: aiResponse.stage || existingContact.stage,
            followUpsCreated: aiResponse.followUps?.length || 0
          }
        };
      }

      // Create new contact with AI-determined priority and stage
      const newContact = await prisma.instagramContact.create({
        data: {
          organizationId: follower.organizationId,
          userId: follower.userId,
          instagramId: follower.instagramId,
          instagramNickname: follower.instagramNickname,
          avatar: follower.avatar,
          followerCount: follower.followerCount,
          isVerifiedUser: follower.isVerified,
          stage: (aiResponse.stage as any) || 'initial',
          priority: aiResponse.priority || 1,
          status: 'pending',
          messageCount: conversationResponse?.data?.[0]?.messages?.data?.length || 0,
          isIgnored: false,
          isTakeControl: false,
          isConversionLinkSent: false,
          lastInteractionAt: lastUserInteractionAt,
          conversationSource: 'extension'
        }
      });

      // Save conversation messages to database
      const messages = conversationResponse?.data?.[0]?.messages?.data || [];
      if (messages.length > 0) {
        for (const msg of messages) {
          try {
            await prisma.instagramMessage.create({
              data: {
                contactId: newContact.id,
                messageId: msg.id,
                content: msg.message || '[Media/Attachment]',
                isFromUser: msg.from?.username === follower.instagramNickname,
                isFromExtension: true,
                timestamp: new Date(msg.created_time),
                mediaType: msg.attachments?.[0]?.mime_type || null,
                mediaUrl: msg.attachments?.[0]?.file_url || null
              }
            });
          } catch (error) {
            console.error(`Error saving message ${msg.id}:`, error);
          }
        }
      }

      // Create AI-generated follow-ups in MessageList
      if (aiResponse.followUps && aiResponse.followUps.length > 0) {
        const handlerType = determineHandlerType(lastUserInteractionAt);
        
        const followUpMessages = aiResponse.followUps.map((followUp, index) => ({
          content: followUp.message,
          delayHours: followUp.delayHours || 24,
          sequenceNumber: index + 1,
          messageType: 'SYSTEM' as const
        }));

        await createMessagesInMessageList(
          follower.organizationId,
          newContact.id,
          follower.instagramNickname,
          aiResponse.priority || 1,
          followUpMessages,
          handlerType
        );

        console.log(`✅ Created ${followUpMessages.length} follow-up messages in MessageList for ${follower.instagramNickname} (handler: ${handlerType})`);
      }

      // Mark follower as contacted
      await prisma.instagramFollower.update({
        where: { id: follower.id },
        data: {
          status: 'contacted',
          isContacted: true,
          updatedAt: new Date()
        }
      });

      // Mark conversation as gathered
      if (conversationData) {
        await prisma.instagramConversationsNotGathered.updateMany({
          where: {
            organizationId: follower.organizationId,
            instagramConversationId: conversationData.instagramConversationId
          },
          data: {
            isGathered: true
          }
        });
      }

      console.log(`✅ Added ${follower.instagramNickname} with AI priority ${aiResponse.priority} and ${aiResponse.followUps?.length || 0} follow-ups to MessageList`);
      return {
        success: true,
        data: {
          action: 'created_with_ai_analysis',
          contactId: newContact.id,
          priority: aiResponse.priority || 1,
          stage: aiResponse.stage || 'initial',
          followUpsCreated: aiResponse.followUps?.length || 0
        }
      };
    } else {
      // No conversation data available - fallback to batch messages
      console.log(`⚠️ No conversation data available for ${follower.instagramNickname} - using batch messages`);
      
      // Get available message batches
      const messageBatches = await prisma.messageBatch.findMany({
        where: {
          organizationId: follower.organizationId,
          isActive: true
        },
        include: {
          MessageBatchItem: {
            orderBy: { sequenceNumber: 'asc' }
          }
        }
      });

      const batchesWithMessages = messageBatches.filter(
        batch => batch.MessageBatchItem.length > 0
      );

      const randomBatch = batchesWithMessages.length > 0
        ? batchesWithMessages[Math.floor(Math.random() * batchesWithMessages.length)]
        : null;

      const newContact = await prisma.instagramContact.create({
        data: {
          organizationId: follower.organizationId,
          userId: follower.userId,
          instagramId: follower.instagramId,
          instagramNickname: follower.instagramNickname,
          avatar: follower.avatar,
          followerCount: follower.followerCount,
          isVerifiedUser: follower.isVerified,
          stage: 'new',
          priority: 3,
          status: 'pending',
          messageCount: 0,
          isIgnored: false,
          isTakeControl: false,
          isConversionLinkSent: false,
          conversationSource: 'extension',
          batchId: randomBatch?.id
        }
      });

      // Create batch messages in MessageList
      if (randomBatch && randomBatch.MessageBatchItem.length > 0) {
        const batchMessages = randomBatch.MessageBatchItem.map((item, index) => ({
          content: item.messageText,
          delayHours: index === 0 ? 0 : (item.delayMinutes / 60),
          sequenceNumber: item.sequenceNumber,
          messageType: 'EXTENSION' as const
        }));

        await createMessagesInMessageList(
          follower.organizationId,
          newContact.id,
          follower.instagramNickname,
          3,
          batchMessages,
          'EXTENSION'
        );
      }

      // Mark follower as contacted
      await prisma.instagramFollower.update({
        where: { id: follower.id },
        data: {
          status: 'contacted',
          isContacted: true,
          updatedAt: new Date()
        }
      });

      console.log(`✅ Follower processed with batch messages: ${follower.instagramNickname}`);
      return {
        success: true,
        data: {
          action: 'created_with_batch_messages',
          contactId: newContact.id,
          priority: 3,
          batchId: randomBatch?.id
        }
      };
    }

  } catch (error) {
    console.error(`💥 Error triggering follower processing: ${followerId}`, error);
    return { success: false, error: 'Internal error' };
  }
}

/**
 * Trigger processing for multiple followers by nickname
 */
export async function triggerFollowerProcessingByNicknames(nicknames: string[], organizationId: string) {
  const results = {
    processed: 0,
    errors: [] as string[],
    successes: [] as string[]
  };

  for (const nickname of nicknames) {
    try {
      // Find the follower
      const follower = await prisma.instagramFollower.findFirst({
        where: {
          organizationId,
          instagramNickname: nickname,
          status: 'pending',
          automationEnabled: true
        }
      });

      if (!follower) {
        results.errors.push(`${nickname}: Not found or already processed`);
        continue;
      }

      // Trigger processing
      const result = await triggerFollowerProcessing(follower.id);

      if (result.success) {
        results.successes.push(nickname);
        results.processed++;
      } else {
        results.errors.push(`${nickname}: ${result.error}`);
      }

      // Add small delay to prevent overwhelming the system
      await new Promise(resolve => setTimeout(resolve, 500)); // 500ms delay

    } catch (error) {
      results.errors.push(`${nickname}: ${error}`);
    }
  }

  return results;
}

/**
 * Auto-trigger processing for all pending followers in an organization
 */
export async function autoTriggerPendingFollowers(organizationId: string, limit: number = 10) {
  try {
    console.log(`🔄 Auto-triggering processing for organization: ${organizationId}`);

    // Get pending followers
    const pendingFollowers = await prisma.instagramFollower.findMany({
      where: {
        organizationId,
        status: 'pending',
        automationEnabled: true
      },
      take: limit,
      orderBy: {
        createdAt: 'asc' // Process oldest first
      }
    });

    if (pendingFollowers.length === 0) {
      console.log(`✅ No pending followers to process for organization: ${organizationId}`);
      return { success: true, processed: 0, message: 'No pending followers' };
    }

    console.log(`📋 Found ${pendingFollowers.length} pending followers to process`);

    const results = {
      processed: 0,
      errors: [] as string[],
      successes: [] as string[]
    };

    for (const follower of pendingFollowers) {
      try {
        const result = await triggerFollowerProcessing(follower.id);

        if (result.success) {
          results.successes.push(follower.instagramNickname);
          results.processed++;
        } else {
          results.errors.push(`${follower.instagramNickname}: ${result.error}`);
        }

        // Add delay between followers
        await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay

      } catch (error) {
        results.errors.push(`${follower.instagramNickname}: ${error}`);
      }
    }

    console.log(`✅ Auto-trigger completed for organization ${organizationId}:`, results);
    return { success: true, ...results };

  } catch (error) {
    console.error(`💥 Error in auto-trigger for organization ${organizationId}:`, error);
    return { success: false, error: 'Auto-trigger failed' };
  }
}

/**
 * Check if a follower needs processing
 */
export async function checkFollowerNeedsProcessing(instagramNickname: string, organizationId: string): Promise<boolean> {
  try {
    // Check if follower exists and is pending
    const follower = await prisma.instagramFollower.findFirst({
      where: {
        organizationId,
        instagramNickname,
        status: 'pending',
        automationEnabled: true
      }
    });

    if (!follower) {
      return false;
    }

    // Check if contact already exists
    const existingContact = await prisma.instagramContact.findFirst({
      where: {
        organizationId,
        instagramNickname
      }
    });

    // Needs processing if follower is pending and no contact exists
    return !existingContact;

  } catch (error) {
    console.error(`Error checking if follower needs processing: ${instagramNickname}`, error);
    return false;
  }
}

/**
 * Get processing statistics for an organization
 */
export async function getProcessingStats(organizationId: string) {
  try {
    const [
      totalFollowers,
      pendingFollowers,
      contactedFollowers,
      totalContacts,
      totalMessages,
      readyMessages
    ] = await Promise.all([
      prisma.instagramFollower.count({
        where: { organizationId }
      }),
      prisma.instagramFollower.count({
        where: {
          organizationId,
          status: 'pending',
          automationEnabled: true
        }
      }),
      prisma.instagramFollower.count({
        where: {
          organizationId,
          status: 'contacted'
        }
      }),
      prisma.instagramContact.count({
        where: { organizationId }
      }),
      prisma.messageList.count({
        where: { organizationId }
      }),
      prisma.attackList.count({
        where: {
          organizationId,
          status: 'READY'
        }
      })
    ]);

    return {
      totalFollowers,
      pendingFollowers,
      contactedFollowers,
      totalContacts,
      totalMessages,
      readyMessages,
      processingRate: totalFollowers > 0 ? (contactedFollowers / totalFollowers) * 100 : 0,
      needsProcessing: pendingFollowers > 0
    };

  } catch (error) {
    console.error(`Error getting processing stats for organization ${organizationId}:`, error);
    return null;
  }
}
