/**
 * Simple Follower Processing - NO CONVERSATION GATHERING
 * 
 * This replaces the complex conversation gathering system with a simple approach:
 * 1. Process 50 followers directly
 * 2. Create contacts with batch messages 
 * 3. Skip conversation analysis completely
 * 4. Move directly to ACTIVE status for messaging
 */

import { prisma } from '@workspace/database/client';

interface FollowerData {
  instagramNickname: string;
  isVerified: boolean;
}

interface ProcessingResult {
  success: boolean;
  processed: number;
  errors: string[];
  contacts: Array<{
    id: string;
    instagramNickname: string;
    priority: number;
    batchId?: string;
  }>;
}

/**
 * Process followers with simple system (NO conversation gathering)
 */
export async function processFollowersSimple(
  organizationId: string,
  followers: FollowerData[]
): Promise<ProcessingResult> {
  console.log(`🚀 SIMPLE PROCESSING: Processing ${followers.length} followers without conversation gathering`);
  
  const result: ProcessingResult = {
    success: true,
    processed: 0,
    errors: [],
    contacts: []
  };

  try {
    // Get available message batches
    const messageBatches = await prisma.messageBatch.findMany({
      where: {
        organizationId,
        isActive: true
      },
      include: {
        MessageBatchItem: {
          orderBy: { sequenceNumber: 'asc' }
        }
      }
    });

    const batchesWithMessages = messageBatches.filter(
      batch => batch.MessageBatchItem.length > 0
    );

    console.log(`📦 Found ${batchesWithMessages.length} active message batches`);

    for (const followerData of followers) {
      try {
        // Find the follower record
        const follower = await prisma.instagramFollower.findFirst({
          where: {
            organizationId,
            instagramNickname: followerData.instagramNickname
          }
        });

        if (!follower) {
          result.errors.push(`${followerData.instagramNickname}: Follower record not found`);
          continue;
        }

        // Check if contact already exists
        const existingContact = await prisma.instagramContact.findFirst({
          where: {
            organizationId,
            instagramNickname: followerData.instagramNickname
          }
        });

        if (existingContact) {
          // Just mark follower as contacted and update contact
          await prisma.instagramFollower.update({
            where: { id: follower.id },
            data: {
              status: 'contacted',
              isContacted: true,
              updatedAt: new Date()
            }
          });

          await prisma.instagramContact.update({
            where: { id: existingContact.id },
            data: {
              attackListStatus: 'pending',
              nextMessageAt: new Date(),
              updatedAt: new Date()
            }
          });

          result.contacts.push({
            id: existingContact.id,
            instagramNickname: followerData.instagramNickname,
            priority: existingContact.priority,
            batchId: existingContact.batchId || undefined
          });

          console.log(`✅ Updated existing contact: ${followerData.instagramNickname}`);
        } else {
          // Create new contact with batch messages
          const randomBatch = batchesWithMessages.length > 0
            ? batchesWithMessages[Math.floor(Math.random() * batchesWithMessages.length)]
            : null;

          const newContact = await prisma.instagramContact.create({
            data: {
              organizationId,
              userId: follower.userId,
              instagramId: follower.instagramId,
              instagramNickname: follower.instagramNickname,
              avatar: follower.avatar,
              followerCount: follower.followerCount,
              isVerifiedUser: follower.isVerified,
              stage: 'new',
              priority: 3, // Standard priority for new followers
              status: 'pending',
              messageCount: 0,
              isIgnored: false,
              isTakeControl: false,
              isConversionLinkSent: false,
              nextMessageAt: new Date(), // Ready for immediate messaging
              attackListStatus: 'pending',
              conversationSource: 'extension',
              batchId: randomBatch?.id
            }
          });

          // Mark follower as contacted
          await prisma.instagramFollower.update({
            where: { id: follower.id },
            data: {
              status: 'contacted',
              isContacted: true,
              updatedAt: new Date()
            }
          });

          result.contacts.push({
            id: newContact.id,
            instagramNickname: followerData.instagramNickname,
            priority: 3,
            batchId: randomBatch?.id || undefined
          });

          console.log(`✅ Created new contact: ${followerData.instagramNickname} (batch: ${randomBatch?.name || 'none'})`);
        }

        result.processed++;

      } catch (error) {
        const errorMsg = `${followerData.instagramNickname}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        result.errors.push(errorMsg);
        console.error(`❌ Error processing ${followerData.instagramNickname}:`, error);
      }
    }

    console.log(`🎉 SIMPLE PROCESSING COMPLETE: ${result.processed}/${followers.length} followers processed`);
    console.log(`📊 Created/updated ${result.contacts.length} contacts ready for messaging`);

    if (result.errors.length > 0) {
      console.log(`⚠️ ${result.errors.length} errors occurred`);
      result.success = false;
    }

    return result;

  } catch (error) {
    console.error('❌ Error in simple follower processing:', error);
    result.success = false;
    result.errors.push(`System error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return result;
  }
}

/**
 * Update extension status to ACTIVE (skip conversation gathering completely)
 */
export async function updateExtensionToActive(organizationId: string): Promise<void> {
  try {
    await prisma.chromeExtensionSettings.upsert({
      where: { organizationId },
      update: {
        extensionStatus: 'ACTIVE',
        currentActivity: 'Ready for messaging - simple processing complete',
        lastActivityAt: new Date()
      },
      create: {
        organizationId,
        extensionStatus: 'ACTIVE',
        currentActivity: 'Ready for messaging - simple processing complete',
        lastActivityAt: new Date()
      }
    });

    console.log(`✅ Extension status updated to ACTIVE for organization: ${organizationId}`);
  } catch (error) {
    console.error('❌ Error updating extension status:', error);
  }
}

/**
 * Get processing statistics
 */
export async function getSimpleProcessingStats(organizationId: string) {
  try {
    const [
      totalFollowers,
      pendingFollowers,
      contactedFollowers,
      attackListContacts
    ] = await Promise.all([
      prisma.instagramFollower.count({
        where: { organizationId }
      }),
      prisma.instagramFollower.count({
        where: {
          organizationId,
          status: 'pending',
          automationEnabled: true
        }
      }),
      prisma.instagramFollower.count({
        where: {
          organizationId,
          status: 'contacted'
        }
      }),
      prisma.instagramContact.count({
        where: {
          organizationId,
          attackListStatus: 'pending'
        }
      })
    ]);

    return {
      totalFollowers,
      pendingFollowers,
      contactedFollowers,
      attackListContacts,
      processingRate: totalFollowers > 0 ? (contactedFollowers / totalFollowers) * 100 : 0,
      isComplete: pendingFollowers === 0
    };

  } catch (error) {
    console.error(`Error getting processing stats for organization ${organizationId}:`, error);
    return null;
  }
}