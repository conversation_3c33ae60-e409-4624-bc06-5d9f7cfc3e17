'use client';

import * as React from 'react';
import { format } from 'date-fns';
import { Calendar, MoreHorizontal, RefreshCw, Trash2, Filter, Eye, Edit, Search, X, Send } from 'lucide-react';

import { But<PERSON> } from '@workspace/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@workspace/ui/components/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@workspace/ui/components/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@workspace/ui/components/dropdown-menu';
import { Badge } from '@workspace/ui/components/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@workspace/ui/components/avatar';
import { Input } from '@workspace/ui/components/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select';
import { Checkbox } from '@workspace/ui/components/checkbox';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@workspace/ui/components/dialog';
import { Textarea } from '@workspace/ui/components/textarea';
import { useToast } from '@workspace/ui/hooks/use-toast';
import { useActiveOrganization } from '~/hooks/use-active-organization';

interface AttackListItem {
  id: string;
  contactId: string;
  contactUsername: string;
  contactAvatar: string | null;
  message: string;
  messageType: 'FIRST_MESSAGE' | 'FOLLOW_UP';
  scheduledTime: Date | null;
  priority: number;
  createdAt: Date;
  updatedAt: Date;
}

const priorityLabels = {
  5: 'Highly Engaged',
  4: 'Engaged', 
  3: 'New Followers',
  2: 'Low Engaged',
  1: 'Follow-up'
};

const messageTypeColors = {
  'FIRST_MESSAGE': 'bg-blue-100 text-blue-800',
  'FOLLOW_UP': 'bg-purple-100 text-purple-800'
};

export function AttackListManager(): React.JSX.Element {
  const { toast } = useToast();
  const activeOrganization = useActiveOrganization();
  const [attackList, setAttackList] = React.useState<AttackListItem[]>([]);
  const [filteredAttackList, setFilteredAttackList] = React.useState<AttackListItem[]>([]);
  const [isLoading, setIsLoading] = React.useState(false);
  const [metadata, setMetadata] = React.useState<any>(null);

  // Filter states
  const [searchTerm, setSearchTerm] = React.useState('');
  const [priorityFilter, setPriorityFilter] = React.useState<string>('all');
  const [messageTypeFilter, setMessageTypeFilter] = React.useState<string>('all');

  // Selection states
  const [selectedItems, setSelectedItems] = React.useState<Set<string>>(new Set());
  const [selectAll, setSelectAll] = React.useState(false);

  // Dialog states
  const [showMessageDialog, setShowMessageDialog] = React.useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = React.useState(false);
  const [selectedContact, setSelectedContact] = React.useState<AttackListItem | null>(null);

  // Load attack list data
  React.useEffect(() => {
    loadAttackList();
  }, []);

  // Filter attack list when filters change
  React.useEffect(() => {
    let filtered = attackList;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.contactUsername.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Priority filter
    if (priorityFilter !== 'all') {
      filtered = filtered.filter(item => item.priority === parseInt(priorityFilter));
    }

    // Message type filter
    if (messageTypeFilter !== 'all') {
      filtered = filtered.filter(item => item.messageType === messageTypeFilter);
    }

    setFilteredAttackList(filtered);
  }, [attackList, searchTerm, priorityFilter, messageTypeFilter]);

  const loadAttackList = async () => {
    try {
      setIsLoading(true);

      const response = await fetch(`/api/chrome-extension/attack-list-v2?organizationId=${activeOrganization.id}&limit=50`);
      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to load attack list');
      }

      // Convert date strings back to Date objects
      const attackListData = result.data.messages.map((item: any) => ({
        ...item,
        scheduledTime: item.scheduledTime ? new Date(item.scheduledTime) : null,
        createdAt: new Date(item.createdAt),
        updatedAt: new Date(item.updatedAt)
      }));

      setAttackList(attackListData);
      setMetadata(result.data.metadata);
    } catch (error) {
      console.error('Error loading attack list:', error);
      toast({
        title: 'Error',
        description: 'Failed to load attack list',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const markMessageSent = async (id: string) => {
    try {
      const response = await fetch(`/api/chrome-extension/mark-sent-v2`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          messageId: id,
          organizationId: activeOrganization.id 
        })
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to mark message as sent');
      }

      // Remove from local state
      setAttackList(prev => prev.filter(item => item.id !== id));

      toast({
        title: 'Success',
        description: result.data.nextMessage 
          ? `Message sent! Next message: ${result.data.nextMessage.contactUsername}`
          : 'Message sent successfully!'
      });
    } catch (error) {
      console.error('Error marking message as sent:', error);
      toast({
        title: 'Error',
        description: 'Failed to mark message as sent',
        variant: 'destructive'
      });
    }
  };

  const refreshAttackList = () => {
    loadAttackList();
  };

  // Selection handlers
  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedItems(new Set(filteredAttackList.map(item => item.id)));
    } else {
      setSelectedItems(new Set());
    }
  };

  const handleSelectItem = (id: string, checked: boolean) => {
    const newSelected = new Set(selectedItems);
    if (checked) {
      newSelected.add(id);
    } else {
      newSelected.delete(id);
    }
    setSelectedItems(newSelected);
    setSelectAll(newSelected.size === filteredAttackList.length);
  };

  // Bulk operations
  const handleBulkDelete = async () => {
    try {
      setIsLoading(true);
      const promises = Array.from(selectedItems).map(id =>
        fetch(`/api/chrome-extension/mark-sent-v2`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ messageId: id, organizationId: activeOrganization.id })
        })
      );

      await Promise.all(promises);

      setAttackList(prev => prev.filter(item => !selectedItems.has(item.id)));
      setSelectedItems(new Set());
      setSelectAll(false);
      setShowDeleteDialog(false);

      toast({
        title: 'Success',
        description: `Marked ${selectedItems.size} messages as sent`
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to mark messages as sent',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteAll = async () => {
    try {
      setIsLoading(true);
      const promises = attackList.map(item =>
        fetch(`/api/chrome-extension/mark-sent-v2`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ messageId: item.id, organizationId: activeOrganization.id })
        })
      );

      await Promise.all(promises);

      setAttackList([]);
      setSelectedItems(new Set());
      setSelectAll(false);

      toast({
        title: 'Success',
        description: 'Marked all messages as sent'
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to mark all messages as sent',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Attack List - Chrome Extension Messages</CardTitle>
              <CardDescription>
                Messages ready to be sent by the Chrome Extension
                {filteredAttackList.length !== attackList.length && (
                  <span className="ml-2 text-sm">
                    (Showing {filteredAttackList.length} of {attackList.length})
                  </span>
                )}
              </CardDescription>
            </div>
            <div className="flex gap-2">
              {selectedItems.size > 0 && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowDeleteDialog(true)}
                    disabled={isLoading}
                  >
                    <Send className="mr-2 h-4 w-4" />
                    Mark Sent ({selectedItems.size})
                  </Button>
                </>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={handleDeleteAll}
                disabled={isLoading || attackList.length === 0}
              >
                <Send className="mr-2 h-4 w-4" />
                Mark All Sent
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={refreshAttackList}
                disabled={isLoading}
              >
                <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-4 pt-4 border-t">
            <div className="flex items-center gap-2">
              <Search className="h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-48"
              />
              {searchTerm && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSearchTerm('')}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>

            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priorities</SelectItem>
                  <SelectItem value="5">5 - Highly Engaged</SelectItem>
                  <SelectItem value="4">4 - Engaged</SelectItem>
                  <SelectItem value="3">3 - New Followers</SelectItem>
                  <SelectItem value="2">2 - Low Engaged</SelectItem>
                  <SelectItem value="1">1 - Follow-up</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <Select value={messageTypeFilter} onValueChange={setMessageTypeFilter}>
                <SelectTrigger className="w-36">
                  <SelectValue placeholder="Message Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="FIRST_MESSAGE">First Message</SelectItem>
                  <SelectItem value="FOLLOW_UP">Follow Up</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {metadata && (
            <div className="mb-4 p-4 bg-muted/50 rounded-lg">
              <div className="text-sm text-muted-foreground">
                <strong>Total messages ready:</strong> {metadata.totalMessages || attackList.length}
              </div>
              <div className="flex gap-4 mt-2 text-xs">
                <div>
                  <Badge variant="outline" className="mr-1">First Messages</Badge>
                  {attackList.filter(item => item.messageType === 'FIRST_MESSAGE').length}
                </div>
                <div>
                  <Badge variant="outline" className="mr-1">Follow Ups</Badge>
                  {attackList.filter(item => item.messageType === 'FOLLOW_UP').length}
                </div>
              </div>
            </div>
          )}
          {filteredAttackList.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              {attackList.length === 0
                ? "No messages ready. Messages will appear here when contacts are ready for the Chrome Extension."
                : "No messages match the current filters."
              }
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[50px]">
                    <Checkbox
                      checked={selectAll}
                      onCheckedChange={handleSelectAll}
                      aria-label="Select all"
                    />
                  </TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Message Type</TableHead>
                  <TableHead>Message</TableHead>
                  <TableHead>Scheduled</TableHead>
                  <TableHead className="w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAttackList.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedItems.has(item.id)}
                        onCheckedChange={(checked) => handleSelectItem(item.id, checked as boolean)}
                        aria-label={`Select ${item.contactUsername}`}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={item.contactAvatar || undefined} />
                          <AvatarFallback>
                            {item.contactUsername.slice(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <span className="font-medium">@{item.contactUsername}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {item.priority} - {priorityLabels[item.priority as keyof typeof priorityLabels]}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={messageTypeColors[item.messageType]}>
                        {item.messageType === 'FIRST_MESSAGE' ? 'First Message' : 'Follow Up'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="max-w-xs truncate" title={item.message}>
                        {item.message}
                      </div>
                    </TableCell>
                    <TableCell>
                      {item.scheduledTime ? format(item.scheduledTime, 'MMM dd, yyyy HH:mm') : 'Ready now'}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedContact(item);
                            setShowMessageDialog(true);
                          }}
                          title="View message details"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => markMessageSent(item.id)}
                          title="Mark as sent"
                          className="text-green-600 hover:text-green-700"
                        >
                          <Send className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* View Message Dialog */}
      <Dialog open={showMessageDialog} onOpenChange={setShowMessageDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Message Details for @{selectedContact?.contactUsername}</DialogTitle>
            <DialogDescription>
              Review the message that will be sent to this contact
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {selectedContact && (
              <div className="space-y-3">
                <div className="p-4 bg-muted/50 rounded-lg">
                  <div className="text-sm text-muted-foreground mb-2">Contact Info</div>
                  <div className="flex items-center gap-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={selectedContact.contactAvatar || undefined} />
                      <AvatarFallback>
                        {selectedContact.contactUsername.slice(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">@{selectedContact.contactUsername}</div>
                      <div className="text-sm text-muted-foreground">
                        Priority: {selectedContact.priority} - {priorityLabels[selectedContact.priority as keyof typeof priorityLabels]}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Type: {selectedContact.messageType === 'FIRST_MESSAGE' ? 'First Message' : 'Follow Up'}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="text-sm font-medium">Message to Send:</div>
                  <div className="p-4 bg-background border rounded-lg">
                    <div className="whitespace-pre-wrap">{selectedContact.message}</div>
                  </div>
                  {selectedContact.scheduledTime && (
                    <div className="text-xs text-muted-foreground">
                      Scheduled for: {format(selectedContact.scheduledTime, 'PPpp')}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button onClick={() => setShowMessageDialog(false)}>Close</Button>
            {selectedContact && (
              <Button 
                onClick={() => {
                  markMessageSent(selectedContact.id);
                  setShowMessageDialog(false);
                }}
                className="bg-green-600 hover:bg-green-700"
              >
                <Send className="mr-2 h-4 w-4" />
                Mark as Sent
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Mark Selected Messages as Sent</DialogTitle>
            <DialogDescription>
              Are you sure you want to mark {selectedItems.size} messages as sent?
              This will remove them from the attack list.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleBulkDelete} disabled={isLoading} className="bg-green-600 hover:bg-green-700">
              {isLoading ? 'Processing...' : 'Mark as Sent'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
