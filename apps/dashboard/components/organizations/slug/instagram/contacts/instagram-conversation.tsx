'use client';

import * as React from 'react';
import { format } from 'date-fns';
import { Send, Image, FileAudio, Paperclip, FileText, Info, Clock } from 'lucide-react';
import { useParams } from 'next/navigation';

import { Avatar, AvatarFallback, AvatarImage } from '@workspace/ui/components/avatar';
import { Button } from '@workspace/ui/components/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Input } from '@workspace/ui/components/input';
import { ScrollArea } from '@workspace/ui/components/scroll-area';
import { Separator } from '@workspace/ui/components/separator';
import { useToast } from '@workspace/ui/hooks/use-toast';
import { replaceOrgSlug, routes } from '@workspace/routes';
import Link from 'next/link';

import { InstagramContactDto } from '~/types/dtos/instagram-contact-dto';
import { InstagramMessageDto } from '~/types/dtos/instagram-message-dto';
import { sendManualMessage } from '~/actions/instagram/send-manual-message';
import { TranscribeMediaButton } from './transcribe-media-button';
import { AiDebugInfo } from '../shared/ai-debug-info';
import { useAuthContext } from '~/hooks/use-auth-context';


interface InstagramConversationProps {
  contact: InstagramContactDto;
  messages: InstagramMessageDto[];
}

export function InstagramConversation({ contact, messages }: InstagramConversationProps): React.JSX.Element {
  const params = useParams<{ slug: string }>();
  const { toast } = useToast();
  const { session } = useAuthContext();
  const [newMessage, setNewMessage] = React.useState('');
  const [isLoading, setIsLoading] = React.useState(false);
  const messagesEndRef = React.useRef<HTMLDivElement>(null);
  const scrollAreaRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    // Use setTimeout to ensure DOM is updated before scrolling
    const timer = setTimeout(() => {
      scrollToBottom();
    }, 100);

    return () => clearTimeout(timer);
  }, [messages]);

  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'end',
        inline: 'nearest'
      });
    }
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newMessage.trim()) return;

    setIsLoading(true);
    try {
      await sendManualMessage({
        contactId: contact.id,
        message: newMessage
      });

      setNewMessage('');
      toast({
        title: 'Message sent',
        description: 'Your message has been sent successfully'
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to send message',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="flex flex-col h-full">
      <CardHeader className="flex-shrink-0 pb-3">
        <div className="flex justify-between items-center">
          <CardTitle>Conversation</CardTitle>
          <div className="flex items-center gap-2">
            {contact.followUps.length > 0 && (
              <Link
                href={replaceOrgSlug(routes.dashboard.organizations.slug.instagram.MessageList, params.slug)}
                className="inline-flex"
              >
                <Button variant="outline" size="sm">
                  <Clock className="h-4 w-4 mr-2" />
                  View Message List
                </Button>
              </Link>
            )}
            {contact.isTakeControl && (
              <div className="flex items-center gap-2 text-sm text-warning border border-warning rounded-md px-2 py-1">
                <span>Manual Control Active</span>
              </div>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="flex-1 p-0 overflow-hidden">
        <ScrollArea ref={scrollAreaRef} className="h-full">
          <div className="flex flex-col gap-4 p-4 pb-8">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.isFromUser ? 'justify-start' : 'justify-end'}`}
              >
                <div className={`flex gap-3 max-w-[80%] ${message.isFromUser ? 'flex-row' : 'flex-row-reverse'}`}>
                  {message.isFromUser ? (
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={contact.avatar || undefined} alt={contact.instagramUsername} />
                      <AvatarFallback>{contact.instagramUsername.substring(0, 2).toUpperCase()}</AvatarFallback>
                    </Avatar>
                  ) : (
                    <Avatar className="h-8 w-8">
                      <AvatarFallback>BOT</AvatarFallback>
                    </Avatar>
                  )}
                  <div className="flex flex-col gap-1">
                    <div
                      className={`rounded-lg p-3 ${message.isFromUser
                        ? 'bg-muted text-foreground'
                        : 'bg-primary text-primary-foreground'
                        }`}
                    >
                      <p className="text-sm">{message.content}</p>

                      {message.mediaUrl && (
                        <div className="mt-2">
                          <div className="flex justify-between items-start mb-2">
                            <div className="flex-1">
                              {message.mediaType === 'image' ? (
                                <div className="rounded overflow-hidden">
                                  <img
                                    src={message.mediaUrl}
                                    alt="Shared image"
                                    className="max-w-full h-auto max-h-60 object-contain"
                                  />
                                </div>
                              ) : message.mediaType === 'audio' ? (
                                <div className="flex items-center gap-2">
                                  <FileAudio className="h-4 w-4" />
                                  <span className="text-xs">Voice message</span>
                                </div>
                              ) : (
                                <div className="flex items-center gap-2">
                                  <Paperclip className="h-4 w-4" />
                                  <span className="text-xs">Attachment</span>
                                </div>
                              )}
                            </div>
                            {message.isFromUser && !message.mediaDescription && (
                              <div className="ml-2">
                                <TranscribeMediaButton
                                  messageId={message.id}
                                />
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {message.mediaDescription && (
                        <div className="mt-2 text-xs border-l-2 pl-2 py-1 bg-opacity-10 bg-gray-100 dark:bg-gray-800">
                          <div className="flex items-center gap-1 text-muted-foreground mb-1">
                            <FileText className="h-3 w-3" />
                            <span className="font-medium">AI Transcription:</span>
                          </div>
                          <p className="whitespace-pre-wrap">{message.mediaDescription}</p>
                        </div>
                      )}

                      {!message.isFromUser && message.debugInfo && (
                        <AiDebugInfo
                          debugInfo={message.debugInfo}
                          isSaasAdmin={session?.user?.email === '<EMAIL>'}
                        />
                      )}
                    </div>
                    <div className="flex items-center gap-1">
                      <span className="text-xs text-muted-foreground">
                        {format(message.timestamp, 'MMM dd, HH:mm')}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>
      </CardContent>
      <Separator />
      <CardFooter className="p-4 flex-shrink-0">
        <form onSubmit={handleSendMessage} className="flex w-full gap-2">
          <Input
            placeholder="Type your message..."
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            disabled={isLoading || !contact.isTakeControl}
            className="flex-1"
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage(e);
              }
            }}
          />
          <Button
            type="submit"
            size="icon"
            disabled={isLoading || !contact.isTakeControl || !newMessage.trim()}
          >
            <Send className="h-4 w-4" />
          </Button>
        </form>
      </CardFooter>
    </Card>
  );
}
