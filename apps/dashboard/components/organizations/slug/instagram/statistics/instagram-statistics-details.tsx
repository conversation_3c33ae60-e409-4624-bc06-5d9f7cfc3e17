'use client';

import * as React from 'react';
import { 
  ShieldCheckIcon, 
  UsersIcon, 
  ClockIcon, 
  AlertTriangleIcon,
  TrendingUpIcon,
  MessageSquareIcon,
  ImageIcon,
  MicIcon,
  FileTextIcon
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Badge } from '@workspace/ui/components/badge';
import { Progress } from '@workspace/ui/components/progress';
import { Separator } from '@workspace/ui/components/separator';

import { InstagramStatisticsDto } from '~/types/dtos/instagram-statistics-dto';

interface InstagramStatisticsDetailsProps {
  statistics: InstagramStatisticsDto;
}

export function InstagramStatisticsDetails({ 
  statistics 
}: InstagramStatisticsDetailsProps): React.JSX.Element {
  const formatNumber = (num: number): string => {
    return num.toLocaleString();
  };

  const formatPercentage = (num: number): string => {
    return num.toFixed(1) + '%';
  };

  const formatDays = (days: number): string => {
    if (days < 1) {
      return `${(days * 24).toFixed(1)} hours`;
    }
    return `${days.toFixed(1)} days`;
  };

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">Detailed Analytics</h3>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Quality Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShieldCheckIcon className="h-5 w-5" />
              Contact Quality
            </CardTitle>
            <CardDescription>
              Quality indicators of your contacts
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm">Verified Users</span>
              <Badge variant="outline">
                {formatNumber(statistics.qualityMetrics.verifiedUsers)}
              </Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Avg Follower Count</span>
              <Badge variant="secondary">
                {formatNumber(Math.round(statistics.qualityMetrics.averageFollowerCount))}
              </Badge>
            </div>
            <Separator />
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Business → User</span>
                <span>{formatNumber(statistics.qualityMetrics.businessFollowsUser)}</span>
              </div>
              <Progress 
                value={(statistics.qualityMetrics.businessFollowsUser / statistics.peopleContacted) * 100} 
                className="h-2" 
              />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>User → Business</span>
                <span>{formatNumber(statistics.qualityMetrics.userFollowsBusiness)}</span>
              </div>
              <Progress 
                value={(statistics.qualityMetrics.userFollowsBusiness / statistics.peopleContacted) * 100} 
                className="h-2" 
              />
            </div>
          </CardContent>
        </Card>

        {/* Message Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUpIcon className="h-5 w-5" />
              Message Performance
            </CardTitle>
            <CardDescription>
              Effectiveness of messaging campaigns
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm">Success Rate</span>
              <Badge variant={statistics.followUpSuccessRate > 20 ? 'default' : 'secondary'}>
                {formatPercentage(statistics.followUpSuccessRate)}
              </Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Avg Time to Conversion</span>
              <Badge variant="outline">
                {formatDays(statistics.averageTimeToConversion)}
              </Badge>
            </div>
            <Separator />
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Total Messages Sent</span>
                <span>{formatNumber(statistics.approxFollowUpAmount)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Contacts Messaged</span>
                <span>{formatNumber(statistics.peopleFollowedUp)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Avg per Contact</span>
                <span>
                  {statistics.peopleFollowedUp > 0
                    ? (statistics.approxFollowUpAmount / statistics.peopleFollowedUp).toFixed(1)
                    : '0'
                  }
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Media Usage */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquareIcon className="h-5 w-5" />
              Media Usage
            </CardTitle>
            <CardDescription>
              Types of content in conversations
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <FileTextIcon className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Text Messages</span>
                </div>
                <Badge variant="outline">
                  {formatNumber(statistics.mediaUsage.textMessages)}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <MicIcon className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Audio Messages</span>
                </div>
                <Badge variant="outline">
                  {formatNumber(statistics.mediaUsage.audioMessages)}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <ImageIcon className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Image Messages</span>
                </div>
                <Badge variant="outline">
                  {formatNumber(statistics.mediaUsage.imageMessages)}
                </Badge>
              </div>
            </div>
            <Separator />
            <div className="text-xs text-muted-foreground">
              Total: {formatNumber(
                statistics.mediaUsage.textMessages + 
                statistics.mediaUsage.audioMessages + 
                statistics.mediaUsage.imageMessages
              )} messages
            </div>
          </CardContent>
        </Card>

        {/* 24-Hour Window Utilization */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ClockIcon className="h-5 w-5" />
              24-Hour Window
            </CardTitle>
            <CardDescription>
              Message timing optimization
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Within 24h Window</span>
                <span>{formatNumber(statistics.windowUtilization.within24Hours)}</span>
              </div>
              <Progress 
                value={
                  statistics.windowUtilization.within24Hours + statistics.windowUtilization.outside24Hours > 0
                    ? (statistics.windowUtilization.within24Hours / 
                       (statistics.windowUtilization.within24Hours + statistics.windowUtilization.outside24Hours)) * 100
                    : 0
                } 
                className="h-2" 
              />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Outside 24h Window</span>
                <span>{formatNumber(statistics.windowUtilization.outside24Hours)}</span>
              </div>
              <div className="text-xs text-muted-foreground">
                Handled by Chrome Extension
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Disqualification Reasons */}
        {Object.keys(statistics.disqualificationReasons).length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangleIcon className="h-5 w-5" />
                Disqualification Reasons
              </CardTitle>
              <CardDescription>
                Why contacts were disqualified
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {Object.entries(statistics.disqualificationReasons)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5)
                .map(([reason, count]) => (
                  <div key={reason} className="flex justify-between items-center">
                    <span className="text-sm truncate flex-1 mr-2">{reason}</span>
                    <Badge variant="destructive">
                      {formatNumber(count)}
                    </Badge>
                  </div>
                ))}
            </CardContent>
          </Card>
        )}

        {/* Response Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UsersIcon className="h-5 w-5" />
              Response Metrics
            </CardTitle>
            <CardDescription>
              Contact engagement analysis
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm">Response Rate</span>
              <Badge variant={statistics.responseRate > 70 ? 'default' : 'secondary'}>
                {formatPercentage(statistics.responseRate)}
              </Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Zero Response</span>
              <Badge variant="destructive">
                {formatNumber(statistics.peopleWithZeroResponse)}
              </Badge>
            </div>
            <Separator />
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Avg Messages/Contact</span>
                <span>{statistics.averageMessagesPerContact.toFixed(1)}</span>
              </div>
              <Progress 
                value={Math.min((statistics.averageMessagesPerContact / 10) * 100, 100)} 
                className="h-2" 
              />
              <div className="text-xs text-muted-foreground">
                Target: 5+ messages per contact
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
