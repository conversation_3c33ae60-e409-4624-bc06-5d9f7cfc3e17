'use client';

import * as React from 'react';
import {
  UsersIcon,
  MessageSquareIcon,
  CheckCircleIcon,
  TrendingUpIcon,
  HeartIcon,
  RepeatIcon,
  PercentIcon,
  UserCheckIcon,
  InstagramIcon,
  ClockIcon,
  BarChart3Icon
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Badge } from '@workspace/ui/components/badge';

import { InstagramStatisticsDto } from '~/types/dtos/instagram-statistics-dto';

interface InstagramStatisticsOverviewProps {
  statistics: InstagramStatisticsDto;
}

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ComponentType<{ className?: string }>;
  description?: string;
  badge?: {
    text: string;
    variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  };
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

function StatCard({ title, value, icon: Icon, description, badge, trend }: StatCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {description && (
          <p className="text-xs text-muted-foreground mt-1">{description}</p>
        )}
        <div className="flex items-center gap-2 mt-2">
          {badge && (
            <Badge variant={badge.variant || 'secondary'} className="text-xs">
              {badge.text}
            </Badge>
          )}
          {trend && (
            <div className={`flex items-center text-xs ${trend.isPositive ? 'text-green-600' : 'text-red-600'
              }`}>
              <TrendingUpIcon className={`h-3 w-3 mr-1 ${!trend.isPositive ? 'rotate-180' : ''
                }`} />
              {trend.value.toFixed(1)}%
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export function InstagramStatisticsOverview({
  statistics
}: InstagramStatisticsOverviewProps): React.JSX.Element {
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatPercentage = (num: number): string => {
    return num.toFixed(1) + '%';
  };

  return (
    <div className="space-y-6">
      {/* Core Metrics */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Core Metrics</h3>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Followers"
            value={formatNumber(statistics.followers)}
            icon={InstagramIcon}
            description="Your Instagram followers"
            badge={{
              text: statistics.followers > 0 ? 'Live Data' : 'No Data',
              variant: statistics.followers > 0 ? 'default' : 'destructive'
            }}
          />
          <StatCard
            title="People Contacted"
            value={formatNumber(statistics.peopleContacted)}
            icon={UsersIcon}
            description="Total unique contacts"
          />
          <StatCard
            title="People Converted"
            value={formatNumber(statistics.peopleConverted)}
            icon={CheckCircleIcon}
            description="Reached conversion stage"
            badge={{
              text: formatPercentage(statistics.percentageConverted),
              variant: statistics.percentageConverted > 10 ? 'default' : 'secondary'
            }}
          />
          <StatCard
            title="People Engaged"
            value={formatNumber(statistics.peopleEngaged)}
            icon={HeartIcon}
            description="Active in conversations"
            badge={{
              text: formatPercentage(statistics.percentageEngaged),
              variant: statistics.percentageEngaged > 30 ? 'default' : 'secondary'
            }}
          />
        </div>
      </div>

      {/* Performance Metrics */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Performance Metrics</h3>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Form Sent"
            value={formatNumber(statistics.peopleFormSent)}
            icon={MessageSquareIcon}
            description="Forms successfully sent"
            badge={{
              text: formatPercentage(statistics.percentageFormSent),
              variant: statistics.percentageFormSent > 20 ? 'default' : 'secondary'
            }}
          />
          <StatCard
            title="Response Rate"
            value={formatPercentage(statistics.responseRate)}
            icon={PercentIcon}
            description="People who responded"
            badge={{
              text: statistics.peopleWithZeroResponse > 0 ? `${statistics.peopleWithZeroResponse} no response` : 'All responded',
              variant: statistics.responseRate > 70 ? 'default' : 'secondary'
            }}
          />
          <StatCard
            title="Messages Sent"
            value={formatNumber(statistics.peopleFollowedUp)}
            icon={RepeatIcon}
            description="Contacts messaged"
            badge={{
              text: `${statistics.approxFollowUpAmount} total`,
              variant: 'outline'
            }}
          />
          <StatCard
            title="Avg Messages/Contact"
            value={statistics.averageMessagesPerContact.toFixed(1)}
            icon={BarChart3Icon}
            description="Conversation depth"
            badge={{
              text: statistics.averageMessagesPerContact > 5 ? 'High engagement' : 'Standard',
              variant: statistics.averageMessagesPerContact > 5 ? 'default' : 'secondary'
            }}
          />
        </div>
      </div>

      {/* Chrome Extension Metrics */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Chrome Extension</h3>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Extension Messages Sent"
            value={formatNumber(statistics.chromeExtension.followUpsSent)}
            icon={CheckCircleIcon}
            description="Messages sent via Chrome Extension"
            badge={{
              text: formatPercentage(statistics.chromeExtension.successRate),
              variant: statistics.chromeExtension.successRate > 80 ? 'default' : 'secondary'
            }}
          />
          <StatCard
            title="Pending Messages"
            value={formatNumber(statistics.chromeExtension.followUpsPending)}
            icon={ClockIcon}
            description="Waiting to be sent"
            badge={{
              text: statistics.chromeExtension.followUpsPending > 0 ? 'Active' : 'None',
              variant: statistics.chromeExtension.followUpsPending > 0 ? 'default' : 'outline'
            }}
          />
          <StatCard
            title="Failed Messages"
            value={formatNumber(statistics.chromeExtension.followUpsFailed)}
            icon={UserCheckIcon}
            description="Failed to send"
            badge={{
              text: statistics.chromeExtension.followUpsFailed > 0 ? 'Needs attention' : 'All good',
              variant: statistics.chromeExtension.followUpsFailed > 0 ? 'destructive' : 'default'
            }}
          />
          <StatCard
            title="Avg Response Time"
            value={`${statistics.chromeExtension.averageResponseTime.toFixed(0)}m`}
            icon={ClockIcon}
            description="Extension response time"
            badge={{
              text: statistics.chromeExtension.lastSyncTime ? 'Synced' : 'Not synced',
              variant: statistics.chromeExtension.lastSyncTime ? 'default' : 'destructive'
            }}
          />
        </div>
      </div>
    </div>
  );
}
