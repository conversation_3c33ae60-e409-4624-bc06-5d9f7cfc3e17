'use client';

import * as React from 'react';
import { Co<PERSON>, RefreshCw } from 'lucide-react';

import { Button } from '@workspace/ui/components/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Input } from '@workspace/ui/components/input';
import { useToast } from '@workspace/ui/hooks/use-toast';
import { createApiKey } from '~/actions/api-keys/create-api-key';

export function ApiKeySection(): React.JSX.Element {
  const { toast } = useToast();
  const [apiKey, setApiKey] = React.useState<string | null>(null);
  const [isGenerating, setIsGenerating] = React.useState(false);

  const handleGenerateApiKey = async () => {
    try {
      setIsGenerating(true);
      const result = await createApiKey({
        description: 'Instagram Chrome Extension',
        neverExpires: true
      });

      if (result && !result.validationErrors && !result.serverError && result.data) {
        setApiKey(result.data.apiKey || 'API key generated');
        toast({
          title: 'API Key Generated',
          description: 'Your new API key has been generated successfully.'
        });
      } else {
        toast({
          title: 'Error',
          description: 'Failed to generate API key',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error generating API key:', error);
      toast({
        title: 'Error',
        description: 'Failed to generate API key',
        variant: 'destructive'
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCopyApiKey = () => {
    if (apiKey) {
      navigator.clipboard.writeText(apiKey);
      toast({
        title: 'API Key Copied',
        description: 'The API key has been copied to your clipboard.'
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Chrome Extension API Key</CardTitle>
        <CardDescription>
          Generate an API key for the Instagram Chrome Extension to authenticate with the backend.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {apiKey ? (
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Input
                value={apiKey}
                readOnly
                className="font-mono"
              />
              <Button
                variant="outline"
                size="icon"
                onClick={handleCopyApiKey}
                title="Copy API Key"
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              This API key will only be shown once. Make sure to copy it now and store it securely.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Generate an API key to use with the Instagram Chrome Extension.
            </p>

            <Button
              onClick={handleGenerateApiKey}
              disabled={isGenerating}
              className="w-full"
            >
              {isGenerating ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                'Generate API Key'
              )}
            </Button>
          </div>
        )}

        <div className="rounded-md bg-muted p-4">
          <h4 className="mb-2 text-sm font-medium">How to use this API key</h4>
          <ol className="list-decimal pl-4 text-sm text-muted-foreground">
            <li className="mb-1">Install the Instagram Chrome Extension</li>
            <li className="mb-1">Open the extension and go to API Settings</li>
            <li className="mb-1">Enter the API key and save</li>
            <li>The extension will now automatically send messages</li>
          </ol>
        </div>
      </CardContent>
    </Card>
  );
}
