'use client';

import * as React from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import { z } from 'zod';

import { Button } from '@workspace/ui/components/button';
import { Badge } from '@workspace/ui/components/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@workspace/ui/components/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@workspace/ui/components/form';
import { Input } from '@workspace/ui/components/input';
import { Switch } from '@workspace/ui/components/switch';
import { Slider } from '@workspace/ui/components/slider';
import { useToast } from '@workspace/ui/hooks/use-toast';

import { InstagramSettingsDto } from '~/types/dtos/instagram-settings-dto';
import { saveInstagramSettings } from '~/actions/instagram/save-instagram-settings';
import { InstagramConnect } from './instagram-connect';

const instagramSettingsSchema = z.object({
  isBotEnabled: z.boolean(),
  responseTimeRange: z.array(z.number()).length(2).refine(
    ([min, max]) => min < max,
    {
      message: 'Minimum response time must be less than maximum response time'
    }
  ),
  messageDelayRange: z.array(z.number()).length(2).refine(
    ([min, max]) => min < max,
    {
      message: 'Minimum message delay must be less than maximum message delay'
    }
  ),
  autoCleanupEnabled: z.boolean(),
  followUpCleanupDays: z.number().min(1).max(365)
});

type InstagramSettingsFormValues = z.infer<typeof instagramSettingsSchema>;

interface InstagramSettingsFormProps {
  settings: InstagramSettingsDto | null;
}

export function InstagramSettingsForm({ settings }: InstagramSettingsFormProps): React.JSX.Element {
  const { toast } = useToast();
  const router = useRouter();
  const [isLoading, setIsLoading] = React.useState(false);

  const form = useForm<InstagramSettingsFormValues>({
    resolver: zodResolver(instagramSettingsSchema),
    defaultValues: {
      isBotEnabled: settings?.isBotEnabled || false,
      responseTimeRange: [
        settings?.minResponseTime || 30,
        settings?.maxResponseTime || 50
      ],
      messageDelayRange: [
        settings?.messageDelayMin || 3,
        settings?.messageDelayMax || 5
      ],
      autoCleanupEnabled: settings?.autoCleanupEnabled ?? true,
      followUpCleanupDays: settings?.followUpCleanupDays || 30
    }
  });

  const onSubmit = async (values: InstagramSettingsFormValues) => {
    setIsLoading(true);
    try {
      await saveInstagramSettings({
        id: settings?.id,
        isBotEnabled: values.isBotEnabled,
        minResponseTime: values.responseTimeRange[0],
        maxResponseTime: values.responseTimeRange[1],
        messageDelayMin: values.messageDelayRange[0],
        messageDelayMax: values.messageDelayRange[1],
        autoCleanupEnabled: values.autoCleanupEnabled,
        followUpCleanupDays: values.followUpCleanupDays
      });

      toast({
        title: 'Settings saved',
        description: 'Your Instagram settings have been saved successfully'
      });

      // Refresh the router to update the UI immediately
      router.refresh();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save Instagram settings',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const responseTimeRange = form.watch('responseTimeRange');
  const messageDelayRange = form.watch('messageDelayRange');

  const handleDisconnect = async () => {
    try {
      // Clear Instagram connection by updating settings
      await saveInstagramSettings({
        id: settings?.id,
        isBotEnabled: false, // Disable bot when disconnecting
        minResponseTime: settings?.minResponseTime || 30,
        maxResponseTime: settings?.maxResponseTime || 50,
        messageDelayMin: settings?.messageDelayMin || 3,
        messageDelayMax: settings?.messageDelayMax || 5,
        autoCleanupEnabled: settings?.autoCleanupEnabled ?? true,
        followUpCleanupDays: settings?.followUpCleanupDays || 30,
        // Clear Instagram data
        instagramToken: '',
        instagramAccountId: '',
        instagramUsername: '',
        instagramName: '',
        instagramProfilePicture: '',
        isConnected: false
      });

      // Refresh the page to update the UI
      router.refresh();
    } catch (error) {
      throw error;
    }
  };

  return (
    <div className="space-y-6">
      {/* Instagram Account Connection */}
      <InstagramConnect
        isConnected={settings?.isConnected}
        accountInfo={{
          username: settings?.instagramUsername,
          name: settings?.instagramName,
          profilePicture: settings?.instagramProfilePicture,
          accountId: settings?.instagramAccountId
        }}
        tokenExpiresAt={settings?.tokenExpiresAt}
        onDisconnect={handleDisconnect}
      />

      {/* Bot Settings Form */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <Card>
            <CardHeader>
              <CardTitle>Instagram Bot Settings</CardTitle>
              <CardDescription>
                Configure how your Instagram bot operates
                {!settings?.isConnected && (
                  <span className="block text-orange-600 mt-1">
                    Connect your Instagram account first to enable bot functionality
                  </span>
                )}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
            <FormField
              control={form.control}
              name="isBotEnabled"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Enable Bot</FormLabel>
                    <FormDescription>
                      Turn the Instagram bot on or off
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="responseTimeRange"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Response Time (seconds)</FormLabel>
                  <FormControl>
                    <Slider
                      min={5}
                      max={120}
                      step={1}
                      value={field.value}
                      onValueChange={field.onChange}
                      className="py-4"
                    />
                  </FormControl>
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>Min: {responseTimeRange[0]} seconds</span>
                    <span>Max: {responseTimeRange[1]} seconds</span>
                  </div>
                  <FormDescription>
                    Set the minimum and maximum response time to make the bot feel more human-like
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="messageDelayRange"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Message Delay (seconds)</FormLabel>
                  <FormControl>
                    <Slider
                      min={1}
                      max={30}
                      step={1}
                      value={field.value}
                      onValueChange={field.onChange}
                      className="py-4"
                    />
                  </FormControl>
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>Min: {messageDelayRange[0]} seconds</span>
                    <span>Max: {messageDelayRange[1]} seconds</span>
                  </div>
                  <FormDescription>
                    Set the delay between multiple messages when the bot sends more than one message
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Message Cleanup Settings</h3>
                <Badge variant="outline">System Messages</Badge>
              </div>

              <FormField
                control={form.control}
                name="autoCleanupEnabled"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Auto Cleanup</FormLabel>
                      <FormDescription>
                        Automatically remove old messages after the specified number of days
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="followUpCleanupDays"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Cleanup After (Days)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        max="365"
                        placeholder="30"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 30)}
                      />
                    </FormControl>
                    <FormDescription>
                      Remove sent/failed messages older than this many days (1-365)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
          <CardFooter>
            <Button 
              type="submit" 
              disabled={isLoading || !settings?.isConnected}
            >
              {isLoading ? 'Saving...' : 'Save Settings'}
            </Button>
            {!settings?.isConnected && (
              <p className="text-sm text-muted-foreground ml-3">
                Connect Instagram account to enable settings
              </p>
            )}
          </CardFooter>
        </Card>
      </form>
    </Form>
    </div>
  );
}
