'use client';

import * as React from 'react';
import { format } from 'date-fns';
import { Edit, Trash2, Clock, User, MessageSquare, Filter, Search, RefreshCw, Calendar } from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';

import { Badge } from '@workspace/ui/components/badge';
import { Button } from '@workspace/ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@workspace/ui/components/dialog';
import { Input } from '@workspace/ui/components/input';
import { Label } from '@workspace/ui/components/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@workspace/ui/components/table';
import { Textarea } from '@workspace/ui/components/textarea';
import { useToast } from '@workspace/ui/hooks/use-toast';
import { Avatar, AvatarFallback, AvatarImage } from '@workspace/ui/components/avatar';
import { Popover, PopoverContent, PopoverTrigger } from '@workspace/ui/components/popover';
import { Calendar as CalendarComponent } from '@workspace/ui/components/calendar';
import { cn } from '@workspace/ui/lib/utils';

interface MessageListItem {
  id: string;
  contactId: string;
  contactUsername: string;
  contactAvatar: string | null;
  message: string;
  scheduledTime: Date | null;
  status: 'PENDING' | 'PROCESSING' | 'SENT' | 'FAILED';
  messageType: 'SYSTEM' | 'EXTENSION';
  handlerType: 'SYSTEM' | 'EXTENSION';
  priority: number;
  sequenceNumber?: number;
  sentAt?: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export function MessageListDashboard(): React.JSX.Element {
  const params = useParams<{ slug: string }>();
  const { toast } = useToast();

  const [messages, setMessages] = React.useState<MessageListItem[]>([]);
  const [filteredMessages, setFilteredMessages] = React.useState<MessageListItem[]>([]);
  const [isLoading, setIsLoading] = React.useState(false);
  const [searchTerm, setSearchTerm] = React.useState('');
  const [statusFilter, setStatusFilter] = React.useState<string>('all');
  const [typeFilter, setTypeFilter] = React.useState<string>('all');
  const [handlerFilter, setHandlerFilter] = React.useState<string>('all');

  // Edit dialog state
  const [editDialogOpen, setEditDialogOpen] = React.useState(false);
  const [editingMessage, setEditingMessage] = React.useState<MessageListItem | null>(null);
  const [editMessageText, setEditMessageText] = React.useState('');
  const [editDate, setEditDate] = React.useState<Date | undefined>(undefined);
  const [editTime, setEditTime] = React.useState('12:00');

  // Delete dialog state
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [deletingMessage, setDeletingMessage] = React.useState<MessageListItem | null>(null);

  // Filter messages based on search term, status, type, and handler
  React.useEffect(() => {
    let filtered = messages;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        message =>
          message.contactUsername.toLowerCase().includes(searchTerm.toLowerCase()) ||
          message.message.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(message => message.status === statusFilter);
    }

    // Type filter
    if (typeFilter !== 'all') {
      filtered = filtered.filter(message => message.messageType === typeFilter);
    }

    // Handler filter
    if (handlerFilter !== 'all') {
      filtered = filtered.filter(message => message.handlerType === handlerFilter);
    }

    setFilteredMessages(filtered);
  }, [messages, searchTerm, statusFilter, typeFilter, handlerFilter]);

  const refreshData = async (showSuccessToast = true): Promise<void> => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/message-list', {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache'
        }
      });
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setMessages(data.data.messages || []);
          if (showSuccessToast) {
            toast({
              title: 'Success',
              description: 'Message list refreshed successfully'
            });
          }
        }
      }
    } catch (error) {
      console.error('Error refreshing message list:', error);
      toast({
        title: 'Error',
        description: 'Failed to refresh message list',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Load data on component mount
  React.useEffect(() => {
    refreshData(false);
  }, []);

  const handleEdit = (message: MessageListItem): void => {
    setEditingMessage(message);
    setEditMessageText(message.message);
    setEditDate(message.scheduledTime ? new Date(message.scheduledTime) : undefined);
    setEditTime(message.scheduledTime ? format(new Date(message.scheduledTime), 'HH:mm') : '12:00');
    setEditDialogOpen(true);
  };

  const handleSaveEdit = async (): Promise<void> => {
    if (!editingMessage) return;

    if (!editDate) {
      toast({
        title: 'Error',
        description: 'Please select a date for the message.',
        variant: 'destructive'
      });
      return;
    }

    setIsLoading(true);
    try {
      // Combine date and time
      const [hours, minutes] = editTime.split(':').map(Number);
      const scheduledDateTime = new Date(editDate);
      scheduledDateTime.setHours(hours, minutes, 0, 0);

      const response = await fetch('/api/message-list', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          messageId: editingMessage.id,
          message: editMessageText,
          scheduledTime: scheduledDateTime.toISOString()
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          toast({
            title: 'Success',
            description: 'Message updated successfully'
          });
          setEditDialogOpen(false);
          await refreshData(false);
        } else {
          throw new Error(data.error || 'Failed to update message');
        }
      } else {
        throw new Error('Failed to update message');
      }
    } catch (error) {
      console.error('Error updating message:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update message',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = (message: MessageListItem): void => {
    setDeletingMessage(message);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async (): Promise<void> => {
    if (!deletingMessage) return;

    setIsLoading(true);
    try {
      const response = await fetch('/api/message-list', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          messageId: deletingMessage.id
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          toast({
            title: 'Success',
            description: 'Message deleted successfully'
          });
          setDeleteDialogOpen(false);
          await refreshData(false);
        } else {
          throw new Error(data.error || 'Failed to delete message');
        }
      } else {
        throw new Error('Failed to delete message');
      }
    } catch (error) {
      console.error('Error deleting message:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete message',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'SENT':
        return 'default';
      case 'FAILED':
        return 'destructive';
      case 'PROCESSING':
        return 'secondary';
      case 'PENDING':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const getMessageTypeBadgeVariant = (messageType: string) => {
    return messageType === 'SYSTEM' ? 'secondary' : 'outline';
  };

  const getHandlerTypeBadgeVariant = (handlerType: string) => {
    return handlerType === 'SYSTEM' ? 'default' : 'secondary';
  };

  const getHandlerTypeDescription = (handlerType: string) => {
    return handlerType === 'SYSTEM' ? 'System (24h window)' : 'Extension';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Message List</h1>
          <p className="text-muted-foreground">
            Manage system and extension messages with priority and timing control
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={() => refreshData(true)} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Messages</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{messages.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {messages.filter(m => m.status === 'PENDING').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System (24h)</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {messages.filter(m => m.handlerType === 'SYSTEM').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Extension</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {messages.filter(m => m.handlerType === 'EXTENSION').length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4 md:flex-row md:items-end">
            <div className="flex-1">
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by contact or message..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="status-filter">Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger id="status-filter" className="w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="PROCESSING">Processing</SelectItem>
                  <SelectItem value="SENT">Sent</SelectItem>
                  <SelectItem value="FAILED">Failed</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="type-filter">Message Type</Label>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger id="type-filter" className="w-[180px]">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="SYSTEM">System</SelectItem>
                  <SelectItem value="EXTENSION">Extension</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="handler-filter">Handler</Label>
              <Select value={handlerFilter} onValueChange={setHandlerFilter}>
                <SelectTrigger id="handler-filter" className="w-[180px]">
                  <SelectValue placeholder="Filter by handler" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Handlers</SelectItem>
                  <SelectItem value="SYSTEM">System (24h)</SelectItem>
                  <SelectItem value="EXTENSION">Extension</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Messages Table */}
      <Card>
        <CardHeader>
          <CardTitle>Messages ({filteredMessages.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Contact</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Message</TableHead>
                  <TableHead>Handler</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Scheduled</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredMessages.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                      No messages found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredMessages.map((message) => (
                    <TableRow key={message.id}>
                      <TableCell>
                        <Link
                          href={`/organizations/${params.slug}/instagram/contacts/${message.contactId}`}
                          className="flex items-center gap-2 hover:underline"
                        >
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={message.contactAvatar || undefined} />
                            <AvatarFallback>
                              {message.contactUsername.slice(0, 2).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <span className="font-medium">{message.contactUsername}</span>
                        </Link>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          Priority {message.priority}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="max-w-xs truncate" title={message.message}>
                          {message.message}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getHandlerTypeBadgeVariant(message.handlerType)}>
                          {getHandlerTypeDescription(message.handlerType)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadgeVariant(message.status)}>
                          {message.status.toLowerCase()}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {message.scheduledTime ? (
                          <div className="text-sm">
                            {format(new Date(message.scheduledTime), 'MMM dd, yyyy HH:mm')}
                          </div>
                        ) : (
                          <span className="text-muted-foreground">Not scheduled</span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEdit(message)}
                            disabled={message.status === 'SENT'}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDelete(message)}
                            className="text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Message</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="edit-message">Message</Label>
              <Textarea
                id="edit-message"
                value={editMessageText}
                onChange={(e) => setEditMessageText(e.target.value)}
                rows={4}
              />
            </div>
            <div className="grid gap-2">
              <Label>Scheduled Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !editDate && "text-muted-foreground"
                    )}
                  >
                    <Calendar className="mr-2 h-4 w-4" />
                    {editDate ? format(editDate, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <CalendarComponent
                    mode="single"
                    selected={editDate}
                    onSelect={setEditDate}
                    disabled={(date) => {
                      const today = new Date();
                      today.setHours(0, 0, 0, 0);
                      const compareDate = new Date(date);
                      compareDate.setHours(0, 0, 0, 0);
                      return compareDate < today;
                    }}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-time">Time</Label>
              <Input
                id="edit-time"
                type="time"
                value={editTime}
                onChange={(e) => setEditTime(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveEdit} disabled={isLoading}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Message</DialogTitle>
          </DialogHeader>
          <p>
            Are you sure you want to delete this message for{' '}
            <strong>{deletingMessage?.contactUsername}</strong>? This action cannot be undone.
          </p>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleConfirmDelete} disabled={isLoading}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}