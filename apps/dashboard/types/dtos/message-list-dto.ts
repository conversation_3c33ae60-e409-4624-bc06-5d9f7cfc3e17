// MessageList interface for the new simplified architecture
export interface MessageListItem {
  id: string;
  contactId: string;
  contactUsername: string;
  contactAvatar?: string | null;
  message: string;
  messageType: 'FIRST_MESSAGE' | 'FOLLOW_UP';
  handlerType: 'SYSTEM' | 'EXTENSION';
  priority: number;
  status: 'PENDING' | 'SENT' | 'FAILED';
  scheduledTime?: Date | null;
  sentAt?: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface MessageListFilters {
  status?: 'all' | 'PENDING' | 'SENT' | 'FAILED';
  messageType?: 'all' | 'FIRST_MESSAGE' | 'FOLLOW_UP';
  handlerType?: 'all' | 'SYSTEM' | 'EXTENSION';
  search?: string;
}

export interface MessageListResponse {
  success: boolean;
  data?: MessageListItem[];
  error?: string;
}