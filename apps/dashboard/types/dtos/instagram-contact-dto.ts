export interface InstagramMessageDto {
  message: string;
  time: Date | null;
  status: string;
  type: 'legacy' | 'new';
  id: string;
  sequenceNumber: number;
}

export interface InstagramContactDto {
  id: string;
  instagramId: string | null;
  instagramUsername: string;
  avatar: string | null;
  messageCount: number;
  stage: string;
  isIgnored: boolean;
  isLinkSent: boolean;
  isTakeControl: boolean;
  messages: InstagramMessageDto[];
  followUps: InstagramMessageDto[]; // Backward compatibility alias
  createdAt: Date;
  updatedAt: Date;
}

// Legacy alias for backward compatibility - can be removed later
export interface InstagramFollowUpDto extends InstagramMessageDto {}
