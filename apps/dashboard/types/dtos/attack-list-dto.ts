// AttackList interface for Chrome Extension messages
export interface AttackListItem {
  id: string;
  contactId: string;
  contactUsername: string;
  contactAvatar?: string | null;
  message: string;
  messageType: 'FIRST_MESSAGE' | 'FOLLOW_UP';
  priority: number;
  status: 'PENDING' | 'SENT' | 'FAILED';
  scheduledTime?: Date | null;
  sentAt?: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface AttackListFilters {
  status?: 'all' | 'PENDING' | 'SENT' | 'FAILED';
  messageType?: 'all' | 'FIRST_MESSAGE' | 'FOLLOW_UP';
  search?: string;
}

export interface AttackListResponse {
  success: boolean;
  data?: AttackListItem[];
  error?: string;
}

// Response from mark-sent-v2 endpoint
export interface MarkSentResponse {
  success: boolean;
  nextMessage?: {
    id: string;
    contactUsername: string;
    message: string;
    messageType: 'FIRST_MESSAGE' | 'FOLLOW_UP';
  } | null;
  error?: string;
}

// Chrome Extension API interfaces
export interface ExtensionMessage {
  id: string;
  contactUsername: string;
  message: string;
  messageType: 'FIRST_MESSAGE' | 'FOLLOW_UP';
  priority: number;
}

export interface ExtensionMessagesResponse {
  success: boolean;
  messages?: ExtensionMessage[];
  error?: string;
}