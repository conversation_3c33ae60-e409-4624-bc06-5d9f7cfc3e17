# Instagram Conversation Gathering Optimization - Technical Design

## Current Implementation Analysis

### Problems Identified

1. **Late Triggering**: Conversation gathering happens during first follower processing
2. **Small API Limits**: Using limit=25 for API calls (inefficient)
3. **Sequential Processing**: No parallel fetching, causing slow performance
4. **Heavy Storage**: Storing full conversation data instead of minimal mappings
5. **No Progress Tracking**: No way to monitor gathering progress for large accounts

## Research & Testing Strategy

### Test Token Provided

```
IGAGRvI3ppin1BZAFAtOWg1cXVVVGtuNTBOMkR4Nm9PaFRQSEV6UVhORG5IbTY5LXpjY25Qb2RqMWFCS2RCdlZAESF9yc2JEUlppaFk4V1JOQjNmSjViOVp1dmRxX3hFM09rU09nRkJBWnE0YXhRQTlPd0lvcE9yRndRbzdpTjNrbwZDZD
```

### API Testing Plan

#### 1. Test Maximum Limit Support

```typescript
// Test different limit values to find optimal batch size
const testLimits = [50, 100, 200, 500, 1000];
for (const limit of testLimits) {
  const response = await axios.get(
    `https://graph.instagram.com/v23.0/me/conversations`,
    {
      params: {
        fields: "participants,updated_time,id",
        access_token: ACCESS_TOKEN,
        limit: limit,
      },
    },
  );
  // Measure response time and success
}
```

#### 2. Test Parallel Fetching

```typescript
// Test parallel pagination performance
const fetchPage = (url: string) => axios.get(url);

// Approach A: Promise.all for known pages
const pages = await Promise.all([
  fetchPage(url1),
  fetchPage(url2),
  fetchPage(url3),
  fetchPage(url4),
]);

// Approach B: Concurrent with limit
const pLimit = require("p-limit");
const limit = pLimit(4); // 4 concurrent requests
```

#### 3. Test Minimal Data Requirements

```typescript
// Test minimal fields needed for matching
const minimalFields = "id,participants.limit(2){username}";
const fullFields = "participants,updated_time,id,messages";
// Compare response sizes and times
```

## Optimized Architecture Design

### 1. Fast Conversation Gathering Function

```typescript
interface ConversationMapping {
  username: string;
  conversationId: string;
  participantId?: string;
}

async function blazingFastConversationGathering(
  accessToken: string,
  organizationId: string,
): Promise<ConversationMapping[]> {
  const mappings: ConversationMapping[] = [];
  const OPTIMAL_LIMIT = 200; // Based on testing
  const MAX_PARALLEL = 4; // Parallel streams

  // Phase 1: Initial fetch with large limit
  const initialResponse = await fetchConversations(accessToken, OPTIMAL_LIMIT);

  // Phase 2: Parallel pagination
  const pages = extractPaginationUrls(initialResponse);
  const parallelResults = await fetchPagesInParallel(pages, MAX_PARALLEL);

  // Phase 3: Minimal data extraction
  for (const conversation of allConversations) {
    const participant = conversation.participants?.[1]; // Skip business account
    if (participant?.username) {
      mappings.push({
        username: participant.username,
        conversationId: conversation.id,
        participantId: participant.id,
      });
    }
  }

  return mappings;
}
```

### 2. OAuth Callback Integration

```typescript
// In OAuth callback after successful connection
await prisma.$transaction(async (tx) => {
  // 1. Save Instagram settings
  const settings = await tx.instagramSettings.upsert({
    where: { organizationId },
    update: {
      /* ... */
    },
    create: {
      /* ... */
    },
  });

  // 2. Trigger async conversation gathering
  await triggerConversationGathering(settings.instagramToken, organizationId);

  // 3. Set gathering status
  await tx.organizationGatheringStatus.create({
    data: {
      organizationId,
      status: "IN_PROGRESS",
      startedAt: new Date(),
      totalConversations: 0,
    },
  });
});
```

### 3. Optimized Database Schema

```prisma
// Minimal conversation mapping table
model InstagramConversationMapping {
  id                      String   @id @default(uuid())
  organizationId          String
  conversationId          String   @unique // Instagram conversation ID
  username                String   // Participant username
  participantId           String?  // Optional participant ID
  lastUpdated            DateTime @default(now())

  @@index([organizationId, username])
  @@index([conversationId])
}

// Progress tracking table
model ConversationGatheringStatus {
  id                String   @id @default(uuid())
  organizationId    String   @unique
  status            String   // PENDING, IN_PROGRESS, COMPLETED, FAILED
  totalConversations Int     @default(0)
  processedCount    Int      @default(0)
  startedAt         DateTime?
  completedAt       DateTime?
  lastError         String?
  errorCount        Int      @default(0)

  @@index([organizationId])
}
```

### 4. Background Job Implementation

```typescript
// Queue-based processing for large accounts
interface GatheringJob {
  organizationId: string;
  accessToken: string;
  pageUrl?: string; // For resumable pagination
  attempt: number;
}

class ConversationGatheringQueue {
  async process(job: GatheringJob) {
    try {
      // 1. Fetch conversations in batches
      const batch = await fetchConversationBatch(job);

      // 2. Store minimal mappings
      await storeConversationMappings(batch);

      // 3. Update progress
      await updateGatheringProgress(job.organizationId, batch.length);

      // 4. Queue next page if exists
      if (batch.nextPageUrl) {
        await this.enqueue({
          ...job,
          pageUrl: batch.nextPageUrl,
        });
      }
    } catch (error) {
      await handleGatheringError(job, error);
    }
  }
}
```

### 5. Performance Metrics & Monitoring

```typescript
interface GatheringMetrics {
  organizationId: string;
  totalTime: number; // milliseconds
  conversationCount: number;
  apiCallCount: number;
  averageResponseTime: number;
  parallelFetchCount: number;
  errorRate: number;
}

// Track and log performance
async function trackGatheringPerformance(
  organizationId: string,
  metrics: GatheringMetrics,
) {
  await prisma.performanceLog.create({
    data: {
      organizationId,
      operation: "CONVERSATION_GATHERING",
      metrics: metrics as any,
      timestamp: new Date(),
    },
  });

  // Alert if performance degrades
  if (metrics.totalTime > 30000) {
    // > 30 seconds
    console.warn(
      `Slow conversation gathering for org ${organizationId}:`,
      metrics,
    );
  }
}
```

## Implementation Steps

### Phase 1: Research & Testing (Immediate)

1. Create test script using provided token
2. Determine optimal API limits (target: 200-500)
3. Test parallel fetching performance
4. Measure response sizes with minimal fields

### Phase 2: Core Implementation

1. Create new `blazingFastConversationGathering` function
2. Implement parallel fetching with p-limit
3. Add progress tracking and metrics
4. Create background job queue

### Phase 3: Integration

1. Modify OAuth callback to trigger gathering
2. Remove gathering from follower processing
3. Update database schema for minimal storage
4. Add API endpoint for manual triggering

### Phase 4: Monitoring & Optimization

1. Add performance dashboards
2. Implement retry logic for failures
3. Add caching for conversation mappings
4. Create cleanup jobs for old mappings

## Expected Performance Improvements

### Current Performance

- **API Calls**: ~400 calls for 10k conversations (limit=25)
- **Time**: 3-5 minutes for 10k conversations
- **Storage**: ~50MB for 10k conversations

### Optimized Performance

- **API Calls**: ~50 calls for 10k conversations (limit=200)
- **Time**: 15-30 seconds for 10k conversations
- **Storage**: ~2MB for 10k conversations (username mappings only)

### Metrics

- **8-10x faster** conversation gathering
- **90% reduction** in API calls
- **96% reduction** in storage requirements
- **Parallel processing** with 4 concurrent streams
- **Real-time progress** tracking

## Error Handling Strategy

1. **Rate Limiting**: Exponential backoff with jitter
2. **Token Expiry**: Automatic token refresh
3. **Network Failures**: Retry with resume capability
4. **Partial Failures**: Save progress and continue
5. **Data Validation**: Skip invalid conversations

## Testing Checklist

- [ ] Test with provided token for API limits
- [ ] Benchmark sequential vs parallel fetching
- [ ] Validate minimal field requirements
- [ ] Test with accounts having 1k, 10k, 50k conversations
- [ ] Verify error handling and retry logic
- [ ] Check memory usage during large gatherings
- [ ] Validate data integrity after gathering
- [ ] Test progress tracking accuracy
- [ ] Verify cleanup of old mappings
- [ ] Load test with multiple simultaneous gatherings

## Security Considerations

1. **Token Security**: Encrypt tokens at rest
2. **Rate Limiting**: Respect Instagram API limits
3. **Data Privacy**: Store only minimal required data
4. **Access Control**: Validate organization ownership
5. **Audit Logging**: Track all gathering operations

## Rollout Plan

1. **Week 1**: Research and testing with provided token
2. **Week 2**: Implement core gathering function
3. **Week 3**: Integration and testing
4. **Week 4**: Monitoring and optimization
5. **Week 5**: Production rollout with feature flag

## Success Metrics

- Conversation gathering completes in < 30 seconds for 90% of accounts
- Zero data loss during gathering
- < 1% error rate in production
- 95% reduction in database storage for conversation data
- User satisfaction score > 4.5/5 for speed improvements
