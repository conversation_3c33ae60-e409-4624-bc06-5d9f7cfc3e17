# Instagram Conversation Gathering Fix

## Current Issue

The `process-followers/route.ts` is fetching conversations on-the-fly instead of using the `InstagramConversationsNotGathered` table as an intermediary storage.

## Desired Flow

1. Extension uploads first follower
2. System gathers ALL conversations from Instagram API and populates `InstagramConversationsNotGathered` table (one-time)
3. After gathering is finished, match each follower with conversations in the table
4. No conversation → batch messages (priority 3)
5. Has conversation → AI analysis → attack list (or disqualified based on AI decision)

## Solution

The correct implementation already exists in `instagram-follower-trigger.ts` (lines 76-132). We need to apply the same pattern to `process-followers/route.ts`.

### Key Changes for process-followers/route.ts

Replace the current conversation gathering logic (lines 312-398) with:

```typescript
// 🚀 SIMPLE PROCESSING: Check if conversations gathered → Gather if needed → Match → Process
if (results.newFollowers > 0) {
  console.log(
    `🚀 Processing ${results.newFollowers} new followers with simple flow...`,
  );

  try {
    // Get Instagram settings
    const instagramSettings = await prisma.instagramSettings.findFirst({
      where: {
        organizationId,
        instagramToken: { not: null },
      },
    });

    // STEP 1: Check if InstagramConversationsNotGathered table is populated
    console.log(
      `Checking if conversation gathering has been done for organization: ${organizationId}`,
    );
    const existingConversations =
      await prisma.instagramConversationsNotGathered.count({
        where: { organizationId },
      });

    // If no conversations in table, this is FIRST TIME - run conversation gathering
    if (existingConversations === 0 && instagramSettings?.instagramToken) {
      console.log(
        `🚀 FIRST FOLLOWER DETECTED - Running conversation gathering for organization: ${organizationId}`,
      );

      try {
        // Get ALL conversations from Instagram API
        const instagramApiResponse = await getAllConversations(
          instagramSettings.instagramToken,
        );
        const instagramApiConversations = instagramApiResponse.data || [];

        console.log(
          `Found ${instagramApiConversations.length} conversations to populate`,
        );

        // Populate InstagramConversationsNotGathered table
        for (const conversation of instagramApiConversations) {
          const participants = conversation.participants || [];
          if (participants.length >= 2) {
            // Skip the first participant (business account) and take the second one
            const participant = participants[1];
            if (participant.username) {
              try {
                await prisma.instagramConversationsNotGathered.create({
                  data: {
                    organizationId,
                    instagramConversationId: conversation.id,
                    participantUsername: participant.username,
                    participantId: participant.id,
                    updatedTime: new Date(conversation.updated_time),
                    isGathered: false,
                  },
                });
                console.log(
                  `✅ Added conversation for: ${participant.username}`,
                );
              } catch (error) {
                // Ignore duplicates
                if (
                  !(error instanceof Error) ||
                  !error.message?.includes("unique constraint")
                ) {
                  console.error(
                    `Error adding conversation for ${participant.username}:`,
                    error,
                  );
                }
              }
            }
          }
        }

        console.log(
          `✅ Conversation gathering completed. Populated ${instagramApiConversations.length} conversations.`,
        );
      } catch (error) {
        console.error(`❌ Error during conversation gathering:`, error);
        // Continue with processing even if gathering fails
      }
    } else {
      console.log(
        `✅ Conversation gathering already done. Found ${existingConversations} existing conversations.`,
      );
    }

    // STEP 2: Process each new follower by matching with conversations
    console.log(
      `🔍 Processing each follower: Match with InstagramConversationsNotGathered table`,
    );

    for (const follower of validatedData.followers) {
      const username = follower.instagramNickname;

      // Check if this follower has a conversation in our table
      const conversationData =
        await prisma.instagramConversationsNotGathered.findFirst({
          where: {
            organizationId,
            participantUsername: username,
          },
        });

      console.log(
        `👤 Processing ${username}: ${conversationData ? "HAS CONVERSATION → AI" : "NO CONVERSATION → BATCH"}`,
      );

      // Find the created follower record
      const followerRecord = await prisma.instagramFollower.findFirst({
        where: {
          organizationId,
          instagramNickname: username,
        },
      });

      if (!followerRecord) {
        console.log(`⚠️ Follower record not found for ${username}, skipping`);
        continue;
      }

      if (conversationData && instagramSettings?.instagramToken) {
        // HAS CONVERSATION → AI ANALYSIS
        await processFollowerWithConversation(
          followerRecord,
          conversationData,
          instagramSettings.instagramToken,
          organizationId,
        );
      } else {
        // NO CONVERSATION → MESSAGE BATCHES
        await processFollowerWithBatches(followerRecord, organizationId);
      }
    }

    console.log(
      `✅ Simple processing completed for ${results.newFollowers} followers`,
    );
  } catch (error) {
    console.error("❌ Error in simple processing:", error);
    // Continue - don't fail the upload
  }
}
```

### Update processFollowerWithConversation function

```typescript
async function processFollowerWithConversation(
  follower: any,
  conversationData: any,
  instagramToken: string,
  organizationId: string,
) {
  try {
    console.log(`🤖 AI processing for ${follower.instagramNickname}...`);

    // Get conversation messages using conversationData.instagramConversationId
    const conversationResponse = await fetch(
      `https://graph.instagram.com/v22.0/${conversationData.instagramConversationId}/messages?access_token=${instagramToken}&fields=id,created_time,from,to,message,attachments{id,image_data,video_data,file_url,mime_type}`,
    );

    if (!conversationResponse.ok) {
      console.log(
        `⚠️ Failed to get messages for ${follower.instagramNickname}, using batch instead`,
      );
      return await processFollowerWithBatches(follower, organizationId);
    }

    const messageData = await conversationResponse.json();
    const messages = messageData.messages?.data || [];

    if (messages.length === 0) {
      console.log(
        `⚠️ No messages found for ${follower.instagramNickname}, using batch instead`,
      );
      return await processFollowerWithBatches(follower, organizationId);
    }

    // Format conversation history for AI with timestamps (last 10 messages)
    const sortedMessages = messages.sort(
      (a: any, b: any) =>
        new Date(a.created_time).getTime() - new Date(b.created_time).getTime(),
    );

    // Take last 10 messages for AI analysis
    const last10Messages = sortedMessages.slice(-10);

    const conversationHistory = last10Messages
      .map((msg: any) => {
        const sender = msg.from?.username || msg.from?.id || "Unknown";
        const messageText = msg.message || "[Media/Attachment]";
        const messageDate = new Date(msg.created_time).toISOString();
        return `[${messageDate}] ${sender}: ${messageText}`;
      })
      .join("\n");

    // AI analysis with conversation gathering mode
    const { generateInstagramResponse } = await import(
      "@workspace/instagram-bot"
    );
    const aiResponse = await generateInstagramResponse({
      prompt: "CONVERSATION GATHERING",
      conversationHistory: conversationHistory,
      organizationId: organizationId,
    });

    console.log(
      `✅ AI analysis for ${follower.instagramNickname}: stage=${aiResponse.stage}, priority=${aiResponse.priority}, followUps=${aiResponse.followUps?.length || 0}`,
    );

    // Create contact with AI-determined values
    const newContact = await prisma.instagramContact.create({
      data: {
        organizationId,
        userId: follower.userId,
        instagramId: follower.instagramId,
        instagramNickname: follower.instagramNickname,
        avatar: follower.avatar,
        followerCount: follower.followerCount,
        isVerifiedUser: follower.isVerified,
        stage: (aiResponse.stage as any) || "new",
        priority: aiResponse.priority || 1,
        status: "pending",
        messageCount: messages.length,
        nextMessageAt: new Date(),
        attackListStatus: "pending",
        conversationSource: "api",
        lastInteractionAt: new Date(conversationData.updatedTime),
      },
    });

    // Create AI-generated follow-ups
    if (aiResponse.followUps && aiResponse.followUps.length > 0) {
      for (let i = 0; i < aiResponse.followUps.length; i++) {
        const followUp = aiResponse.followUps[i];
        const scheduledTime = new Date();
        scheduledTime.setHours(
          scheduledTime.getHours() + (followUp.delayHours || 24),
        );

        await prisma.instagramFollowUp.create({
          data: {
            contactId: newContact.id,
            sequenceNumber: i + 1,
            message: followUp.message,
            scheduledTime: scheduledTime,
            status: "external",
          },
        });
      }
      console.log(
        `📝 Created ${aiResponse.followUps.length} follow-ups for ${follower.instagramNickname}`,
      );
    }

    // Mark follower as contacted
    await prisma.instagramFollower.update({
      where: { id: follower.id },
      data: { status: "contacted", isContacted: true },
    });

    // Mark conversation as gathered
    await prisma.instagramConversationsNotGathered.update({
      where: {
        instagramConversationId: conversationData.instagramConversationId,
      },
      data: {
        isGathered: true,
      },
    });

    console.log(
      `✅ ${follower.instagramNickname} processed with AI (priority ${aiResponse.priority || 1})`,
    );
  } catch (error) {
    console.error(
      `❌ Error in AI processing for ${follower.instagramNickname}:`,
      error,
    );
    // Fallback to batch processing
    await processFollowerWithBatches(follower, organizationId);
  }
}
```

## Key Benefits

1. **Efficient API Usage**: Conversations are gathered ONCE when the first follower is uploaded
2. **Table-based Matching**: Fast matching using database query instead of API calls
3. **Clean Separation**: Clear separation between conversation gathering and follower processing
4. **Fallback Logic**: If conversation gathering fails, system continues with batch messages
5. **Proper State Management**: Uses `isGathered` flag to track processed conversations

## Testing

1. Clear the `InstagramConversationsNotGathered` table for test organization
2. Upload first batch of followers via Chrome extension
3. Verify conversations are gathered and populated in table
4. Check that followers are properly matched and processed
5. Upload second batch and verify no duplicate gathering occurs

## Important Notes

- The `InstagramConversationsNotGathered` table acts as a cache for conversations
- Conversation gathering happens ONCE per organization
- Each follower is matched against this table for fast processing
- AI analysis only happens for followers WITH conversations
- Batch messages are assigned to followers WITHOUT conversations
