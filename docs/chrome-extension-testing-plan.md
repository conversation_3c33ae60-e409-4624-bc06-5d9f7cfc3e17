# Chrome Extension Settings Update - Comprehensive Testing Plan

## Overview

This document outlines the testing strategy for the new Chrome Extension settings features including daily message limits, continuous scraping, and duplicate prevention.

## Test Environment Setup

- Fresh database with test organization
- Chrome Extension v2.0 with new features
- Test Instagram accounts (sender and followers)
- API running locally or on staging

## 1. Daily Message Limit Testing

### Test Case 1.1: Unlimited Messages

**Setup:** Set daily limit to 0 (unlimited)
**Steps:**

1. Configure daily limit slider to 0
2. Send 50+ messages throughout the day
3. Verify all messages are sent successfully
   **Expected:** No limit enforced, all messages sent

### Test Case 1.2: Limited Messages (e.g., 25/day)

**Setup:** Set daily limit to 25
**Steps:**

1. Configure daily limit slider to 25
2. Send 20 batch messages (counts as 20)
3. Send 5 follow-up messages
4. Attempt to send 26th message
   **Expected:**

- First 25 messages sent successfully
- 26th message blocked with "Daily limit reached" status
- Progress bar shows 25/25 (100%)

### Test Case 1.3: Daily Reset at Midnight

**Setup:** Daily limit of 10, 10 messages sent
**Steps:**

1. Send 10 messages before midnight
2. Wait for midnight (or manually advance time)
3. Check counter reset
4. Send more messages
   **Expected:**

- Counter resets to 0 at midnight
- Can send 10 more messages after reset

### Test Case 1.4: Batch Message Counting

**Setup:** Daily limit of 10
**Steps:**

1. Send batch with 3 messages in sequence
2. Check message counter
   **Expected:** Counter increases by 1 (not 3)

### Test Case 1.5: Follow-up Message Counting

**Setup:** Daily limit of 10
**Steps:**

1. Send individual follow-up message
2. Check message counter
   **Expected:** Counter increases by 1

## 2. Continuous Scraping Testing

### Test Case 2.1: Attack List Empty - Continuous Scraping

**Setup:** Empty attack list, continuous scraping enabled
**Steps:**

1. Clear attack list
2. Enable continuous scraping toggle
3. Wait for bot to become idle
4. Monitor scraping behavior
   **Expected:**

- Bot scrapes next 250 followers
- 1-hour minimum break enforced
- Repeats when idle again

### Test Case 2.2: Attack List Has Messages - No Continuous Scraping

**Setup:** Attack list with messages, continuous scraping enabled
**Steps:**

1. Add contacts to attack list
2. Enable continuous scraping
3. Monitor behavior
   **Expected:**

- Standard scraping intervals apply (not continuous)
- No scraping until attack list empty

### Test Case 2.3: 1-Hour Cooldown Enforcement

**Setup:** Continuous scraping enabled
**Steps:**

1. Complete one continuous scraping session
2. Try to scrape again immediately
3. Check cooldown timer
   **Expected:**

- Cannot scrape for 1 hour
- UI shows remaining cooldown time

### Test Case 2.4: Toggle Continuous Scraping Off

**Setup:** Continuous scraping disabled
**Steps:**

1. Clear attack list
2. Disable continuous scraping
3. Monitor behavior
   **Expected:**

- No automatic scraping when idle
- Standard 7-day intervals apply

## 3. Attack Past Followers Testing

### Test Case 3.1: Attack Past Followers Enabled

**Setup:** Toggle enabled
**Steps:**

1. Enable "Attack past followers?" toggle
2. Run scraping session
3. Check scraped followers
   **Expected:**

- All followers scraped including existing contacts
- Duplicates handled appropriately

### Test Case 3.2: Attack Past Followers Disabled

**Setup:** Toggle disabled
**Steps:**

1. Disable "Attack past followers?" toggle
2. Have existing contacts in database
3. Run scraping session
   **Expected:**

- Only new followers scraped
- Existing contacts skipped

## 4. Duplicate Message Prevention Testing

### Test Case 4.1: Identical First Message

**Setup:** Contact with previously sent message
**Steps:**

1. Send first message "Hello!"
2. Try to send same "Hello!" message again
   **Expected:**

- Second message blocked
- Contact skipped with duplicate detection

### Test Case 4.2: Different First Message Allowed

**Setup:** Contact with previously sent message
**Steps:**

1. Send first message "Hello!"
2. Send different message "Hi there!"
   **Expected:**

- Different message allowed
- Both messages in history

### Test Case 4.3: Follow-ups Not Affected

**Setup:** Contact with first message sent
**Steps:**

1. Send follow-up messages
2. Send duplicate follow-up content
   **Expected:**

- Follow-ups not checked for duplicates
- All follow-ups sent normally

## 5. UI/UX Testing

### Test Case 5.1: Daily Limit Slider

**Steps:**

1. Move slider from 0 to 500
2. Check value display updates
3. Save settings
   **Expected:**

- Smooth slider interaction
- "Unlimited" shown at 0
- Specific count shown for 1-500

### Test Case 5.2: Progress Bar Display

**Setup:** Daily limit of 50, 25 sent
**Steps:**

1. Check progress bar
2. Send more messages
3. Monitor updates
   **Expected:**

- Shows 25/50 (50% filled)
- Updates in real-time
- Color changes when limit reached

### Test Case 5.3: Settings Persistence

**Steps:**

1. Configure all new settings
2. Refresh page
3. Check settings retained
   **Expected:**

- All settings saved correctly
- Values persist across sessions

## 6. Integration Testing

### Test Case 6.1: Full Workflow with Limits

**Setup:** Daily limit 20, continuous scraping on
**Steps:**

1. Start with empty attack list
2. Let bot scrape continuously (250 followers)
3. Send 20 messages
4. Hit daily limit
5. Wait for continuous scraping (1 hour)
6. Scrape more followers
   **Expected:**

- Proper flow between all features
- Limits respected
- Continuous scraping works when idle

### Test Case 6.2: API Endpoint Integration

**Steps:**

1. Test all new endpoints:
   - GET/PUT settings
   - GET messages-to-send
   - POST mark-sent
   - GET scraping-eligibility
   - POST update-scraping-timestamp
     **Expected:**

- All endpoints return correct data
- Proper error handling
- CORS headers present

## 7. Performance Testing

### Test Case 7.1: Large Message Volume

**Setup:** 500 messages daily limit
**Steps:**

1. Queue 500 messages
2. Let bot process throughout day
3. Monitor performance
   **Expected:**

- No degradation
- Counters accurate
- Database performs well

### Test Case 7.2: Scraping Performance

**Steps:**

1. Scrape 1000+ followers
2. Monitor memory usage
3. Check API response times
   **Expected:**

- Batch processing efficient
- No memory leaks
- API responds quickly

## 8. Error Handling

### Test Case 8.1: API Failures

**Steps:**

1. Simulate API downtime
2. Try to send messages
3. Check error handling
   **Expected:**

- Graceful failure
- Clear error messages
- Retry logic works

### Test Case 8.2: Invalid Settings

**Steps:**

1. Try to set invalid values
2. Submit malformed requests
   **Expected:**

- Validation prevents bad data
- User-friendly error messages

## 9. Edge Cases

### Test Case 9.1: Midnight Reset During Active Session

**Setup:** Sending messages at 11:59 PM
**Steps:**

1. Start sending batch at 11:59 PM
2. Continue past midnight
3. Check counter behavior
   **Expected:**

- Counter resets at midnight
- Current batch completes
- New limit applies to next batch

### Test Case 9.2: Changing Limits Mid-Day

**Setup:** 10 messages sent, limit was 20
**Steps:**

1. Change limit from 20 to 5
2. Try to send more messages
   **Expected:**

- New limit blocks messages immediately
- Shows 10/5 (over limit)

### Test Case 9.3: Scraping During Cooldown

**Setup:** In 1-hour cooldown period
**Steps:**

1. Try manual scraping trigger
2. Check API response
   **Expected:**

- Scraping blocked
- Shows remaining cooldown time

## 10. Regression Testing

### Test Case 10.1: Existing Features Still Work

**Steps:**

1. Test all existing features:
   - Basic message sending
   - Follow-up scheduling
   - Attack list management
   - Pause periods
   - Smart focus
     **Expected:**

- All existing features unaffected
- No breaking changes

## Test Execution Schedule

### Phase 1: Unit Testing (Day 1)

- Individual feature testing
- API endpoint validation
- Database operations

### Phase 2: Integration Testing (Day 2)

- Feature interactions
- Full workflow testing
- Performance baseline

### Phase 3: User Acceptance Testing (Day 3)

- Real user scenarios
- Edge case validation
- Final bug fixes

## Success Criteria

- All test cases pass
- No critical bugs
- Performance meets requirements
- User experience is smooth
- Backward compatibility maintained

## Test Data Requirements

- 10 test Instagram accounts
- 1000+ follower relationships
- Various message templates
- Test organization with different settings

## Tools Required

- Chrome DevTools for debugging
- Postman for API testing
- Database viewer for verification
- Network throttling for performance testing

## Sign-off Checklist

- [ ] All test cases executed
- [ ] Bugs logged and fixed
- [ ] Performance acceptable
- [ ] Documentation updated
- [ ] Stakeholder approval
