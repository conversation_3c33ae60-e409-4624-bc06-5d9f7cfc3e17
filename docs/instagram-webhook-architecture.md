# Instagram Webhook Multi-Organization Architecture

## Problem Statement

- Multiple organizations, each with their own Instagram account
- Current webhook implementation incorrectly routes all messages to the first organization found
- Need to capture both incoming messages from users AND outgoing messages sent manually on Instagram
- Must maintain proper message attribution and organization separation

## Core Issue

The webhook handler currently ignores the Instagram Business Account ID from the webhook payload and just finds ANY organization with bot enabled, causing messages to be incorrectly attributed.

## Solution Architecture

### 1. Database Schema Requirements

The `InstagramSettings` table needs to store the Instagram Account ID (IGID) to enable proper routing:

```prisma
model InstagramSettings {
  // ... existing fields ...
  instagramAccountId    String?  // This already exists!
  instagramBusinessId   String?  // Add this - the IGID from webhooks
  // ... rest of fields ...
}
```

### 2. Webhook Routing Logic

#### Current (BROKEN) Implementation:

```typescript
// Line 155: Gets the account ID from webhook
const instagramBusinessAccountId = entry.id;

// Line 201-227: WRONG - finds ANY organization
const instagramSettings = await prisma.instagramSettings.findFirst({
  where: {
    isBotEnabled: true,
    instagramToken: { not: null },
  },
});
```

#### Fixed Implementation:

```typescript
// Get the Instagram Business Account ID from webhook
const instagramBusinessAccountId = entry.id;

// Find the CORRECT organization by matching the Instagram Business ID
const instagramSettings = await prisma.instagramSettings.findFirst({
  where: {
    OR: [
      { instagramBusinessId: instagramBusinessAccountId },
      { instagramAccountId: instagramBusinessAccountId },
    ],
    instagramToken: { not: null },
  },
  include: {
    Organization: {
      include: {
        memberships: {
          where: { role: "ADMIN" },
          include: { user: true },
          take: 1,
        },
      },
    },
  },
});

if (!instagramSettings) {
  console.error(
    `No organization found for Instagram Business Account: ${instagramBusinessAccountId}`,
  );
  // Still respond with 200 to acknowledge webhook
  return NextResponse.json({ success: true });
}
```

### 3. Message Attribution Strategy

#### Incoming Messages (From Users)

- Webhook provides `messaging.sender.id` - the user's Instagram ID
- Message is marked as `isFromUser: true`
- Processed by AI bot if enabled

#### Outgoing Messages (Sent Manually on Instagram)

The webhook receives these as "echo" messages with `is_echo: true`:

```typescript
// Current implementation (Line 172-176)
if (messaging.message.is_echo) {
  console.log("Skipping echo message");
  continue; // THIS IS WRONG - we should save these!
}

// Fixed implementation:
if (messaging.message.is_echo) {
  // This is a message sent BY the business account
  // Save it but mark it as not from user
  isFromUser = false;
  // Continue processing to save the message
}
```

### 4. Complete Solution for Capturing All Messages

```typescript
// Determine message direction
const isEchoMessage = messaging.message.is_echo ||
                     senderId === instagramBusinessAccountId ||
                     senderId === instagramSettings.instagramAccountId;

// Save ALL messages, both incoming and outgoing
await prisma.instagramMessage.create({
  data: {
    contactId: contact.id,
    messageId,
    content: messageText,
    isFromUser: !isEchoMessage,  // false for business messages
    isFromExtension: false,       // true only for Chrome extension
    mediaUrl,
    mediaType,
    timestamp: messageTimestamp
  }
});

// Only queue for AI processing if it's FROM a user
if (!isEchoMessage && contactSettings?.isBotEnabled) {
  await queueMessage(...);
}
```

### 5. Instagram Account Connection Flow

When an organization connects their Instagram account:

```typescript
// After OAuth callback or token setup
const businessInfo = await getBusinessAccountInfo(accessToken);

await prisma.instagramSettings.upsert({
  where: { organizationId },
  update: {
    instagramToken: accessToken,
    instagramAccountId: businessInfo.id, // User-facing ID
    instagramBusinessId: businessInfo.id, // IGID for webhook routing
    instagramUsername: businessInfo.username,
    instagramName: businessInfo.name,
    instagramProfilePicture: businessInfo.profile_picture_url,
    isConnected: true,
    tokenExpiresAt: calculateExpiry(),
  },
  create: {
    organizationId,
    instagramToken: accessToken,
    instagramAccountId: businessInfo.id,
    instagramBusinessId: businessInfo.id,
    instagramUsername: businessInfo.username,
    // ... other fields
  },
});
```

### 6. Webhook Setup for Each Organization

Since you're using Instagram API with Instagram Login (not Facebook Business), each organization needs to:

1. **Subscribe their app to webhooks** for their Instagram account
2. **Use a shared webhook endpoint** with proper routing: `/api/webhooks/instagram`
3. **Verify the webhook** with the shared verification token

### 7. Key Implementation Steps

1. **Update Database Schema**

   - Add `instagramBusinessId` field to `InstagramSettings` if not present
   - Run migration

2. **Fix Webhook Handler**

   - Update organization lookup to use Instagram Business ID
   - Save echo messages as outgoing messages
   - Proper error handling for unknown accounts

3. **Update Connection Flow**

   - Store Instagram Business ID when account is connected
   - Validate and refresh tokens periodically

4. **Testing Strategy**
   - Test with multiple organizations
   - Verify correct message routing
   - Test both incoming and outgoing messages
   - Validate echo message handling

### 8. Security Considerations

1. **Webhook Signature Validation** - Already implemented ✓
2. **Rate Limiting** - Already implemented ✓
3. **Organization Isolation** - Fixed with proper ID matching
4. **Token Security** - Store encrypted in database
5. **Audit Logging** - Log all webhook events with organization context

### 9. Monitoring & Debugging

Add detailed logging:

```typescript
console.log(
  `[Webhook] Instagram Business Account: ${instagramBusinessAccountId}`,
);
console.log(
  `[Webhook] Found Organization: ${instagramSettings?.organizationId}`,
);
console.log(
  `[Webhook] Message Direction: ${isEchoMessage ? "Outgoing" : "Incoming"}`,
);
console.log(`[Webhook] Sender ID: ${senderId}`);
console.log(`[Webhook] Message saved with ID: ${messageId}`);
```

### 10. Edge Cases to Handle

1. **Unknown Instagram Account** - Log and acknowledge webhook
2. **Expired Token** - Mark organization as disconnected
3. **Duplicate Messages** - Already handled with deduplication
4. **Missing Organization Admin** - Skip processing, log error
5. **Rate Limits** - Implement exponential backoff

## Implementation Priority

1. **Critical Fix** - Update webhook handler to use Instagram Business ID for routing
2. **Important** - Save echo messages as outgoing messages
3. **Enhancement** - Add monitoring and better error handling
4. **Future** - Implement token refresh mechanism
