# Consolidated Migration Guide

This guide documents the consolidation of multiple Prisma migrations into a single, clean migration for production deployment.

## Overview

Previously, the project had **26+ individual migrations** accumulated during development. These have been consolidated into a **single migration** that creates the complete database schema in one step.

## Migration Consolidation Results

### Before Consolidation

- 26+ individual migration files
- Complex migration history
- Slower production deployments
- Potential for migration conflicts

### After Consolidation

- ✅ **1 consolidated migration**: `20250815200430_initial_consolidated_schema`
- ✅ **1,281 lines** of clean SQL covering the complete schema
- ✅ **45+ tables** with all relationships
- ✅ **87+ indexes** for optimal performance
- ✅ **13 enums** with proper mappings
- ✅ **All foreign key constraints** properly established

## What's Included

The consolidated migration creates:

### Core Tables

- `User`, `Account`, `Session` (Authentication)
- `Organization`, `Membership`, `Invitation` (Multi-tenancy)
- `ApiKey`, `Webhook`, `Notification` (Core features)

### Instagram Features

- `InstagramContact`, `InstagramFollower` (Contact management)
- `InstagramMessage`, `InstagramConversation` (Messaging)
- `InstagramSettings`, `InstagramBotSettings` (Configuration)
- `InstagramError`, `InstagramFollowUp` (Error handling & automation)

### Chrome Extension

- `ChromeExtensionSettings` (Extension configuration)
- Message limits and continuous scraping features
- Duplicate prevention system

### Bot & AI Features

- `BotStyle`, `BotMode`, `BotRule` (Bot configuration)
- `AdminSettings`, `AdminPrompt` (AI provider settings)
- `PromptConfig`, `TestSettings` (Prompt management)

### Messaging & Templates

- `MessageBatch`, `MessageBatchItem` (Bulk messaging)
- `FollowUpTemplate` (Automated follow-ups)
- `MessageQueue`, `FollowerProcessingQueue` (Processing queues)

## Production Deployment

### Quick Deployment

```bash
# Run the automated deployment script
./scripts/deploy-consolidated-migration.sh
```

### Manual Deployment

```bash
# Navigate to database package
cd packages/database

# Apply the consolidated migration
pnpm prisma migrate deploy

# Generate Prisma client
pnpm prisma generate
```

### Environment Requirements

- PostgreSQL database (empty or new)
- `DATABASE_URL` environment variable configured
- Node.js and pnpm installed

## Key Benefits

### Performance

- **Faster deployments**: Single migration vs 26+ migrations
- **Reduced complexity**: No migration dependency chains
- **Optimal indexes**: All performance indexes applied at once

### Maintenance

- **Cleaner history**: No development iteration artifacts
- **Single source of truth**: One migration file to maintain
- **Easier rollbacks**: Clear baseline state

### Development

- **Consistent schema**: Same structure across all environments
- **Simplified onboarding**: New developers get clean schema
- **Reduced conflicts**: No merge conflicts in migration files

## Verification

After deployment, verify the migration was successful:

```bash
# Check table count (should be 45+)
pnpm prisma db execute --stdin <<< "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';"

# Verify key tables exist
pnpm prisma db execute --stdin <<< "SELECT tablename FROM pg_tables WHERE schemaname = 'public' LIMIT 10;"

# Test Prisma client
pnpm prisma studio
```

## Backup Files Created

During the consolidation process, backup files were created:

- `packages/database/prisma/schema.prisma.backup` - Original schema backup
- `packages/database/migration-history-backup.txt` - List of original migrations

## Rollback Plan

If you need to rollback to the original migration structure:

```bash
# Restore original schema
cp packages/database/prisma/schema.prisma.backup packages/database/prisma/schema.prisma

# Note: You would need to restore individual migration files from git history
# This consolidation is intended to be a one-way operation for production
```

## Important Notes

### For New Environments

- ✅ Perfect for fresh production deployments
- ✅ Ideal for staging environment setup
- ✅ Great for new team member onboarding

### For Existing Production

- ⚠️ Only use this if your production database is empty
- ⚠️ If you have existing data, continue using incremental migrations
- ⚠️ This consolidation is designed for clean deployments

### Database Compatibility

- ✅ PostgreSQL (primary target)
- ✅ All modern PostgreSQL versions supported
- ✅ UUID support required

## Troubleshooting

### Migration Fails

```bash
# Check database connection
pnpm prisma db execute --stdin <<< "SELECT 1;"

# Check migration status
pnpm prisma migrate status

# Reset and retry (only for empty databases)
pnpm prisma migrate reset --force --skip-seed
pnpm prisma migrate deploy
```

### Client Generation Issues

```bash
# Clear generated client
rm -rf node_modules/.prisma/client
rm -rf node_modules/@prisma/client

# Regenerate
pnpm prisma generate
```

### Permission Issues

```bash
# Make deployment script executable
chmod +x scripts/deploy-consolidated-migration.sh

# Check database permissions
# Ensure your database user has CREATE, ALTER, DROP permissions
```

## Success Metrics

After successful deployment, you should see:

- ✅ 45+ tables created
- ✅ All indexes applied (87+ indexes)
- ✅ All foreign key relationships established
- ✅ Prisma client generates without errors
- ✅ Application connects successfully

## Next Steps

1. **Deploy to staging** first to validate
2. **Run application tests** to ensure functionality
3. **Deploy to production** when confident
4. **Monitor performance** after deployment
5. **Update team documentation** with new process

---

## Migration Details

**Migration ID**: `20250815200430_initial_consolidated_schema`  
**Created**: August 15, 2025  
**Size**: 1,281 lines of SQL  
**Replaces**: 26+ individual migrations  
**Status**: ✅ Tested and verified

This consolidated migration provides a clean, efficient foundation for your production database deployment.
