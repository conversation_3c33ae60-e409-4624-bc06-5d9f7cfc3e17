# Instagram Webhook Implementation Fix - Step by Step

## Executive Summary

**The Problem:** Your webhook handler incorrectly routes ALL Instagram messages to the first organization it finds, regardless of which Instagram account actually received the message.

**The Solution:** Fix the organization lookup to use the Instagram Business Account ID from the webhook payload.

## Critical Code Changes Required

### 1. Fix the Webhook Handler (PRIORITY 1)

**File:** `/apps/dashboard/app/api/webhooks/instagram/route.ts`

**Current Code (BROKEN - Line 201-227):**

```typescript
// ❌ WRONG - This finds ANY organization with bot enabled
const instagramSettings = await prisma.instagramSettings.findFirst({
  where: {
    isBotEnabled: true,
    instagramToken: { not: null },
  },
  include: {
    Organization: {
      include: {
        memberships: {
          where: { role: "ADMIN" },
          include: { user: true },
          take: 1,
        },
      },
    },
  },
});
```

**Fixed Code (CORRECT):**

```typescript
// ✅ CORRECT - Find organization by Instagram Business Account ID
const instagramSettings = await prisma.instagramSettings.findFirst({
  where: {
    OR: [
      { instagramBusinessId: instagramBusinessAccountId },
      { instagramAccountId: instagramBusinessAccountId },
    ],
    instagramToken: { not: null },
  },
  include: {
    Organization: {
      include: {
        memberships: {
          where: { role: "ADMIN" },
          include: { user: true },
          take: 1,
        },
      },
    },
  },
});

if (!instagramSettings) {
  console.error(
    `No organization found for Instagram Business Account: ${instagramBusinessAccountId}`,
  );
  console.error(
    `Webhook received message for unknown account. Please ensure account ${instagramBusinessAccountId} is connected.`,
  );

  // Still return 200 to acknowledge webhook (prevents Instagram from retrying)
  return NextResponse.json({ success: true });
}
```

### 2. Fix Echo Message Handling (PRIORITY 2)

**Current Code (BROKEN - Line 172-176):**

```typescript
// ❌ WRONG - Skipping echo messages means we lose outgoing messages
if (messaging.message.is_echo) {
  console.log("Skipping echo message");
  continue;
}
```

**Fixed Code (CORRECT):**

```typescript
// ✅ CORRECT - Save echo messages as outgoing messages
const isEchoMessage =
  messaging.message.is_echo ||
  senderId === instagramBusinessAccountId ||
  senderId === instagramSettings.instagramAccountId;

// Don't skip - continue processing to save the message
```

### 3. Update Message Saving Logic (Line 442-455)

**Fixed Code:**

```typescript
// Save ALL messages (both incoming and outgoing)
await prisma.instagramMessage.create({
  data: {
    contactId: contact.id,
    messageId,
    content: messageText,
    isFromUser: !isEchoMessage, // false for echo/business messages
    mediaUrl,
    mediaType,
    timestamp: messageTimestamp,
  },
});

console.log(
  `Saved message: ${messageId} (${isEchoMessage ? "OUTGOING" : "INCOMING"}) at ${messageTimestamp.toISOString()}`,
);

// Only queue for AI processing if it's FROM a user (not echo)
if (!isEchoMessage && contactSettings?.isBotEnabled && !contact.isTakeControl) {
  await queueMessage(
    {
      organizationId: contact.organizationId,
      senderId,
      messageId,
      messageText,
      mediaUrl,
      mediaType,
    },
    delay,
    true,
    timestamp,
  );
}
```

## Database Schema Check

### Required Fields in InstagramSettings

Check if these fields exist in your `InstagramSettings` table:

```prisma
model InstagramSettings {
  id                      String       @id @default(uuid())
  organizationId          String       @unique
  instagramToken          String?
  instagramAccountId      String?      // User-facing ID
  instagramBusinessId     String?      // ← ADD THIS if missing (IGID for webhooks)
  instagramUsername       String?
  // ... other fields
}
```

If `instagramBusinessId` is missing, create a migration:

```sql
ALTER TABLE "InstagramSettings"
ADD COLUMN "instagramBusinessId" VARCHAR(255);

-- Copy existing IDs as starting point
UPDATE "InstagramSettings"
SET "instagramBusinessId" = "instagramAccountId"
WHERE "instagramAccountId" IS NOT NULL;
```

## Instagram Connection Flow Update

When an organization connects their Instagram account, make sure to store the Business ID:

**File:** Update your OAuth callback or connection handler

```typescript
// After getting the access token
const businessInfo = await getBusinessAccountInfo(accessToken);

await prisma.instagramSettings.upsert({
  where: { organizationId },
  update: {
    instagramToken: accessToken,
    instagramAccountId: businessInfo.id,
    instagramBusinessId: businessInfo.id, // ← IMPORTANT: Store this!
    instagramUsername: businessInfo.username,
    isConnected: true,
  },
  create: {
    organizationId,
    instagramToken: accessToken,
    instagramAccountId: businessInfo.id,
    instagramBusinessId: businessInfo.id, // ← IMPORTANT: Store this!
    instagramUsername: businessInfo.username,
    isBotEnabled: false,
    minResponseTime: 30,
    maxResponseTime: 60,
    messageDelayMin: 3,
    messageDelayMax: 5,
  },
});
```

## Testing Plan

### 1. Test Organization Routing

```typescript
// Add this temporary debug code to webhook handler
console.log("=== WEBHOOK DEBUG ===");
console.log(
  "Instagram Business Account ID from webhook:",
  instagramBusinessAccountId,
);
console.log("Found organization:", instagramSettings?.organizationId);
console.log("Organization name:", instagramSettings?.Organization?.name);
console.log("===================");
```

### 2. Test with Multiple Organizations

1. **Setup:**

   - Connect Instagram Account A to Organization 1
   - Connect Instagram Account B to Organization 2

2. **Test Incoming Messages:**

   - Send message to Account A → Should route to Org 1
   - Send message to Account B → Should route to Org 2

3. **Test Outgoing Messages:**
   - Send manual reply from Account A → Should save as outgoing for Org 1
   - Send manual reply from Account B → Should save as outgoing for Org 2

### 3. Verify Message Attribution

```sql
-- Check messages are correctly attributed
SELECT
  im.id,
  im."isFromUser",
  im.content,
  ic."organizationId",
  o.name as org_name
FROM "InstagramMessage" im
JOIN "InstagramContact" ic ON im."contactId" = ic.id
JOIN "Organization" o ON ic."organizationId" = o.id
ORDER BY im."createdAt" DESC
LIMIT 20;
```

## Common Issues & Solutions

### Issue 1: "No organization found for Instagram Business Account"

**Solution:** The Instagram account needs to be properly connected with the Business ID stored.

### Issue 2: Messages still going to wrong organization

**Solution:** Check that `instagramBusinessId` is correctly stored and matches the webhook payload.

### Issue 3: Echo messages not being saved

**Solution:** Ensure you removed the `continue` statement for echo messages.

### Issue 4: AI responding to outgoing messages

**Solution:** Verify the `!isEchoMessage` check before queueing for AI.

## Deployment Checklist

- [ ] Add `instagramBusinessId` field to database (if missing)
- [ ] Update webhook handler with fixed organization lookup
- [ ] Fix echo message handling to save outgoing messages
- [ ] Update Instagram connection flow to store Business ID
- [ ] Deploy changes to staging environment
- [ ] Test with multiple organizations
- [ ] Verify correct message routing
- [ ] Deploy to production
- [ ] Monitor logs for any routing errors

## Monitoring

Add these log entries for production monitoring:

```typescript
// Log successful routing
logger.info("Webhook routed successfully", {
  instagramBusinessId: instagramBusinessAccountId,
  organizationId: instagramSettings.organizationId,
  messageId,
  isEcho: isEchoMessage,
});

// Log routing failures
logger.error("Failed to route webhook", {
  instagramBusinessId: instagramBusinessAccountId,
  error: "No organization found",
});
```

## Expected Outcome

After implementing these fixes:

✅ Each organization receives only their own Instagram messages
✅ Outgoing messages (sent manually on Instagram) are captured
✅ AI bot only responds to incoming user messages
✅ Complete conversation history is maintained
✅ Multiple organizations can operate independently

## Questions to Verify

1. Do you have the `instagramBusinessId` field in your database?
2. Are you storing the Instagram Business ID when accounts connect?
3. Are you using Instagram API with Instagram Login (not Facebook Business)?
4. Do you need per-organization AI settings?

## Next Steps

1. **Immediate:** Fix the webhook handler organization lookup
2. **Important:** Ensure Business ID is stored on account connection
3. **Testing:** Verify with at least 2 organizations
4. **Optional:** Add per-organization AI configuration
