# Chrome Extension Settings Update - Implementation Plan

## Overview

This document outlines the implementation plan for updating the Chrome Extension settings to include message limits, improved scraping behavior, and message duplicate prevention.

## Requirements Summary

### 1. Daily Message Limit

- Add setting for maximum messages per day (10 to Unlimited)
- Count batch messages as 1 message (regardless of how many messages in the batch)
- Count follow-ups as 1 message each
- Track daily message count and reset at midnight

### 2. Scraping Behavior Changes

- Add "Attack past followers?" toggle setting
- If disabled: Skip all follower scraping
- If enabled: Continue with modified behavior
- When attack list is empty AND bot has nothing to do:
  - Continue scraping next 250 followers
  - No 7-day waiting period between scraping sessions
  - Only scrape when idle (no messages to send)

### 3. Duplicate Prevention

- Prevent sending the same first message to a user twice
- Track sent messages per contact

## Database Schema Changes

### ChromeExtensionSettings Table Updates

```sql
ALTER TABLE "ChromeExtensionSettings"
ADD COLUMN "dailyMessageLimit" INTEGER DEFAULT NULL, -- NULL = unlimited, otherwise 10+
ADD COLUMN "messagesSentToday" INTEGER DEFAULT 0,
ADD COLUMN "lastMessageResetDate" DATE DEFAULT CURRENT_DATE,
ADD COLUMN "attackPastFollowers" BOOLEAN DEFAULT true,
ADD COLUMN "continuousScraping" BOOLEAN DEFAULT true,
ADD COLUMN "lastContinuousScrapingAt" TIMESTAMP; -- Track last continuous scraping time

-- Add indexes for performance
CREATE INDEX IX_ChromeExtensionSettings_lastMessageResetDate
ON "ChromeExtensionSettings"("lastMessageResetDate");
```

### InstagramContact Table Updates

```sql
ALTER TABLE "InstagramContact"
ADD COLUMN "firstMessageHash" VARCHAR(64), -- SHA256 hash of first message sent
ADD COLUMN "messagesSentCount" INTEGER DEFAULT 0,
ADD COLUMN "lastMessageSentHash" VARCHAR(64);

-- Add index for duplicate detection
CREATE INDEX IX_InstagramContact_firstMessageHash
ON "InstagramContact"("firstMessageHash");
```

## API Endpoint Modifications

### 1. `/api/chrome-extension/settings` (GET & PUT)

**GET Response Updates:**

```typescript
{
  // Existing fields...
  dailyMessageLimit: number | null, // null = unlimited
  messagesSentToday: number,
  lastMessageResetDate: string,
  attackPastFollowers: boolean,
  continuousScraping: boolean,
  canSendMoreMessages: boolean, // Calculated field
  remainingMessages: number | null, // Calculated field
  canScrapeContinuously: boolean, // True if 1 hour has passed since last scraping
  nextContinuousScrapingAt: string | null // When next continuous scraping is allowed
}
```

**PUT Request Updates:**

```typescript
{
  // Existing fields...
  dailyMessageLimit?: number | null,
  attackPastFollowers?: boolean,
  continuousScraping?: boolean
}
```

### 2. `/api/chrome-extension/messages-to-send` (GET)

**Modifications:**

1. Check daily message limit before returning messages
2. Filter out contacts with duplicate first messages
3. Return empty array if daily limit reached

**Response Updates:**

```typescript
{
  success: boolean,
  data: Message[],
  limitReached?: boolean,
  remainingMessages?: number,
  resetTime?: string // When daily limit resets
}
```

### 3. `/api/chrome-extension/mark-sent` (POST)

**Request Updates:**

```typescript
{
  contactId: string,
  messageType: 'batch_sequence' | 'follow_up' | 'initial',
  batchCompleted?: boolean,
  sequenceNumber?: number,
  messageHash?: string, // SHA256 of message content
  messageCount?: number // For batch: 1, for follow-up: 1
}
```

**Processing Logic:**

1. Update daily message counter
2. Store message hash for duplicate prevention
3. Reset daily counter if date changed

### 4. `/api/chrome-extension/scraping-eligibility` (GET)

**Modifications:**

1. Check `attackPastFollowers` setting
2. If false, return not eligible
3. If true and attack list empty, allow continuous scraping
4. Remove 7-day waiting period when `continuousScraping` is true

## Chrome Extension Background Script Changes

### 1. Message Sending Logic (`background/index.ts`)

```typescript
// Before sending messages
async function checkDailyLimit(apiKey: string): Promise<boolean> {
  const settings = await ApiService.getChromeExtensionSettings(apiKey);

  if (!settings.data) return false;

  // Reset counter if new day
  const today = new Date().toDateString();
  const lastReset = new Date(settings.data.lastMessageResetDate).toDateString();

  if (today !== lastReset) {
    await ApiService.resetDailyMessageCounter(apiKey);
    return true;
  }

  // Check limit
  if (settings.data.dailyMessageLimit === null) {
    return true; // Unlimited
  }

  return settings.data.messagesSentToday < settings.data.dailyMessageLimit;
}

// Modified message sending
async function sendMessages() {
  if (!(await checkDailyLimit(apiKey))) {
    await setStatus("📊 Daily message limit reached. Resets at midnight.");
    return;
  }

  // Continue with existing logic...
}
```

### 2. Scraping Logic Modifications

```typescript
async function checkAndPerformScraping(apiKey: string, settings: any) {
  // Check if attack past followers is enabled
  if (!settings.attackPastFollowers) {
    console.log(
      '🔍 SCRAPING: Disabled - "Attack past followers" is turned off',
    );
    return;
  }

  // Check if attack list is empty
  const attackList = await ApiService.getAttackList(apiKey);
  if (attackList.data && attackList.data.length > 0) {
    console.log("🔍 SCRAPING: Attack list not empty, skipping scraping");
    return;
  }

  // Check scraping eligibility (modified to support continuous scraping)
  const eligibility = await ApiService.getScrapingEligibility(apiKey);

  if (eligibility.success && eligibility.data?.isEligible) {
    // Perform scraping immediately when idle
    await performFollowerScraping(apiKey, settings);
  }
}
```

### 3. Message Duplicate Prevention

```typescript
async function prepareMessageForSending(
  message: Message,
  contactId: string,
): Promise<boolean> {
  // Generate hash of message content
  const messageHash = await generateSHA256(message.text);

  // Check if this is a first message
  if (message.type === "batch_sequence" && message.sequenceNumber === 1) {
    // Check for duplicate
    const contact = await ApiService.getContactStatus(contactId);
    if (contact.firstMessageHash === messageHash) {
      console.log(
        `⚠️ Duplicate first message detected for ${contact.username}`,
      );
      return false;
    }
  }

  return true;
}
```

## UI Component Updates

### Chrome Extension Settings Form

```tsx
// New form fields
<FormField
  control={form.control}
  name="dailyMessageLimit"
  render={({ field }) => (
    <FormItem>
      <FormLabel>Daily Message Limit</FormLabel>
      <FormControl>
        <Select value={field.value?.toString() || 'unlimited'}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="unlimited">Unlimited</SelectItem>
            <SelectItem value="10">10 messages</SelectItem>
            <SelectItem value="25">25 messages</SelectItem>
            <SelectItem value="50">50 messages</SelectItem>
            <SelectItem value="100">100 messages</SelectItem>
            <SelectItem value="250">250 messages</SelectItem>
            <SelectItem value="500">500 messages</SelectItem>
          </SelectContent>
        </Select>
      </FormControl>
      <FormDescription>
        Maximum messages per day. Batches count as 1 message.
      </FormDescription>
    </FormItem>
  )}
/>

<FormField
  control={form.control}
  name="attackPastFollowers"
  render={({ field }) => (
    <FormItem className="flex items-center justify-between">
      <div>
        <FormLabel>Attack Past Followers?</FormLabel>
        <FormDescription>
          Enable scraping and messaging of followers
        </FormDescription>
      </div>
      <FormControl>
        <Switch
          checked={field.value}
          onCheckedChange={field.onChange}
        />
      </FormControl>
    </FormItem>
  )}
/>

{/* Daily Message Counter Display */}
{settings.dailyMessageLimit && (
  <Card>
    <CardHeader>
      <CardTitle>Daily Progress</CardTitle>
    </CardHeader>
    <CardContent>
      <div className="space-y-2">
        <Progress
          value={(settings.messagesSentToday / settings.dailyMessageLimit) * 100}
        />
        <p className="text-sm text-muted-foreground">
          {settings.messagesSentToday} / {settings.dailyMessageLimit} messages sent today
        </p>
        {settings.messagesSentToday >= settings.dailyMessageLimit && (
          <Alert>
            <AlertDescription>
              Daily limit reached. Resets at midnight.
            </AlertDescription>
          </Alert>
        )}
      </div>
    </CardContent>
  </Card>
)}

{/* Continuous Scraping Status */}
{settings.attackPastFollowers && settings.continuousScraping && (
  <Card>
    <CardHeader>
      <CardTitle>Continuous Scraping</CardTitle>
    </CardHeader>
    <CardContent>
      <div className="space-y-2">
        {settings.canScrapeContinuously ? (
          <Badge variant="success">Ready to scrape</Badge>
        ) : (
          <>
            <Badge variant="secondary">Cooling down</Badge>
            <p className="text-sm text-muted-foreground">
              Next scraping available at: {settings.nextContinuousScrapingAt}
            </p>
          </>
        )}
        <p className="text-xs text-muted-foreground">
          Continuous scraping runs when attack list is empty with 1-hour breaks
        </p>
      </div>
    </CardContent>
  </Card>
)}
```

## Implementation Steps

### Phase 1: Database & Backend (Week 1)

1. Create and test database migration script
2. Update Prisma schema
3. Modify API endpoints for settings management
4. Implement daily counter logic
5. Add duplicate detection system

### Phase 2: Chrome Extension (Week 2)

1. Update background script for message limits
2. Modify scraping behavior
3. Add duplicate prevention
4. Update popup UI to show limits
5. Test message counting logic

### Phase 3: Dashboard UI (Week 3)

1. Update settings form components
2. Add daily progress indicators
3. Create limit management UI
4. Add monitoring dashboard

### Phase 4: Testing & Deployment (Week 4)

1. Unit tests for new functionality
2. Integration testing
3. Load testing with limits
4. Staged rollout
5. Monitoring and adjustments

## Testing Strategy

### Unit Tests

- Daily counter reset logic
- Message limit enforcement
- Duplicate detection
- Scraping eligibility with new settings

### Integration Tests

- End-to-end message sending with limits
- Batch counting as single message
- Follow-up counting
- Continuous scraping when idle

### Edge Cases

- Midnight counter reset during active session
- Changing limits mid-day
- Network failures during counter updates
- Concurrent message sending

## Rollback Plan

### Database Rollback

```sql
-- Remove new columns if needed
ALTER TABLE "ChromeExtensionSettings"
DROP COLUMN IF EXISTS "dailyMessageLimit",
DROP COLUMN IF EXISTS "messagesSentToday",
DROP COLUMN IF EXISTS "lastMessageResetDate",
DROP COLUMN IF EXISTS "attackPastFollowers",
DROP COLUMN IF EXISTS "continuousScraping",
DROP COLUMN IF EXISTS "lastContinuousScrapingAt";

ALTER TABLE "InstagramContact"
DROP COLUMN IF EXISTS "firstMessageHash",
DROP COLUMN IF EXISTS "messagesSentCount",
DROP COLUMN IF EXISTS "lastMessageSentHash";
```

### Feature Flags

- Add feature flags for gradual rollout
- Allow enabling/disabling per organization
- Monitor performance and adjust

## Monitoring & Metrics

### Key Metrics to Track

1. Daily message volumes per organization
2. Scraping frequency and success rates
3. Duplicate prevention effectiveness
4. System performance with new features
5. User engagement with limits

### Alerts

- Daily limit reached notifications
- Scraping failures
- Duplicate message attempts
- Counter reset failures

## Migration Considerations

### Data Migration

- Set default values for existing records
- Backfill message counts where possible
- Initialize daily counters

### Communication

- Notify users of new features
- Provide documentation
- Offer support during transition

## Security & Performance

### Security

- Rate limiting on counter updates
- Validation of limit values
- Audit logging for changes

### Performance

- Index optimization for new fields
- Caching of daily counters
- Efficient duplicate checking

## Future Enhancements

1. **Hourly Limits**: Add hourly message limits for more granular control
2. **Custom Reset Times**: Allow organizations to set custom daily reset times
3. **Message Templates**: Prevent duplicate templates rather than exact messages
4. **Analytics Dashboard**: Detailed analytics on message performance
5. **Smart Limits**: AI-based limit recommendations based on engagement

## Conclusion

This implementation plan provides a comprehensive approach to adding message limits, improving scraping behavior, and preventing duplicate messages. The phased approach ensures minimal disruption while delivering valuable features to users.
