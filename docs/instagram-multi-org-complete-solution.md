# Instagram Multi-Organization Complete Solution

## System Architecture Overview

### ✅ Global Admin Configuration (Correct Design)

- **AdminSettings** - Global AI provider settings (Claude, OpenRouter, etc.)
- **AdminPrompt** - Global prompt templates for all organizations
- **BotStyle** - Global styles that organizations can choose from

### ✅ Organization-Specific Configuration

- **PromptConfig** - Each organization's custom information (about us, conversion link, etc.)
- **InstagramSettings** - Each organization's Instagram connection and tokens
- Organizations select from available bot styles but cannot create their own

## The Critical Fix Required

### 🔴 BROKEN: Current Webhook Handler

The webhook handler **incorrectly routes ALL messages to the first organization found**:

```typescript
// ❌ CURRENT CODE (Line 201-227 in webhook/route.ts)
const instagramSettings = await prisma.instagramSettings.findFirst({
  where: {
    isBotEnabled: true,
    instagramToken: { not: null },
  },
});
```

### ✅ FIXED: Correct Webhook Handler

```typescript
// ✅ FIXED CODE - Route by Instagram Business Account ID
const instagramBusinessAccountId = entry.id; // From webhook payload

const instagramSettings = await prisma.instagramSettings.findFirst({
  where: {
    OR: [
      { instagramBusinessId: instagramBusinessAccountId },
      { instagramAccountId: instagramBusinessAccountId },
    ],
    instagramToken: { not: null },
  },
  include: {
    Organization: {
      include: {
        memberships: {
          where: { role: "ADMIN" },
          include: { user: true },
          take: 1,
        },
      },
    },
  },
});

if (!instagramSettings) {
  console.error(
    `No organization found for Instagram account: ${instagramBusinessAccountId}`,
  );
  return NextResponse.json({ success: true }); // Acknowledge webhook
}
```

## Complete Implementation Steps

### Step 1: Database Migration (If Needed)

Check if `instagramBusinessId` exists in your schema:

```sql
-- Add field if missing
ALTER TABLE "InstagramSettings"
ADD COLUMN "instagramBusinessId" VARCHAR(255);

-- Initialize with existing account IDs
UPDATE "InstagramSettings"
SET "instagramBusinessId" = "instagramAccountId"
WHERE "instagramAccountId" IS NOT NULL;
```

### Step 2: Fix Webhook Message Routing

**File:** `/apps/dashboard/app/api/webhooks/instagram/route.ts`

1. **Fix organization lookup** (Line 201-227)
2. **Save echo messages** as outgoing (Line 172-176)
3. **Correct message attribution** (Line 442-455)

### Step 3: Update Instagram Connection Flow

When organizations connect their Instagram account:

```typescript
const businessInfo = await getBusinessAccountInfo(accessToken);

await prisma.instagramSettings.upsert({
  where: { organizationId },
  update: {
    instagramToken: accessToken,
    instagramAccountId: businessInfo.id,
    instagramBusinessId: businessInfo.id, // ← Store this!
    instagramUsername: businessInfo.username,
    isConnected: true,
  },
  create: {
    organizationId,
    instagramToken: accessToken,
    instagramAccountId: businessInfo.id,
    instagramBusinessId: businessInfo.id, // ← Store this!
    // ... other fields
  },
});
```

## System Flow with Multiple Organizations

```mermaid
graph TD
    A[Instagram User] -->|Sends Message| B[Instagram API]
    B -->|Webhook Event| C[Your Webhook Endpoint]

    C -->|Extract Business ID| D{Find Organization}

    D -->|Org 1 Match| E1[Organization 1]
    D -->|Org 2 Match| E2[Organization 2]
    D -->|Org N Match| E3[Organization N]
    D -->|No Match| E4[Log & Acknowledge]

    E1 -->|Queue Message| F1[Process with Org 1 Settings]
    E2 -->|Queue Message| F2[Process with Org 2 Settings]
    E3 -->|Queue Message| F3[Process with Org N Settings]

    F1 -->|Generate Response| G1[AI with Org 1 Prompts]
    F2 -->|Generate Response| G2[AI with Org 2 Prompts]
    F3 -->|Generate Response| G3[AI with Org N Prompts]

    G1 -->|Send Reply| H1[Using Org 1 Token]
    G2 -->|Send Reply| H2[Using Org 2 Token]
    G3 -->|Send Reply| H3[Using Org N Token]
```

## Message Types Handling

### 1. Incoming Messages (From Users)

- `is_echo = false`
- Save as `isFromUser = true`
- Queue for AI processing if bot enabled

### 2. Outgoing Messages (Manual Replies)

- `is_echo = true`
- Save as `isFromUser = false`
- Do NOT queue for AI processing
- Maintains complete conversation history

### 3. Bot Messages

- Generated by AI system
- Save as `isFromUser = false`
- Sent via Instagram API

## Configuration Hierarchy

```
Global (Admin Control)
├── AdminSettings
│   ├── AI Provider (Claude/OpenRouter)
│   ├── API Keys
│   ├── Cache Settings
│   └── Model Selection
├── AdminPrompt
│   ├── General Prompt Template
│   ├── Technical Prompt Template
│   └── Conversation Gathering Prompt
└── BotStyles
    ├── Style 1 (e.g., "Professional")
    ├── Style 2 (e.g., "Friendly")
    └── Style N (e.g., "Casual")

Per-Organization (User Control)
├── InstagramSettings
│   ├── Instagram Token
│   ├── Instagram Business ID  ← CRITICAL FOR ROUTING
│   ├── Bot Enabled/Disabled
│   └── Response Time Settings
└── PromptConfig
    ├── Selected Bot Style (from global)
    ├── About Us
    ├── Qualification Questions
    ├── Conversion Link
    └── Additional Info
```

## Testing Checklist

### 1. Single Organization Test

- [ ] Connect Instagram account
- [ ] Verify Business ID is stored
- [ ] Send test message
- [ ] Verify correct routing
- [ ] Check AI response uses org settings

### 2. Multi-Organization Test

- [ ] Connect Org 1 Instagram account
- [ ] Connect Org 2 Instagram account
- [ ] Send message to Org 1 → Verify routes to Org 1
- [ ] Send message to Org 2 → Verify routes to Org 2
- [ ] Send manual reply from Org 1 → Verify saved as outgoing
- [ ] Send manual reply from Org 2 → Verify saved as outgoing

### 3. Edge Cases

- [ ] Message to unconnected account → Should log and acknowledge
- [ ] Bot disabled for organization → Should save but not respond
- [ ] Invalid Instagram token → Should handle gracefully

## Monitoring & Debugging

Add these logs to track routing:

```typescript
console.log(
  `[WEBHOOK] Received for Instagram Account: ${instagramBusinessAccountId}`,
);
console.log(
  `[WEBHOOK] Routed to Organization: ${instagramSettings?.organizationId}`,
);
console.log(
  `[WEBHOOK] Message Type: ${isEchoMessage ? "OUTGOING" : "INCOMING"}`,
);
console.log(`[WEBHOOK] Bot Enabled: ${instagramSettings?.isBotEnabled}`);
console.log(
  `[WEBHOOK] Will Process: ${!isEchoMessage && instagramSettings?.isBotEnabled}`,
);
```

## Benefits of This Architecture

1. **Scalable** - Supports unlimited organizations
2. **Isolated** - Each org's data is completely separate
3. **Flexible** - Organizations customize their settings
4. **Centralized** - Admin controls AI provider and costs
5. **Maintainable** - Clear separation of concerns

## Common Pitfalls to Avoid

❌ **Don't** use `findFirst` without Instagram Business ID
❌ **Don't** skip echo messages (you'll lose conversation history)
❌ **Don't** process echo messages with AI (infinite loops)
❌ **Don't** mix organization data in cache
❌ **Don't** forget to store Business ID on connection

## Success Metrics

After implementation, you should see:

- ✅ Zero cross-organization message leakage
- ✅ Complete conversation history (in + out)
- ✅ Correct AI responses per organization
- ✅ Each org only sees their own data
- ✅ Admin can manage all orgs from single dashboard

## Final Implementation Priority

1. **🔴 CRITICAL** - Fix webhook organization routing (1 hour)
2. **🟡 IMPORTANT** - Store Instagram Business ID (30 minutes)
3. **🟡 IMPORTANT** - Handle echo messages correctly (30 minutes)
4. **🟢 NICE TO HAVE** - Add monitoring logs (15 minutes)
5. **🟢 NICE TO HAVE** - Add per-org rate limiting (1 hour)

**Total Estimated Time: 3.5 hours**

## Questions Resolved

✅ **Multiple organizations?** Yes, fully supported
✅ **Capture manual messages?** Yes, via echo messages
✅ **Correct attribution?** Yes, by Instagram Business ID
✅ **AI system ready?** Yes, already multi-org capable
✅ **Global admin settings?** Yes, as designed
✅ **Per-org customization?** Yes, via PromptConfig

## Ready for Production

Once you implement the webhook routing fix, your system will correctly:

- Route messages to the right organization
- Maintain complete conversation history
- Apply organization-specific AI prompts
- Scale to support unlimited organizations

The architecture is sound - you just need to fix the one critical bug in webhook routing!
