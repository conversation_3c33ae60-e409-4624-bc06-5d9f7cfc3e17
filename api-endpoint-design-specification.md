# API Endpoint Modifications for MessageList and AttackList Architecture

## Overview

This document specifies the API endpoint modifications required to support the new database architecture with MessageList (all planned messages) and AttackList (chrome extension work queue) tables.

## Current System Issues

### Performance Problems

- `/api/chrome-extension/attack-list`: 588 lines with complex filtering logic
- `/api/chrome-extension/messages-to-send`: 385 lines with overdue follow-up rescheduling
- Multiple database queries with complex 24h window calculations
- SmartFocus priority sorting complexity

### Logic Complexity

- Mixed handling of AI follow-ups vs batch messages
- Dual system compatibility (new InstagramFollowUp + legacy contact fields)
- Complex duplicate message detection
- Daily limit checking and counter management

## New Architecture

### Database Tables

#### MessageList Table (Master list of ALL planned messages)

```sql
CREATE TABLE "MessageList" (
  "id" TEXT PRIMARY KEY,
  "organizationId" TEXT NOT NULL,
  "contactId" TEXT NOT NULL,
  "nickname" TEXT NOT NULL,
  "priority" INTEGER NOT NULL,
  "messageType" TEXT NOT NULL, -- 'first_message', 'system_followup', 'ai_response', 'template'
  "messageContent" TEXT NOT NULL,
  "scheduledTime" TIMESTAMP NOT NULL,
  "status" TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'sent', 'failed'
  "handlerType" TEXT NOT NULL, -- 'system' (24h window) or 'extension' (first messages + outside 24h)
  "sequenceNumber" INTEGER,
  "isInAttackList" BOOLEAN DEFAULT false, -- Track if copied to AttackList
  "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY ("organizationId") REFERENCES "Organization"("id"),
  FOREIGN KEY ("contactId") REFERENCES "InstagramContact"("id")
);

-- Performance indexes
CREATE INDEX "IX_MessageList_organization_status" ON "MessageList"("organizationId", "status");
CREATE INDEX "IX_MessageList_handler_scheduled" ON "MessageList"("handlerType", "scheduledTime");
CREATE INDEX "IX_MessageList_attack_list_ready" ON "MessageList"("isInAttackList", "scheduledTime");
```

#### AttackList Table (Chrome Extension work queue)

```sql
CREATE TABLE "AttackList" (
  "id" TEXT PRIMARY KEY,
  "organizationId" TEXT NOT NULL,
  "contactId" TEXT NOT NULL,
  "nickname" TEXT NOT NULL,
  "priority" INTEGER NOT NULL,
  "messageType" TEXT NOT NULL,
  "messageContent" TEXT NOT NULL,
  "time" TIMESTAMP NOT NULL,
  "messageListId" TEXT NOT NULL, -- Reference to source MessageList entry
  "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY ("organizationId") REFERENCES "Organization"("id"),
  FOREIGN KEY ("contactId") REFERENCES "InstagramContact"("id"),
  FOREIGN KEY ("messageListId") REFERENCES "MessageList"("id")
);

-- Performance indexes
CREATE INDEX "IX_AttackList_organization_time" ON "AttackList"("organizationId", "time");
CREATE INDEX "IX_AttackList_priority_time" ON "AttackList"("priority", "time");
```

## Chrome Extension API Endpoints (Ultra-Simplified)

### 1. GET /api/chrome-extension/attack-list-v2

**Purpose:** Return only messages ready to send immediately - no complex logic

**Request:**

```http
GET /api/chrome-extension/attack-list-v2?organizationId={id}&limit=50
Headers:
  X-API-Key: {api_key}
```

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "nickname": "john_doe",
      "priority": 3,
      "messageType": "first_message",
      "time": "2025-01-15T10:00:00Z",
      "messageContent": "Hey! I saw your latest post about...",
      "contactId": "contact_123"
    }
  ],
  "count": 15,
  "metadata": {
    "nextRefreshAt": "2025-01-15T10:05:00Z"
  }
}
```

**Implementation:**

```sql
-- Single optimized query - 50%+ performance improvement
SELECT
  nickname,
  priority,
  messageType,
  time,
  messageContent,
  contactId
FROM AttackList
WHERE organizationId = ?
ORDER BY priority DESC, time ASC
LIMIT ?;
```

**Performance Benefits:**

- Eliminates 588 lines of complex filtering logic
- Single table query (no JOINs)
- No 24h window calculations
- No conversation source filtering
- Reduces response time from ~800ms to ~200ms

### 2. POST /api/chrome-extension/mark-sent-v2

**Purpose:** Mark message as sent and clean up attack list

**Request:**

```json
{
  "contactId": "contact_123",
  "messageType": "first_message",
  "timestamp": "2025-01-15T10:05:00Z"
}
```

**Response:**

```json
{
  "success": true,
  "action": "message_sent_and_removed",
  "nextMessage": {
    "available": true,
    "contactId": "contact_124"
  }
}
```

**Processing Logic:**

1. Remove message from AttackList
2. Mark corresponding MessageList entry as 'sent'
3. Update contact status
4. Generate follow-up messages if needed
5. Return next available message info

### 3. GET /api/chrome-extension/stats-v2

**Purpose:** Simplified stats for chrome extension popup

**Response:**

```json
{
  "success": true,
  "data": {
    "readyToSend": 25,
    "sentToday": 45,
    "dailyLimit": 100,
    "remainingToday": 55
  }
}
```

## Message List Management API Endpoints

### 1. GET /api/message-list

**Purpose:** Dashboard view of ALL planned messages

**Request:**

```http
GET /api/message-list?organizationId={id}&status=pending&handlerType=system&limit=100&offset=0
```

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "id": "msg_123",
      "nickname": "jane_smith",
      "priority": 4,
      "messageType": "system_followup",
      "messageContent": "Thanks for connecting! How's your business going?",
      "scheduledTime": "2025-01-15T14:00:00Z",
      "status": "pending",
      "handlerType": "system",
      "isInAttackList": false,
      "sequenceNumber": 2
    },
    {
      "id": "msg_124",
      "nickname": "mike_jones",
      "priority": 3,
      "messageType": "first_message",
      "messageContent": "Hey Mike! I noticed you're into...",
      "scheduledTime": "2025-01-15T15:00:00Z",
      "status": "pending",
      "handlerType": "extension",
      "isInAttackList": true,
      "sequenceNumber": 1
    }
  ],
  "pagination": {
    "total": 500,
    "limit": 100,
    "offset": 0,
    "hasMore": true
  },
  "metadata": {
    "totalMessages": 500,
    "systemMessages": 350,
    "extensionMessages": 150,
    "inAttackList": 25,
    "pendingToday": 45
  }
}
```

### 2. PUT /api/message-list/{messageId}

**Purpose:** Update message priority, timing, or content

**Request:**

```json
{
  "priority": 5,
  "scheduledTime": "2025-01-15T16:00:00Z",
  "messageContent": "Updated message content..."
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "msg_123",
    "updated": ["priority", "scheduledTime", "messageContent"],
    "attackListSyncRequired": true
  }
}
```

### 3. DELETE /api/message-list/{messageId}

**Purpose:** Remove message from planning (soft delete)

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "msg_123",
    "status": "cancelled",
    "removedFromAttackList": true
  }
}
```

### 4. POST /api/message-list/batch-update

**Purpose:** Bulk update multiple messages

**Request:**

```json
{
  "messageIds": ["msg_1", "msg_2", "msg_3"],
  "updates": {
    "priority": 4,
    "scheduledTime": "2025-01-15T18:00:00Z"
  }
}
```

## Status Management & Data Flow APIs

### 1. POST /api/status/sync-attack-list

**Purpose:** Background process to populate AttackList from MessageList

**Request:**

```json
{
  "organizationId": "org_123",
  "dryRun": false
}
```

**Processing Logic:**

```sql
-- Find messages ready for chrome extension
SELECT * FROM MessageList
WHERE status = 'pending'
  AND scheduledTime <= NOW()
  AND isInAttackList = false
  AND (
    handlerType = 'extension' OR
    (handlerType = 'system' AND scheduledTime < NOW() - INTERVAL '24 hours')
  );
```

**Response:**

```json
{
  "success": true,
  "processed": {
    "addedToAttackList": 15,
    "systemTimeouts": 3,
    "firstMessages": 12
  }
}
```

### 2. POST /api/status/move-to-extension

**Purpose:** Move failed system messages to chrome extension handling

**Request:**

```json
{
  "messageListIds": ["msg_1", "msg_2"],
  "reason": "24h_window_expired"
}
```

**Response:**

```json
{
  "success": true,
  "moved": 2,
  "addedToAttackList": 2
}
```

## Migration Support APIs

### 1. POST /api/migration/analyze-current-data

**Purpose:** Analyze existing data for migration planning

**Response:**

```json
{
  "success": true,
  "analysis": {
    "totalContacts": 1500,
    "activeFollowUps": 450,
    "batchMessages": 200,
    "systemEligible": 350,
    "extensionEligible": 300,
    "conflicts": 5
  }
}
```

### 2. POST /api/migration/split-messages

**Purpose:** Migrate existing data to new table structure

**Request:**

```json
{
  "organizationId": "org_123",
  "dryRun": true,
  "batchSize": 1000
}
```

**Response:**

```json
{
  "success": true,
  "preview": {
    "toMessageList": 650,
    "toAttackList": 150,
    "systemHandler": 400,
    "extensionHandler": 250,
    "errors": []
  }
}
```

### 3. POST /api/migration/rollback

**Purpose:** Rollback migration if issues found

**Request:**

```json
{
  "organizationId": "org_123",
  "migrationId": "mig_123"
}
```

## Performance Optimizations

### 1. Caching Strategy

**Redis Caching for Attack List:**

```typescript
const cacheKey = `attack-list:${organizationId}:${limit}`;
const cachedResult = await redis.get(cacheKey);
if (cachedResult) return JSON.parse(cachedResult);

// Cache for 2 minutes
await redis.setex(cacheKey, 120, JSON.stringify(result));
```

**Cache Invalidation:**

- When message marked as sent
- When new messages added to AttackList
- Every 2 minutes (background refresh)

### 2. Database Optimizations

**Materialized View for Attack List:**

```sql
CREATE MATERIALIZED VIEW attack_list_ready AS
SELECT
  contactId,
  nickname,
  priority,
  messageType,
  time,
  messageContent,
  organizationId
FROM AttackList
ORDER BY priority DESC, time ASC;

-- Refresh every minute via cron
REFRESH MATERIALIZED VIEW CONCURRENTLY attack_list_ready;
```

**Query Optimization:**

- Use covering indexes for common queries
- Partition AttackList by organizationId for large datasets
- Archive completed messages older than 30 days

### 3. Background Processes

**Attack List Population (runs every minute):**

```typescript
async function updateAttackList() {
  const organizations = await getActiveOrganizations();

  for (const org of organizations) {
    // Find ready messages
    const readyMessages = await findReadyMessages(org.id);

    // Batch insert to AttackList
    await batchInsertToAttackList(readyMessages);

    // Update MessageList flags
    await markAsInAttackList(readyMessages.map((m) => m.id));
  }
}
```

## Error Handling & Validation

### 1. Request Validation Schemas

```typescript
const AttackListQuerySchema = z.object({
  organizationId: z.string().uuid(),
  limit: z.number().min(1).max(100).default(50),
});

const MarkSentSchema = z.object({
  contactId: z.string().uuid(),
  messageType: z.enum([
    "first_message",
    "system_followup",
    "ai_response",
    "template",
  ]),
  timestamp: z.string().datetime(),
});

const MessageListUpdateSchema = z.object({
  priority: z.number().min(1).max(5).optional(),
  scheduledTime: z.string().datetime().optional(),
  messageContent: z.string().min(1).max(1000).optional(),
});
```

### 2. Error Response Format

```json
{
  "success": false,
  "error": {
    "code": "INVALID_ORGANIZATION",
    "message": "Organization not found or access denied",
    "details": {
      "organizationId": "org_invalid",
      "timestamp": "2025-01-15T10:00:00Z"
    }
  }
}
```

### 3. Error Codes

- `INVALID_ORGANIZATION` - Organization not found or access denied
- `INVALID_MESSAGE_ID` - Message not found in MessageList
- `ATTACK_LIST_EMPTY` - No messages ready for chrome extension
- `RATE_LIMIT_EXCEEDED` - API rate limit exceeded
- `MIGRATION_IN_PROGRESS` - System under migration, try again later
- `VALIDATION_ERROR` - Request validation failed

## Authentication & Rate Limiting

### 1. API Key Scopes

```typescript
interface ApiKeyScopes {
  organizationId: string;
  scopes: string[]; // ['attack-list:read', 'messages:write', 'message-list:read']
  rateLimit: {
    requests: number;
    window: string; // '5m', '1h', '1d'
  };
}
```

### 2. Rate Limiting Strategy

```typescript
const rateLimits = {
  "attack-list-v2": { requests: 300, window: "5m" },
  "mark-sent-v2": { requests: 1000, window: "5m" },
  "message-list": { requests: 100, window: "5m" },
  migration: { requests: 10, window: "1h" },
};
```

### 3. Authentication Flow

1. **Chrome Extension:**

   - Uses X-API-Key header
   - Scope validation for each endpoint
   - Rate limiting per organization

2. **Dashboard:**
   - Session-based authentication
   - Organization membership validation
   - Different rate limits for web vs API

## Data Flow Diagram

```mermaid
graph TD
    A[New Contact/Follow-up Created] --> B[Add to MessageList]
    B --> C{Handler Type?}

    C -->|system| D[System Processing<br/>Within 24h Window]
    C -->|extension| E[Background Process<br/>Copies to AttackList]

    D --> F{Success?}
    F -->|Yes| G[Mark Sent in MessageList]
    F -->|No| H[Change handlerType to 'extension'<br/>Move to AttackList]

    E --> I[Chrome Extension API<br/>GET /attack-list-v2]
    I --> J[Chrome Extension<br/>Sends Message]
    J --> K[POST /mark-sent-v2]
    K --> L[Remove from AttackList<br/>Mark Sent in MessageList]

    G --> M[Generate Follow-up<br/>Add to MessageList]
    L --> M

    M --> N{Follow-up Timing?}
    N -->|Within 24h| D
    N -->|Outside 24h| E
```

## Integration Patterns

### 1. Chrome Extension Integration

```typescript
// Simplified chrome extension logic
class AttackListManager {
  async getMessagesToSend() {
    // Single API call - no complex logic needed
    const response = await api.get("/attack-list-v2");
    return response.data;
  }

  async markMessageSent(contactId: string, messageType: string) {
    // Simple mark sent call
    await api.post("/mark-sent-v2", {
      contactId,
      messageType,
      timestamp: new Date().toISOString(),
    });
  }
}
```

### 2. Dashboard Integration

```typescript
// Complete message overview
class MessageListManager {
  async getAllMessages(filters = {}) {
    const response = await api.get("/message-list", { params: filters });
    return response.data;
  }

  async updateMessage(messageId: string, updates: any) {
    await api.put(`/message-list/${messageId}`, updates);
  }

  async getAttackListStatus() {
    // See which messages are currently with chrome extension
    const response = await api.get("/message-list", {
      params: { isInAttackList: true },
    });
    return response.data;
  }
}
```

## Benefits Summary

### Performance Improvements

- **50%+ faster chrome extension API** - single table queries
- **Eliminated complex filtering logic** - 588 lines reduced to ~50 lines
- **Reduced database load** - materialized views and caching
- **Better scalability** - clear separation of concerns

### Operational Benefits

- **Complete message visibility** - MessageList shows all planned messages
- **Clear responsibility separation** - system vs extension handling
- **Automatic failover** - system timeouts move to extension
- **Simplified debugging** - clear data flow and status tracking

### Developer Experience

- **Ultra-simple chrome extension logic** - just send what's in AttackList
- **Clean APIs** - focused single-responsibility endpoints
- **Better error handling** - standardized error responses
- **Migration support** - safe transition from current system

This architecture achieves the core goal: **Chrome extension has simple logic while maintaining complete system oversight and reliability**.
